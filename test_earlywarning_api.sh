#!/bin/bash

# 基于您提供的curl示例进行时间筛选测试
BASE_URL="http://localhost:18000/hmit-admin/backend/earlywarning/page"

echo "=== 预警信息时间筛选功能测试 ==="

# 原始查询（您提供的示例）
echo "1. 原始查询（无时间筛选）:"
curl -s "${BASE_URL}?order=&orderField=&page=1&limit=10&deptName=&businessName=&warningGrade=&warningType=4&_t=1756084618866" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN' \
  -H 'Connection: keep-alive' \
  -H 'Origin: http://localhost:8001' \
  -H 'Referer: http://localhost:8001/' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' | \
  jq -r '.data.total // "请求失败"'

echo ""

# 添加 seTime 筛选
echo "2. 添加 seTime 筛选 (2024-12-25):"
curl -s "${BASE_URL}?order=&orderField=&page=1&limit=10&deptName=&businessName=&warningGrade=&warningType=4&seTime=2024-12-25&_t=1756084618866" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN' \
  -H 'Connection: keep-alive' \
  -H 'Origin: http://localhost:8001' \
  -H 'Referer: http://localhost:8001/' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' | \
  jq -r '.data.total // "请求失败"'

echo ""

# 添加 createTime 筛选
echo "3. 添加 createTime 筛选 (2024-12-25):"
curl -s "${BASE_URL}?order=&orderField=&page=1&limit=10&deptName=&businessName=&warningGrade=&warningType=4&createTime=2024-12-25&_t=1756084618866" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN' \
  -H 'Connection: keep-alive' \
  -H 'Origin: http://localhost:8001' \
  -H 'Referer: http://localhost:8001/' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' | \
  jq -r '.data.total // "请求失败"'

echo ""

# 测试差评提醒的 appTime 筛选
echo "4. 测试差评提醒的 appTime 筛选 (2024-12-25):"
curl -s "${BASE_URL}?order=&orderField=&page=1&limit=10&deptName=&businessName=&warningGrade=&warningType=1&appTime=2024-12-25&_t=1756084618866" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN' \
  -H 'Connection: keep-alive' \
  -H 'Origin: http://localhost:8001' \
  -H 'Referer: http://localhost:8001/' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' | \
  jq -r '.data.total // "请求失败"'

echo ""

# 测试超时接待的 seTime 筛选
echo "5. 测试超时接待的 seTime 筛选 (2024-12-25):"
curl -s "${BASE_URL}?order=&orderField=&page=1&limit=10&deptName=&businessName=&warningGrade=&warningType=2&seTime=2024-12-25&_t=1756084618866" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN' \
  -H 'Connection: keep-alive' \
  -H 'Origin: http://localhost:8001' \
  -H 'Referer: http://localhost:8001/' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' | \
  jq -r '.data.total // "请求失败"'

echo ""

# 显示完整响应示例
echo "6. 完整响应示例 (limit=2):"
curl -s "${BASE_URL}?order=&orderField=&page=1&limit=2&deptName=&businessName=&warningGrade=&warningType=4&_t=1756084618866" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN' \
  -H 'Connection: keep-alive' \
  -H 'Origin: http://localhost:8001' \
  -H 'Referer: http://localhost:8001/' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' | \
  jq '.'

echo ""
echo "=== 测试完成 ==="
echo "注意: 如果显示 '请求失败'，请检查服务是否在 localhost:18000 运行"
