# 单元测试说明

## 新增功能测试

### 1. SysUserServiceImplTest

测试 `SysUserService.getUserWithEnhancedInfo()` 方法：

#### 测试用例：
- `testGetUserWithEnhancedInfo_Success()` - 正常场景测试
  - 验证用户基础信息获取
  - 验证预警统计计算
  - 验证业务统计计算  
  - 验证窗口监控状态更新
  
- `testGetUserWithEnhancedInfo_UserNotFound()` - 用户不存在场景

- `testGetUserWithEnhancedInfo_ExternalApiFailure()` - 外部接口失败场景

- `testGetUserWithEnhancedInfo_NoWindowMonitorData()` - 无监控数据场景

### 2. SysUserControllerTest

测试 `GET /sys/user/windowWorker/{windowId}` 接口：

#### 测试用例：
- `testGetWindowWorker_Success()` - 正常获取窗口人员信息
  - Mock 窗口信息查询
  - Mock 外部API调用
  - Mock 用户信息查询和增强
  - 验证完整流程

- `testGetWindowWorker_WindowNotFound()` - 窗口不存在
- `testGetWindowWorker_WindowNotConfigured()` - 窗口未配置杭州智慧ID
- `testGetWindowWorker_NoPersonnelOnDuty()` - 窗口无人在岗
- `testGetWindowWorker_UserNotFound()` - 用户信息不存在
- `testGetWindowWorker_ExternalApiException()` - 外部接口异常

## 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=SysUserServiceImplTest
mvn test -Dtest=SysUserControllerTest

# 运行特定测试方法
mvn test -Dtest=SysUserServiceImplTest#testGetUserWithEnhancedInfo_Success
mvn test -Dtest=SysUserControllerTest#testGetWindowWorker_Success
```

## 测试覆盖内容

### Service层测试
- ✅ 用户信息增强逻辑
- ✅ 预警统计计算
- ✅ 外部接口调用和异常处理
- ✅ 边界条件和错误场景

### Controller层测试  
- ✅ HTTP接口请求响应
- ✅ 参数验证和错误处理
- ✅ 业务逻辑集成测试
- ✅ 异常场景处理

### Mock对象
- `RestTemplate` - 外部接口调用
- `EarlyWarningDao` - 预警数据查询
- `WindowsService` - 窗口信息服务
- `ExternalApiService` - 外部API服务
- `SysUserService` - 用户服务

## 注意事项

1. 测试使用了 `@MockBean` 来模拟外部依赖
2. 集成测试使用 `TestRestTemplate` 测试HTTP接口
3. 所有异常场景都有对应的测试覆盖
4. Mock数据尽量贴近实际业务场景