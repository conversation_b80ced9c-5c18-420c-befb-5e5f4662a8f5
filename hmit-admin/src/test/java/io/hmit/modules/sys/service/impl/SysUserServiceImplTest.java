package io.hmit.modules.sys.service.impl;

import cn.hutool.core.lang.Assert;
import io.hmit.modules.sys.dto.SysUserDTO;
import io.hmit.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;
/**
 * TODO
 * <AUTHOR> && <EMAIL>
 * @since 2024/12/26 9:14
 */
//@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SysUserServiceImplTest {

    @Autowired
    private SysUserService sysUserService;

    @Test
    public void testJUnitIsRunning() {
        System.out.println("jUnit is installed and running!");
    }


    @Test
    public void bindUserInfo() {
        SysUserDTO sysUserDTO = sysUserService.bindUserInfo("18758806780");
        Assert.notNull(sysUserDTO);
    }
}
