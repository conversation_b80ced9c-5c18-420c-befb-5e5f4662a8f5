package io.hmit.modules.backend.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import org.junit.Test;

import java.util.Date;

/**
 * 工作时间重合计算测试
 */
public class WorkHoursOverlapTest {

    /**
     * 计算时间段与工作时间的重合分钟数
     * 工作时间：8:30-12:00 和 13:30-16:30
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 重合的分钟数
     */
    private static int calculateWorkHoursOverlap(Date startTime, Date endTime) {
        if (startTime == null || endTime == null || !startTime.before(endTime)) {
            return 0;
        }
        
        // 构建当天的工作时间段
        DateTime date = DateUtil.date(startTime);
        DateTime morningStart = DateUtil.parseDateTime(DateUtil.formatDate(date) + " 08:30:00");
        DateTime morningEnd = DateUtil.parseDateTime(DateUtil.formatDate(date) + " 12:00:00");
        DateTime afternoonStart = DateUtil.parseDateTime(DateUtil.formatDate(date) + " 13:30:00");
        DateTime afternoonEnd = DateUtil.parseDateTime(DateUtil.formatDate(date) + " 16:30:00");
        
        int overlapMinutes = 0;
        
        // 计算与上午工作时间的重合
        DateTime startDateTime = DateUtil.date(startTime);
        DateTime endDateTime = DateUtil.date(endTime);
        
        DateTime morningOverlapStart = startDateTime.isAfter(morningStart) ? startDateTime : morningStart;
        DateTime morningOverlapEnd = endDateTime.isBefore(morningEnd) ? endDateTime : morningEnd;
        if (morningOverlapStart.isBefore(morningOverlapEnd)) {
            overlapMinutes += (int) ((morningOverlapEnd.getTime() - morningOverlapStart.getTime()) / (1000 * 60));
        }
        
        // 计算与下午工作时间的重合
        DateTime afternoonOverlapStart = startDateTime.isAfter(afternoonStart) ? startDateTime : afternoonStart;
        DateTime afternoonOverlapEnd = endDateTime.isBefore(afternoonEnd) ? endDateTime : afternoonEnd;
        if (afternoonOverlapStart.isBefore(afternoonOverlapEnd)) {
            overlapMinutes += (int) ((afternoonOverlapEnd.getTime() - afternoonOverlapStart.getTime()) / (1000 * 60));
        }
        
        return overlapMinutes;
    }

    @Test
    public void testWorkHoursOverlap() {
        System.out.println("=== 工作时间重合计算测试 ===");
        
        // 测试用例1: 完全在上午工作时间内 (9:00-10:00)
        Date start1 = DateUtil.parseDateTime("2024-01-01 09:00:00");
        Date end1 = DateUtil.parseDateTime("2024-01-01 10:00:00");
        int overlap1 = calculateWorkHoursOverlap(start1, end1);
        System.out.println("测试1 - 9:00-10:00，预期60分钟，实际: " + overlap1 + "分钟");
        
        // 测试用例2: 跨越上午工作时间 (8:00-13:00)
        Date start2 = DateUtil.parseDateTime("2024-01-01 08:00:00");
        Date end2 = DateUtil.parseDateTime("2024-01-01 13:00:00");
        int overlap2 = calculateWorkHoursOverlap(start2, end2);
        System.out.println("测试2 - 8:00-13:00，预期210分钟(8:30-12:00)，实际: " + overlap2 + "分钟");
        
        // 测试用例3: 完全在下午工作时间内 (14:00-15:00)
        Date start3 = DateUtil.parseDateTime("2024-01-01 14:00:00");
        Date end3 = DateUtil.parseDateTime("2024-01-01 15:00:00");
        int overlap3 = calculateWorkHoursOverlap(start3, end3);
        System.out.println("测试3 - 14:00-15:00，预期60分钟，实际: " + overlap3 + "分钟");
        
        // 测试用例4: 跨越整个工作时间 (8:00-17:00)
        Date start4 = DateUtil.parseDateTime("2024-01-01 08:00:00");
        Date end4 = DateUtil.parseDateTime("2024-01-01 17:00:00");
        int overlap4 = calculateWorkHoursOverlap(start4, end4);
        System.out.println("测试4 - 8:00-17:00，预期390分钟(8:30-12:00 + 13:30-16:30)，实际: " + overlap4 + "分钟");
        
        // 测试用例5: 完全在非工作时间 (12:30-13:00)
        Date start5 = DateUtil.parseDateTime("2024-01-01 12:30:00");
        Date end5 = DateUtil.parseDateTime("2024-01-01 13:00:00");
        int overlap5 = calculateWorkHoursOverlap(start5, end5);
        System.out.println("测试5 - 12:30-13:00，预期0分钟，实际: " + overlap5 + "分钟");
        
        // 测试用例6: 离岗10分钟在上午工作时间 (模拟离岗检测)
        Date now = DateUtil.parseDateTime("2024-01-01 10:00:00");
        Date start6 = DateUtil.offsetMinute(now, -10); // 9:50
        int overlap6 = calculateWorkHoursOverlap(start6, now);
        System.out.println("测试6 - 离岗10分钟(9:50-10:00)，预期10分钟，实际: " + overlap6 + "分钟");
        
        // 测试用例7: 离岗20分钟跨越午休 (11:50-12:10)
        Date now7 = DateUtil.parseDateTime("2024-01-01 12:10:00");
        Date start7 = DateUtil.offsetMinute(now7, -20); // 11:50
        int overlap7 = calculateWorkHoursOverlap(start7, now7);
        System.out.println("测试7 - 离岗20分钟跨越午休(11:50-12:10)，预期10分钟，实际: " + overlap7 + "分钟");
    }
}