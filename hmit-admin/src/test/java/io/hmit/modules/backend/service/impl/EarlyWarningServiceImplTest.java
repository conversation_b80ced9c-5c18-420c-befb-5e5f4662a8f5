package io.hmit.modules.backend.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.modules.backend.entity.EarlyWarningEntity;
import io.hmit.modules.backend.service.EarlyWarningService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * EarlyWarningService 时间筛选功能测试
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class EarlyWarningServiceImplTest {

    @Autowired
    private EarlyWarningService earlyWarningService;

    @Test
    public void testJUnitIsRunning() {
        System.out.println("jUnit is installed and running!");
    }

    /**
     * 测试时间筛选功能
     */
    @Test
    public void testTimeFiltering() {
        log.info("=== 测试时间筛选功能 ===");
        
        // 准备测试参数
        Map<String, Object> params = new HashMap<>();
        params.put("warningType", "4"); // 排队过长
        params.put("page", "1");
        params.put("limit", "10");
        
        // 测试 seTime 筛选
        params.put("seTime", "2024-01-01");

        try {
            var result1 = earlyWarningService.page(params);
            log.info("seTime筛选测试通过，返回记录数: {}", result1.getTotal());
            assertNotNull("seTime筛选结果不应为null", result1);
        } catch (Exception e) {
            log.error("seTime筛选测试失败", e);
            fail("seTime筛选测试失败: " + e.getMessage());
        }

        // 清除 seTime 参数，测试 createTime 筛选
        params.remove("seTime");
        params.put("createTime", "2024-01-01");

        try {
            var result2 = earlyWarningService.page(params);
            log.info("createTime筛选测试通过，返回记录数: {}", result2.getTotal());
            assertNotNull("createTime筛选结果不应为null", result2);
        } catch (Exception e) {
            log.error("createTime筛选测试失败", e);
            fail("createTime筛选测试失败: " + e.getMessage());
        }

        // 清除 createTime 参数，测试 appTime 筛选
        params.remove("createTime");
        params.put("appTime", "2024-01-01");

        try {
            var result3 = earlyWarningService.page(params);
            log.info("appTime筛选测试通过，返回记录数: {}", result3.getTotal());
            assertNotNull("appTime筛选结果不应为null", result3);
        } catch (Exception e) {
            log.error("appTime筛选测试失败", e);
            fail("appTime筛选测试失败: " + e.getMessage());
        }
        
        log.info("=== 时间筛选功能测试完成 ===");
    }

    /**
     * 测试不同数据类型的时间筛选逻辑
     */
    @Test
    public void testTimeFilteringLogic() {
        log.info("=== 测试时间筛选逻辑 ===");

        // 测试参数
        Map<String, Object> params = new HashMap<>();
        params.put("warningType", "2");
        params.put("seTime", "2024-01-01");        // String类型，使用LIKE
        params.put("createTime", "2024-01-01");    // Date类型，使用BETWEEN
        params.put("appTime", "2024-01-01");       // Date类型，使用BETWEEN

        try {
            var result = earlyWarningService.page(params);
            log.info("时间筛选逻辑测试通过，返回记录数: {}", result.getTotal());
            assertNotNull("时间筛选结果不应为null", result);

            // 验证数据类型处理
            log.info("seTime (String): 使用 LIKE '2024-01-01%' 匹配");
            log.info("createTime (Date): 使用 BETWEEN '2024-01-01 00:00:00' AND '2024-01-01 23:59:59' 匹配");
            log.info("appTime (Date): 使用 BETWEEN '2024-01-01 00:00:00' AND '2024-01-01 23:59:59' 匹配");

        } catch (Exception e) {
            log.error("时间筛选逻辑测试失败", e);
            fail("时间筛选逻辑测试失败: " + e.getMessage());
        }

        log.info("=== 时间筛选逻辑测试完成 ===");
    }

    /**
     * 测试无效时间参数处理
     */
    @Test
    public void testInvalidTimeParameters() {
        log.info("=== 测试无效时间参数处理 ===");
        
        Map<String, Object> params = new HashMap<>();
        params.put("warningType", "1");
        params.put("page", "1");
        params.put("limit", "10");
        
        // 测试无效的时间格式
        params.put("seTime", "invalid-date");
        params.put("createTime", "2024-13-45"); // 无效日期
        params.put("appTime", "");
        
        try {
            var result = earlyWarningService.page(params);
            log.info("无效时间参数处理测试通过，返回记录数: {}", result.getTotal());
            assertNotNull("即使时间参数无效，结果也不应为null", result);
        } catch (Exception e) {
            log.error("无效时间参数处理测试失败", e);
            fail("无效时间参数处理测试失败: " + e.getMessage());
        }
        
        log.info("=== 无效时间参数处理测试完成 ===");
    }

    /**
     * 测试组合筛选条件
     */
    @Test
    public void testCombinedFiltering() {
        log.info("=== 测试组合筛选条件 ===");
        
        Map<String, Object> params = new HashMap<>();
        params.put("warningType", "2"); // 超时接待
        params.put("deptName", "余姚市政务服务中心");
        params.put("warningGrade", "1");
        params.put("seTimeStart", "2024-01-01 00:00:00");
        params.put("seTimeEnd", "2024-12-31 23:59:59");
        params.put("createTimeStart", "2024-01-01 00:00:00");
        params.put("createTimeEnd", "2024-12-31 23:59:59");
        params.put("page", "1");
        params.put("limit", "5");
        
        try {
            var result = earlyWarningService.page(params);
            log.info("组合筛选测试通过，返回记录数: {}", result.getTotal());
            assertNotNull("组合筛选结果不应为null", result);
            assertTrue("页面大小应正确", result.getList().size() <= 5);
        } catch (Exception e) {
            log.error("组合筛选测试失败", e);
            fail("组合筛选测试失败: " + e.getMessage());
        }
        
        log.info("=== 组合筛选条件测试完成 ===");
    }
}
