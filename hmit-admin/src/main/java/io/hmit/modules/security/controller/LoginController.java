package io.hmit.modules.security.controller;

import io.hmit.common.exception.ErrorCode;
import io.hmit.common.exception.HmitException;
import io.hmit.common.utils.IpUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.modules.log.entity.SysLogLoginEntity;
import io.hmit.modules.log.enums.LoginOperationEnum;
import io.hmit.modules.log.enums.LoginStatusEnum;
import io.hmit.modules.log.service.SysLogLoginService;
import io.hmit.modules.security.dto.LoginDTO;
import io.hmit.modules.security.password.PasswordUtils;
import io.hmit.modules.security.service.CaptchaService;
import io.hmit.modules.security.service.SysUserTokenService;
import io.hmit.common.utils.sm.SMUtil;
import io.hmit.modules.security.service.UserLockService;
import io.hmit.modules.security.user.SecurityUser;
import io.hmit.modules.security.user.UserDetail;
import io.hmit.modules.sys.dto.SysUserDTO;
import io.hmit.modules.sys.enums.UserStatusEnum;
import io.hmit.modules.sys.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * 登录
 *
 * <AUTHOR>
@RestController
@Api(tags = "登录管理")
public class LoginController {

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserTokenService sysUserTokenService;
    @Autowired
    private CaptchaService captchaService;
    @Autowired
    private SysLogLoginService sysLogLoginService;
    @Autowired
    private UserLockService userLockService;

    @GetMapping("auth")
    @ApiOperation(value = "单点验证")
    public Result<Object> auth() {
        return sysUserTokenService.checkToken(SecurityUser.getUserId());
    }

    @GetMapping("captcha")
    @ApiOperation(value = "验证码", produces = "application/octet-stream")
    @ApiImplicitParam(paramType = "query", dataType = "string", name = "uuid", required = true)
    public void captcha(HttpServletResponse response, String uuid) throws IOException {

        //uuid不能为空
        AssertUtils.isBlank(uuid, ErrorCode.IDENTIFIER_NOT_NULL);

        //生成图片验证码
        BufferedImage image = captchaService.create(uuid);

        response.setHeader("Cache-Control", "no-store, no-cache");
        response.setContentType("image/jpeg");
        ServletOutputStream out = response.getOutputStream();
        ImageIO.write(image, "jpg", out);
        out.close();
    }

    @PostMapping("login")
    @ApiOperation(value = "登录")
    public Result login(HttpServletRequest request, @RequestBody LoginDTO login) {

        //校验数据
        ValidatorUtils.validateEntity(login);

        //验证码是否正确
        boolean flag = captchaService.validate(login.getUuid(), login.getCaptcha());
        if (!flag) {
            return new Result().error(ErrorCode.CAPTCHA_ERROR);
        }

        String username = login.getUsername();
        // 账号登录次数校验锁定
        Map<String, String> userMap = userLockService.checkUserLock(request, username);

        //用户信息
        SysUserDTO user = sysUserService.getByUsername(username);

        /* SM 登录加密 */
        // 对密码解密
        try {
            login.setPassword(SMUtil.SMDecryptSM2AndSM4(login.getKey(), login.getPassword()));
        }catch(Exception e){
            throw new HmitException(ErrorCode.ENCRYPT_OR_DECRYPT_ERROR);
        }
        /* ! SM 登录加密 */

        SysLogLoginEntity log = new SysLogLoginEntity();
        log.setOperation(LoginOperationEnum.LOGIN.value());
        log.setCreateDate(new Date());
        log.setIp(IpUtils.getIpAddr(request));
        log.setUserAgent(request.getHeader(HttpHeaders.USER_AGENT));
        log.setIp(IpUtils.getIpAddr(request));

        //用户不存在
        if (user == null) {
            log.setStatus(LoginStatusEnum.FAIL.value());
            log.setCreatorName(username);
            sysLogLoginService.save(log);

            // 增加用户名锁定
            userLockService.addUserLockTimes(request, username, userMap);

            throw new HmitException(ErrorCode.ACCOUNT_PASSWORD_ERROR);
        }

        //密码错误
        if (!PasswordUtils.matches(login.getPassword(), user.getPassword())) {
            log.setStatus(LoginStatusEnum.FAIL.value());
            log.setCreator(user.getId());
            log.setCreatorName(user.getUsername());
            sysLogLoginService.save(log);

            // 增加用户名锁定
            userLockService.addUserLockTimes(request, username, userMap);

            throw new HmitException(ErrorCode.ACCOUNT_PASSWORD_ERROR);
        }

        //账号停用
        if (user.getStatus() == UserStatusEnum.DISABLE.value()) {
            log.setStatus(LoginStatusEnum.LOCK.value());
            log.setCreator(user.getId());
            log.setCreatorName(user.getUsername());
            sysLogLoginService.save(log);

            throw new HmitException(ErrorCode.ACCOUNT_DISABLE);
        }

        //登录成功
        log.setStatus(LoginStatusEnum.SUCCESS.value());
        log.setCreator(user.getId());
        log.setCreatorName(user.getUsername());
        sysLogLoginService.save(log);
        userLockService.removeUserLockInfo(request, username);
        return sysUserTokenService.createToken(user.getId());
    }

    @PostMapping("loginWorkNo")
    @ApiOperation(value = "工号登录")
    public Result loginWorkNo(HttpServletRequest request, @RequestBody Map<String,Object> login) {

        //验证码是否正确
        if (login.get("workNo") == null) {
            throw new HmitException("工号不能为空");
        }
        String workNo = String.valueOf(login.get("workNo").toString());
        //用户信息
        SysUserDTO user = sysUserService.getByWorkNo(workNo);
        /* ! SM 登录加密 */

        SysLogLoginEntity log = new SysLogLoginEntity();
        log.setOperation(LoginOperationEnum.LOGIN.value());
        log.setCreateDate(new Date());
        log.setIp(IpUtils.getIpAddr(request));
        log.setUserAgent(request.getHeader(HttpHeaders.USER_AGENT));
        log.setIp(IpUtils.getIpAddr(request));

        //用户不存在
        if (user == null) {
            log.setStatus(LoginStatusEnum.FAIL.value());
            log.setCreatorName(workNo);
            sysLogLoginService.save(log);
            throw new HmitException(ErrorCode.ACCOUNT_PASSWORD_ERROR);
        }
        //账号停用
        if (user.getStatus() == UserStatusEnum.DISABLE.value()) {
            log.setStatus(LoginStatusEnum.LOCK.value());
            log.setCreator(user.getId());
            log.setCreatorName(user.getUsername());
            sysLogLoginService.save(log);
            throw new HmitException(ErrorCode.ACCOUNT_DISABLE);
        }

        //登录成功
        log.setStatus(LoginStatusEnum.SUCCESS.value());
        log.setCreator(user.getId());
        log.setCreatorName(user.getUsername());
        sysLogLoginService.save(log);
        return sysUserTokenService.createToken(user.getId());
    }

    @PostMapping("logout")
    @ApiOperation(value = "退出")
    public Result logout(HttpServletRequest request) {

        UserDetail user = SecurityUser.getUser();

        //退出
        sysUserTokenService.logout(user.getId());

        //用户信息
        SysLogLoginEntity log = new SysLogLoginEntity();
        log.setOperation(LoginOperationEnum.LOGOUT.value());
        log.setIp(IpUtils.getIpAddr(request));
        log.setUserAgent(request.getHeader(HttpHeaders.USER_AGENT));
        log.setIp(IpUtils.getIpAddr(request));
        log.setStatus(LoginStatusEnum.SUCCESS.value());
        log.setCreator(user.getId());
        log.setCreatorName(user.getUsername());
        log.setCreateDate(new Date());
        sysLogLoginService.save(log);

        return new Result();
    }

}