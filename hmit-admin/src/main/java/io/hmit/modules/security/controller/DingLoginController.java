package io.hmit.modules.security.controller;

import io.hmit.common.constant.Constant;
import io.hmit.common.exception.HmitException;
import io.hmit.common.utils.IpUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.utils.sm.sm4.SM4Utils;
import io.hmit.model.DingResponse;
import io.hmit.modules.log.entity.SysLogErrorEntity;
import io.hmit.modules.log.entity.SysLogLoginEntity;
import io.hmit.modules.log.enums.LoginOperationEnum;
import io.hmit.modules.log.enums.LoginStatusEnum;
import io.hmit.modules.log.service.SysLogErrorService;
import io.hmit.modules.log.service.SysLogLoginService;
import io.hmit.modules.security.service.SysUserTokenService;
import io.hmit.modules.sys.dto.SysUserDTO;
import io.hmit.modules.sys.service.SysUserService;
import io.hmit.service.DingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <h1>政务钉钉免登</h1>
 *
 * <AUTHOR> && <EMAIL>
 * @since 2024/12/24 16:45
 */

@Slf4j
@RestController
@Api(tags = "政务钉钉免登")
@RequestMapping("ding")
public class DingLoginController {

    /**
     * 12小时后过期
     */
    private final static int EXPIRE = 3600 * 12;

    private final DingService dingService;

    private final SysUserService sysUserService;

    private final SysUserTokenService sysUserTokenService;

    private final SysLogLoginService sysLogLoginService;

    private final SysLogErrorService sysLogErrorService;

    public DingLoginController(DingService dingService, SysUserService sysUserService, SysUserTokenService sysUserTokenService,
                               SysLogLoginService sysLogLoginService, SysLogErrorService sysLogErrorService) {
        this.dingService = dingService;
        this.sysUserService = sysUserService;
        this.sysUserTokenService = sysUserTokenService;
        this.sysLogLoginService = sysLogLoginService;
        this.sysLogErrorService = sysLogErrorService;
    }

    @GetMapping("/getAccessToken")
    @ApiOperation("免登")
    public Result<Object> getAccessToken(@RequestParam("authCode") String authCode,
                                         HttpServletRequest request) {
        log.info("【免登】authCode={}", authCode);
        DingResponse dingResponse = dingService.getUserInfo(authCode);
        log.info("【免登】dingResponse={}", dingResponse);
        if (!dingResponse.isSuccess()) {
            throw new HmitException("【请联系管理员】" + dingResponse.getErrorMsg());
        } else if(!dingResponse.getContent().isSuccess()) {
            throw new HmitException("【请联系管理员】" + dingResponse.getContent().getResponseMessage());
        }
        // 真实姓名
        String lastName = dingResponse.getContent().getData().get("lastName").toString();
        // 浙政钉人员编号
        String employeeCode = dingResponse.getContent().getData().get("employeeCode").toString();
        // 浙政钉人员id
        String accountId = dingResponse.getContent().getData().get("accountId").toString();
        // 对 accountId 加密
        accountId = SM4Utils.encryptData_CBC(accountId);
        // 租户ID
        String tenantId = dingResponse.getContent().getData().get("tenantId").toString();
        // 此处可考虑对 tenantID 进行校验，控制同一租户下用户登录
        Map<String,Object> result = new HashMap<>();
        SysUserDTO sysUserDTO = sysUserService.selectByAccountId(accountId);

        if (null != sysUserDTO) {
            if (!sysUserDTO.getRealName().equals(lastName)) {
                // 异常日志
                SysLogErrorEntity log = new SysLogErrorEntity();
                log.setIp(IpUtils.getIpAddr(request));
                log.setUserAgent(request.getHeader(HttpHeaders.USER_AGENT));
                log.setRequestUri(request.getRequestURI());
                log.setRequestMethod(request.getMethod());
                log.setErrorInfo("免登用户信息和浙政钉信息不一致，但 accountId 与库中用户一致。");
                sysLogErrorService.save(log);
            }
            // 登录日志
            SysLogLoginEntity log = new SysLogLoginEntity();
            log.setOperation(LoginOperationEnum.LOGIN.value());
            log.setCreateDate(new Date());
            log.setIp(IpUtils.getIpAddr(request));
            log.setUserAgent(request.getHeader(HttpHeaders.USER_AGENT));
            log.setIp(IpUtils.getIpAddr(request));
            // 登录成功
            log.setStatus(LoginStatusEnum.SUCCESS.value());
            log.setCreator(sysUserDTO.getId());
            log.setCreatorName(sysUserDTO.getUsername());
            sysLogLoginService.save(log);
            // ！登录日志

            // 生成token
            Date now = new Date();
            Date expireTime = new Date(now.getTime() + EXPIRE * 1000);
            String token = sysUserTokenService.generatorToken(sysUserDTO.getId(), now, expireTime);
            // ！生成token

            result.put(Constant.TOKEN_HEADER, token);
            result.put("expireTime", expireTime);
//            result.put("jsApi", dingService.getJSAPIToken().getContent().getData().toString());
        } else {
            throw new HmitException("账号不存在，请联系管理员添加或绑定");
        }
        try{
            log.info("tenantId = " + tenantId);
            DingResponse scopesInfo = dingService.getScopesV2(tenantId);
            log.info("scopesInfo = " + scopesInfo);
            String results = scopesInfo.getResult();
            log.info("result = " + result);
            // 定义正则表达式
            String patternString = "GO_[a-zA-Z0-9]+";

            Pattern pattern = Pattern.compile(patternString);
            Matcher matcher = pattern.matcher(results);
            String organizationCode = "";
            if (matcher.find()) {
                organizationCode = matcher.group();
            }

            log.info("organizationCode = " + organizationCode);
            DingResponse organizationInfo = dingService.getOrganizationByCode(organizationCode, tenantId);
            log.info("organizationInfo = " + organizationInfo);
        }catch (Exception e){
            log.error("获取组织信息失败: {}", e.getLocalizedMessage());
        }
        return new Result<>().ok(result);
    }

}
