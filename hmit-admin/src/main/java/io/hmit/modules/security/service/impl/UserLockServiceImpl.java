package io.hmit.modules.security.service.impl;

import io.hmit.common.exception.ErrorCode;
import io.hmit.common.exception.HmitException;
import io.hmit.modules.security.service.UserLockService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * <h1>用户登录失败锁定</h1>
 *
 * <AUTHOR> && <EMAIL>
 * @since 2022/9/29 11:18
 */
@Service
public class UserLockServiceImpl implements UserLockService {

    /**
     * 是否开启登录错误锁定
     * true 开启 | false 关闭
     */
    @Value("${hmit.login.lock: true}")
    private boolean lock;

    /*
     * 锁定时间
     * 如果锁定时间需要大于 30 分钟的情况下，
     * 需要修改 servlet 的 session 超时时间
     */
    public static final long INTERVAL_TIME = 30;
    /*
     * 最大错误次数
     */
    public static final int MAX_TRY_TIME = 5;

    /**
     * <h2>校验用户锁定情况</h2>
     *
     * @param username 用户名
     */
    @Override
    public Map<String, String> checkUserLock(HttpServletRequest request, String username) {
        Object o = request.getSession().getServletContext().getAttribute(username);
        if (null == o || !lock) {
            return new HashMap<String, String>();
        }
        Map<String, String> userMap = (HashMap<String, String>) o;
        int tryNum = Integer.parseInt(userMap.get("tryNum"));
        long interval = INTERVAL_TIME - (System.currentTimeMillis() - Long.parseLong(userMap.get("st"))) / 60000;
        if (tryNum >= MAX_TRY_TIME && interval >= 0) {
            throw new HmitException("尝试错误次数过多，请在" + interval + "分钟后重试");
        }
        return userMap;
    }

    /**
     * <h2>增加用户密码错误次数</h2>
     *
     * @param username 用户名
     */
    @Override
    public void addUserLockTimes(HttpServletRequest request, String username, Map<String, String> userMap) {
        if (!lock) {
            return;
        }
        HttpSession session = request.getSession();
        int tryNum = 0;
        long st = System.currentTimeMillis();
        if (userMap.isEmpty()) {
            Object o = session.getServletContext().getAttribute(username);
            if (null != o) {
                userMap = (HashMap<String, String>) o;
                st = Long.parseLong(userMap.get("st"));
                tryNum = Integer.parseInt(userMap.get("tryNum"));
            }
        } else {
            st = Long.parseLong(userMap.get("st"));
            tryNum = Integer.parseInt(userMap.get("tryNum"));
        }
        // 当错误次数累计至 最大错误 次时，对用户抛出锁定的异常提醒
        tryNum = tryNum + 1;
        long interval = INTERVAL_TIME - (System.currentTimeMillis() - st) / 60000;
        if (tryNum >= MAX_TRY_TIME && interval >= 0) {
            userMap.put("tryNum", String.valueOf(tryNum));
            userMap.put("st", String.valueOf(st));
            session.getServletContext().setAttribute(username, userMap);
            throw new HmitException("尝试错误次数过多，请在" + interval + "分钟后重试");
        } else if (interval < 0) {
            // 超过锁定时间需要对错误次数进行重置
            tryNum = 1;
        }
        // 存储用户的错误次数
        userMap.put("tryNum", String.valueOf(tryNum));
        userMap.put("st", String.valueOf(System.currentTimeMillis()));
        session.getServletContext().setAttribute(username, userMap);
    }

    /**
     * <h2>清除登录失败信息</h2>
     */
    @Override
    public void removeUserLockInfo(HttpServletRequest request, String username) {
        request.getSession().getServletContext().removeAttribute(username);
    }


}
