package io.hmit.modules.security.service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <h1>用户登录失败锁定</h1>
 *
 * <AUTHOR> && <EMAIL>
 * @since 2022/9/29 11:14
 */
public interface UserLockService {

    /**
     * <h2>校验用户锁定情况</h2>
     * @param username 用户名
     */
    Map<String, String> checkUserLock(HttpServletRequest request, String username);

    /**
     * <h2>增加用户密码错误次数</h2>
     * @param username 用户名
     */
    void addUserLockTimes(HttpServletRequest request, String username, Map<String, String> userMap);

    /**
     * <h2>清除登录失败信息</h2>
     */
    void removeUserLockInfo(HttpServletRequest request, String username);

}
