package io.hmit.modules.security.oauth2;

import org.apache.shiro.authc.AuthenticationToken;

/**
 * token
 *
 * <AUTHOR>
public class Oauth2Token implements AuthenticationToken {

    private String token;

    public Oauth2Token(String token) {
        this.token = token;
    }

    @Override
    public String getPrincipal() {
        // 用户名
        return token;
    }

    @Override
    public Object getCredentials() {
        // 密码
        return token;
    }
}
