package io.hmit.modules.security.service.impl;

import io.hmit.common.constant.Constant;
import io.hmit.common.service.impl.BaseServiceImpl;
import io.hmit.common.utils.Result;
import io.hmit.common.utils.sm.sm4.SM4Utils;
import io.hmit.modules.security.dao.SysUserTokenDao;
import io.hmit.modules.security.entity.SysUserTokenEntity;
import io.hmit.modules.security.oauth2.TokenGenerator;
import io.hmit.modules.security.service.ShiroService;
import io.hmit.modules.security.service.SysUserTokenService;
import io.hmit.modules.sys.entity.SysUserEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class SysUserTokenServiceImpl extends BaseServiceImpl<SysUserTokenDao, SysUserTokenEntity> implements SysUserTokenService {

    private final ShiroService shiroService;

    /**
     * 12小时后过期
     */
    private final static int EXPIRE = 3600 * 12;

    public SysUserTokenServiceImpl(ShiroService shiroService) {
        this.shiroService = shiroService;
    }

    @Override
    public Result createToken(Long userId) {

        //用户token
        String token;

        //当前时间
        Date now = new Date();

        //过期时间
        Date expireTime = new Date(now.getTime() + EXPIRE * 1000);

        //判断是否生成过token
        SysUserTokenEntity tokenEntity = baseDao.getByUserId(userId);
        if (tokenEntity == null) {
            //生成一个token
            token = TokenGenerator.generateValue();

            tokenEntity = new SysUserTokenEntity();
            tokenEntity.setUserId(userId);
            tokenEntity.setToken(token);
            tokenEntity.setUpdateDate(now);
            tokenEntity.setExpireDate(expireTime);

            //保存token
            this.insert(tokenEntity);
        } else {
            //判断token是否过期
            if (tokenEntity.getExpireDate().getTime() < System.currentTimeMillis()) {
                //token过期，重新生成token
                token = TokenGenerator.generateValue();
            } else {
                token = tokenEntity.getToken();
            }

            tokenEntity.setToken(token);
            tokenEntity.setUpdateDate(now);
            tokenEntity.setExpireDate(expireTime);

            //更新token
            this.updateById(tokenEntity);
        }

        Map<String, Object> map = new HashMap<>(2);
        map.put(Constant.TOKEN_HEADER, token);
        map.put("expire", EXPIRE);
        return new Result().ok(map);
    }

    @Override
    public void logout(Long userId) {

        //生成一个token
        String token = TokenGenerator.generateValue();

        //修改token
        baseDao.updateToken(userId, token);
    }

    @Override
    public Result<Object> checkToken(Long userId) {
        SysUserEntity userEntity = shiroService.getUser(userId);
        if (ObjectUtils.isEmpty(userEntity) || ObjectUtils.isEmpty(userEntity.getAccountId())) {
            return new Result<>().error("未找到账号或未绑定浙政钉");
        }
        userEntity.setPassword(null);
        userEntity.setSuperAdmin(null);
        userEntity.setMobile(null);
        userEntity.setAccountId(SM4Utils.decryptData_CBC(userEntity.getAccountId()));
        return new Result<>().ok(userEntity);
    }

    /**
     * <h2>生成 token</h2>
     */
    @Override
    public String generatorToken(Long userId, Date now, Date expireTime) {
        String token;
        //判断是否生成过token
        SysUserTokenEntity tokenEntity = baseDao.getByUserId(userId);
        if (tokenEntity == null) {
            //生成一个token
            token = TokenGenerator.generateValue();

            tokenEntity = new SysUserTokenEntity();
            tokenEntity.setUserId(userId);
            tokenEntity.setToken(token);
            tokenEntity.setUpdateDate(now);
            tokenEntity.setExpireDate(expireTime);

            //保存token
            this.insert(tokenEntity);
        } else {
            //判断token是否过期
            if (tokenEntity.getExpireDate().getTime() < System.currentTimeMillis()) {
                //token过期，重新生成token
                token = TokenGenerator.generateValue();
            } else {
                token = tokenEntity.getToken();
            }

            tokenEntity.setToken(token);
            tokenEntity.setUpdateDate(now);
            tokenEntity.setExpireDate(expireTime);

            //更新token
            this.updateById(tokenEntity);
        }
        return token;
    }
}