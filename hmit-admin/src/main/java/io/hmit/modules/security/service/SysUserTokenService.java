package io.hmit.modules.security.service;

import io.hmit.common.service.BaseService;
import io.hmit.common.utils.Result;
import io.hmit.modules.security.entity.SysUserTokenEntity;

import java.util.Date;

/**
 * 用户Token
 *
 * <AUTHOR>
public interface SysUserTokenService extends BaseService<SysUserTokenEntity> {

    /**
     * 生成token
     *
     * @param userId 用户ID
     */
    Result createToken(Long userId);

    /**
     * 退出，修改token值
     *
     * @param userId 用户ID
     */
    void logout(Long userId);

    Result<Object> checkToken(Long userId);

    /**
     * <h2>生成 token</h2>
     */
    String generatorToken(Long userId, Date now, Date expireTime);

}