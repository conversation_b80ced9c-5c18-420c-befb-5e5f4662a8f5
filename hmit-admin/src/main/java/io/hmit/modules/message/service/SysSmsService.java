package io.hmit.modules.message.service;

import io.hmit.common.page.PageData;
import io.hmit.common.service.BaseService;
import io.hmit.modules.message.dto.SysSmsDTO;
import io.hmit.modules.message.entity.SysSmsEntity;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 短信
 *
 * <AUTHOR>
public interface SysSmsService extends BaseService<SysSmsEntity> {

    PageData<SysSmsDTO> page(Map<String, Object> params);

    /**
     * 发送短信
     *
     * @param mobile 手机号
     * @param params 短信参数
     */
    void send(String mobile, String params);

    /**
     * 保存短信发送记录
     *
     * @param platform 平台
     * @param mobile   手机号
     * @param params   短信参数
     * @param status   发送状态
     */
    void save(Integer platform, String mobile, LinkedHashMap<String, String> params, Integer status);
}

