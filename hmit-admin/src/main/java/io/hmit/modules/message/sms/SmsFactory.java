package io.hmit.modules.message.sms;

import io.hmit.common.constant.Constant;
import io.hmit.common.utils.SpringContextUtils;
import io.hmit.modules.sys.service.SysParamsService;

/**
 * 短信Factory
 *
 * <AUTHOR>
public class SmsFactory {

    private static SysParamsService sysParamsService;

    static {
        SmsFactory.sysParamsService = SpringContextUtils.getBean(SysParamsService.class);
    }

    public static AbstractSmsService build() {

        //获取短信配置信息
        SmsConfig config = sysParamsService.getValueObject(Constant.SMS_CONFIG_KEY, SmsConfig.class);

        if (config.getPlatform() == Constant.SmsService.ALIYUN.getValue()) {
            return new AliyunSmsService(config);
        } else if (config.getPlatform() == Constant.SmsService.QCLOUD.getValue()) {
            return new QcloudSmsService(config);
        }

        return null;
    }
}
