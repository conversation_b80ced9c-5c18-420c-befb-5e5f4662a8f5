package io.hmit.modules.datasource.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <h1>SQL日志 DTO</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-02
 */
@Data
@ApiModel(value = "SQL日志")
public class SysSqlLogDTO implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "编号")
	private Long id;

	@ApiModelProperty(value = "日志名称")
	private String sqlLogName;

	@ApiModelProperty(value = "SQL内容")
	private String sqlLogContent;

	@ApiModelProperty(value = "SQL结果")
	private String sqlLogResult;

	@ApiModelProperty(value = "执行IP")
	private String sqlLogIp;

	@ApiModelProperty(value = "执行时间")
	private Long sqlExecuteTime;

	@ApiModelProperty(value = "状态")
	private String status;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "创建人")
	private Long creator;

	@ApiModelProperty(value = "创建时间")
	private Date createDate;


}