CREATE TABLE `sys_sql_log`  (
    `id` bigint(20) NOT NULL COMMENT '编号',
    `sql_log_name` varchar(255) NOT NULL COMMENT '日志名称',
    `sql_log_content` text NULL DEFAULT NULL COMMENT 'SQL内容',
    `sql_log_result` text NULL DEFAULT NULL COMMENT 'SQL结果',
    `sql_log_ip` varchar(255) NULL DEFAULT NULL COMMENT '执行IP',
    `sql_execute_time` bigint(20) NULL DEFAULT NULL COMMENT '执行时间',
    `status` varchar(255) NULL DEFAULT NULL COMMENT '状态',
    `remark` varchar(255) NULL DEFAULT NULL DEFAULT NULL COMMENT '备注',
    `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
    `create_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'SQL日志';
