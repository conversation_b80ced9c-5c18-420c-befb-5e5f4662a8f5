package io.hmit.modules.datasource.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.datasource.dto.SysSqlLogDTO;
import io.hmit.modules.datasource.service.SysSqlLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * <h1>SQL日志</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-02
 */
@RestController
@RequestMapping("datasource/syssqllog")
@Api(tags="SQL日志")
public class SysSqlLogController {

    private final SysSqlLogService sysSqlLogService;

    public SysSqlLogController(SysSqlLogService sysSqlLogService) {
        this.sysSqlLogService = sysSqlLogService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("datasource:syssqllog:page")
    public Result<PageData<SysSqlLogDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<SysSqlLogDTO> page = sysSqlLogService.page(params);

        return new Result<PageData<SysSqlLogDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("datasource:syssqllog:info")
    public Result<SysSqlLogDTO> get(@PathVariable("id") Long id){
        SysSqlLogDTO data = sysSqlLogService.get(id);

        return new Result<SysSqlLogDTO>().ok(data);
    }

/*
    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("datasource:syssqllog:save")
    public Result<Object> save(@RequestBody SysSqlLogDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        sysSqlLogService.save(dto);

        return new Result();
    }
*/

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("datasource:syssqllog:update")
    public Result<Object> update(@RequestBody SysSqlLogDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        sysSqlLogService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("datasource:syssqllog:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        sysSqlLogService.delete(ids);

        return new Result<>();
    }

}