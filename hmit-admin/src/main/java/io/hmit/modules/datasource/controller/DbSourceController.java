package io.hmit.modules.datasource.controller;

import com.alibaba.druid.util.JdbcConstants;
import com.alibaba.fastjson.JSON;
import io.hmit.common.utils.Result;
import io.hmit.modules.datasource.db.SqlDruidParser;
import io.hmit.modules.datasource.dto.SysSqlLogDTO;
import io.hmit.modules.datasource.service.SysSqlLogService;
import io.hmit.modules.datasource.util.JdbcUtils;
import io.hmit.modules.security.user.SecurityUser;
import io.hmit.modules.security.user.UserDetail;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <h1></h1>
 * <AUTHOR> && <EMAIL>
 * @since 2022/8/2 16:11
 */
@Slf4j
@RestController
@RequestMapping("/database")
//@Api(tags="SQL执行器")
public class DbSourceController {

    private final JdbcUtils jdbcUtils;

    private final SysSqlLogService sysSqlLogService;

    public DbSourceController(SysSqlLogService sysSqlLogService, JdbcUtils jdbcUtils) {
        this.sysSqlLogService = sysSqlLogService;
        this.jdbcUtils = jdbcUtils;
    }

    /**
     * 执行器
     */
    @ResponseBody
//    @ApiOperation("执行器")
    @PostMapping("/executeSql")
    @RequiresPermissions("datasource:syssqllog:save")
    public Result<Object> executeSql(String sql) {
        UserDetail user = SecurityUser.getUser();
        SysSqlLogDTO sqlLog = new SysSqlLogDTO();
        Map<String, Object> result = new HashMap<>();
        long st = System.currentTimeMillis();

        Map<String, Object> sqlParser = SqlDruidParser.sqlParser(JdbcConstants.MYSQL, sql);
        if (sqlParser.get("executeType") == null) {
            log.error("SQL解析异常：[{}]，执行人：[{}]{}", sql, user.getId(), user.getRealName());
            insertSqlLogInfo(sql, user.getId(), st, null, sqlLog, "ERROR", "SQL解析异常");
            result.put("executeTime", sqlLog.getSqlExecuteTime());
            result.put("result", "SQL解析异常");
            result.put("code", 0);
            return new Result<>().ok(result);
        }
        if (!sqlParser.get("executeType").equals("SELECT")
                &&!sqlParser.get("executeType").equals("UPDATE")
                &&!sqlParser.get("executeType").equals("INSERT")) {
            log.error("非法SQL操作：[{}]，执行人：[{}]{}", sql, user.getId(), user.getRealName());
            insertSqlLogInfo(sql, user.getId(), st, null, sqlLog, "ERROR", "非法SQL操作");
            result.put("executeTime", sqlLog.getSqlExecuteTime());
            result.put("result", "非法SQL操作");
            result.put("code", 0);
            return new Result<>().ok(result);
        }
        result.put("sqlExecuteType", sqlParser.get("executeType"));
        List<String> sqlList = (List<String>) sqlParser.get("executeSql");
        List<Map<String, Object>> dataList = new ArrayList<>();
        try {
            if (sqlParser.get("executeType").equals("SELECT")) {
                for (String executeSql : sqlList
                ) {
                    Map<String, Object> resultData = jdbcUtils.findMoreResult(executeSql, new ArrayList<>());
                    dataList.add(resultData);
                }
            } else {
                for (String executeSql : sqlList
                ) {
                    Map<String, Object> resultData = jdbcUtils.updateByPreparedStatement(executeSql, new ArrayList<>());
                    dataList.add(resultData);
                }
            }
        }catch(SQLException e){
            e.printStackTrace();
        }
        log.warn("执行SQL成功：[{}]，执行人：[{}]{}", sql, user.getId(), user.getRealName());

        insertSqlLogInfo(sql, user.getId(), st, dataList, sqlLog, "OK", null);
        result.put("executeTime", sqlLog.getSqlExecuteTime());
        result.put("result", dataList);
        result.put("code", 1);
        return new Result<>().ok(result);
    }

    private void insertSqlLogInfo(String sql, Long userId, long st,List<Map<String, Object>> dataList,
                                  SysSqlLogDTO sqlLog, String status, String remark) {
        sqlLog.setSqlExecuteTime(System.currentTimeMillis() - st);
        String result = null == dataList ? null : JSON.toJSONString(dataList);
        if (result != null && result.length() > 65535) {
            result = "超出日志存储长度";
        }
        sqlLog.setSqlLogName("SQL执行");
        sqlLog.setSqlLogContent(sql);
        sqlLog.setSqlLogResult(result);
        sqlLog.setStatus(status);
        sqlLog.setCreator(userId);
        sqlLog.setRemark(remark);
        sysSqlLogService.save(sqlLog);
    }
}
