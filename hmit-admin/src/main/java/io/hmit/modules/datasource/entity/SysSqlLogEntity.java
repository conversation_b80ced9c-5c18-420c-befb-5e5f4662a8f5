package io.hmit.modules.datasource.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.hmit.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <h1>SQL日志 实体类</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-02
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("sys_sql_log")
public class SysSqlLogEntity extends BaseEntity {
	private static final long serialVersionUID = 1L;

    /**
     * 日志名称
     */
	private String sqlLogName;
    /**
     * SQL内容
     */
	private String sqlLogContent;
    /**
     * SQL结果
     */
	private String sqlLogResult;
    /**
     * 执行IP
     */
	private String sqlLogIp;
    /**
     * 执行时间
     */
	private Long sqlExecuteTime;
    /**
     * 状态
     */
	private String status;
    /**
     * 备注
     */
	private String remark;
}