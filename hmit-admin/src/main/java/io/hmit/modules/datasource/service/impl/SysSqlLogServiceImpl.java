package io.hmit.modules.datasource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.datasource.dao.SysSqlLogDao;
import io.hmit.modules.datasource.dto.SysSqlLogDTO;
import io.hmit.modules.datasource.entity.SysSqlLogEntity;
import io.hmit.modules.datasource.service.SysSqlLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>SQL日志 Service Impl</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-02
 */
@Slf4j
@Service
public class SysSqlLogServiceImpl extends CrudServiceImpl<SysSqlLogDao, SysSqlLogEntity, SysSqlLogDTO> implements SysSqlLogService {

    @Override
    public QueryWrapper<SysSqlLogEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<SysSqlLogEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id)
                .orderByDesc("id");

        return wrapper;
    }


}