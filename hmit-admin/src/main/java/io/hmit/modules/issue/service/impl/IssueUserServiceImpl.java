package io.hmit.modules.issue.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.issue.dao.IssueUserDao;
import io.hmit.modules.issue.dto.HandledRemarkDTO;
import io.hmit.modules.issue.dto.IssueUserDTO;
import io.hmit.modules.issue.entity.IssueUserEntity;
import io.hmit.modules.issue.service.IssueUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <h1>工单用户(处理人)关系 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-01
 */
@Slf4j
@Service
public class IssueUserServiceImpl extends CrudServiceImpl<IssueUserDao, IssueUserEntity, IssueUserDTO> implements IssueUserService {

    private final IssueUserDao issueUserDao;

    public IssueUserServiceImpl(IssueUserDao issueUserDao) {
        super();
        this.issueUserDao = issueUserDao;
    }

    @Override
    public QueryWrapper<IssueUserEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<IssueUserEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public List<IssueUserEntity> findByIssueIdAndUserId(Long issueId, Long userId, int status) {
        QueryWrapper<IssueUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status)
                .eq(issueId>0, "issue_id", issueId)
                .eq(userId>0, "user_id", userId);
        return baseDao.selectList(queryWrapper);
    }

    @Override
    public void deleteByIssueIds(Long[] ids) {
        issueUserDao.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public List<HandledRemarkDTO> handledRemarkList(Long issueId) {
        List<IssueUserEntity> list = findByIssueIdAndUserId(issueId, 0L, 2);
        return list.stream().map(one->{
            HandledRemarkDTO remark = new HandledRemarkDTO();
            BeanUtils.copyProperties(one, remark);
            if (StringUtils.isEmpty(remark.getHandleRemark())){
                remark.setHandleRemark("无");
            }
            return remark;
        }).collect(Collectors.toList());
    }
}