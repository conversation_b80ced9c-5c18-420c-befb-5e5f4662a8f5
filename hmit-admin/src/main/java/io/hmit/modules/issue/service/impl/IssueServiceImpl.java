package io.hmit.modules.issue.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.hmit.common.constant.Constant;
import io.hmit.common.exception.HmitException;
import io.hmit.common.page.PageData;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.common.utils.ConvertUtils;
import io.hmit.common.utils.DateUtils;
import io.hmit.common.utils.sm.sm4.SM4Utils;
import io.hmit.model.DingResponse;
import io.hmit.modules.issue.IssueTypeEnum;
import io.hmit.modules.issue.dao.IssueDao;
import io.hmit.modules.issue.dto.IssueDTO;
import io.hmit.modules.issue.dto.IssueUserDTO;
import io.hmit.modules.issue.entity.IssueEntity;
import io.hmit.modules.issue.entity.IssueUserEntity;
import io.hmit.modules.issue.service.IssueService;
import io.hmit.modules.issue.service.IssueUserService;
import io.hmit.modules.security.user.SecurityUser;
import io.hmit.modules.security.user.UserDetail;
import io.hmit.modules.sys.dao.SysUserDao;
import io.hmit.modules.sys.dao.SysUserRelationDao;
import io.hmit.modules.sys.dto.SysDeptDTO;
import io.hmit.modules.sys.dto.SysRoleDTO;
import io.hmit.modules.sys.dto.SysUserDTO;
import io.hmit.modules.sys.entity.SysRoleEntity;
import io.hmit.modules.sys.entity.SysUserEntity;
import io.hmit.modules.sys.entity.SysUserRelationEntity;
import io.hmit.modules.sys.service.SysDeptService;
import io.hmit.modules.sys.service.SysRoleService;
import io.hmit.modules.sys.service.SysRoleUserService;
import io.hmit.modules.sys.service.SysUserService;
import io.hmit.service.DingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <h1>工单 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-03-31
 */
@Slf4j
@Service
public class IssueServiceImpl extends CrudServiceImpl<IssueDao, IssueEntity, IssueDTO> implements IssueService {

    @Autowired
    private IssueUserService issueUserService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysRoleUserService sysRoleUserService;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private DingService dingService;

    @Autowired
    private SysUserDao sysUserDao;

    @Value("${ding.tenantId}")
    private String tenantId = "";
    @Autowired
    private SysUserRelationDao sysUserRelationDao;

    @Override
    public QueryWrapper<IssueEntity> getWrapper(Map<String, Object> params){
        String id = (String) params.get("id");
        QueryWrapper<IssueEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }

    @Override
    public PageData<IssueDTO> page(Map<String,Object> params){
        IPage<IssueEntity> page = getPage(params, Constant.ID,false);

        List<IssueEntity> list = baseDao.getList(params);

        // 补充工单发起人的姓名
        PageData<IssueDTO> dtoPage = getPageData(list,page.getTotal(),IssueDTO.class);
        if (CollectionUtils.isNotEmpty(dtoPage.getList())) {
            Map<String, Object> userParam = new HashMap<>();
            Set<Long> ids =  dtoPage.getList().stream().map(IssueDTO::getCreator).collect(Collectors.toSet());
            userParam.put("ids", new ArrayList<>(ids));
            List<SysUserDTO> userDTOS = userList(userParam);
            if (CollectionUtils.isNotEmpty(userDTOS)){
                Map<Long, String> idNameMap = userDTOS.stream().collect(Collectors.toMap(SysUserDTO::getId, SysUserDTO::getRealName));
                dtoPage.setList(dtoPage.getList().stream().peek(one -> {
                    Long creatorId = one.getCreator();
                    String creatorName = (idNameMap.containsKey(creatorId) && !StringUtils.isEmpty(idNameMap.get(creatorId))) ? idNameMap.get(creatorId) : "/";
                    one.setCreatorName(creatorName);
                }).collect(Collectors.toList()));
            }
        }

        return dtoPage;

    }

    /**
     * 我发起的工单列表
     * @param params 查询参数
     * @return 当前用户创建的工单列表
     */
    public PageData<IssueDTO> pageForInit(Map<String,Object> params){
        getInitParams(params);
        return page(params);
    }

    /**
     * 待处理的工单列表
     * @param params 查询参数
     * @return 当前用户创建的工单列表
     */
    public PageData<IssueDTO> pageForTodoAndDoneList(Map<String,Object> params, int isTodo){
        getTodoAndDoneParams(params, isTodo);
        return page(params);
    }

    @Override
    public List<IssueDTO> initList(Map<String, Object> params) {
        getInitParams(params);
        List<IssueEntity> entityList = baseDao.getList(params);
        return ConvertUtils.sourceToTarget(entityList, IssueDTO.class);
    }

    @Override
    public List<IssueDTO> todoAndDoneList(Map<String, Object> params, int isTodo) {
        getTodoAndDoneParams(params, isTodo);
        List<IssueEntity> entityList = baseDao.getList(params);
        return ConvertUtils.sourceToTarget(entityList, IssueDTO.class);
    }

    /**
     * 获取发起工单列表参数
     * @param params
     */
    private void getInitParams(Map<String, Object> params){
        UserDetail user = SecurityUser.getUser();
        params.put("creator", user.getId());
    }

    /**
     * 获取待办或已处理的工单列表参数
     * @param params
     * @param isTodo
     */
    private void getTodoAndDoneParams(Map<String, Object> params, int isTodo){
        UserDetail user = SecurityUser.getUser();
        params.put("ids", new ArrayList<Long>());
        List<IssueUserEntity> issueUserEntityList = issueUserService.findByIssueIdAndUserId(0L, user.getId(), isTodo);
        if (CollectionUtils.isNotEmpty(issueUserEntityList)) {
            List<Long> ids = issueUserEntityList.stream().map(IssueUserEntity::getIssueId).collect(Collectors.toList());
            params.put("ids", ids);
        }
        if (isTodo==1) {
            params.put("todoUserId", user.getId());
        }
        else if (isTodo==2) {
            params.put("DoneUserId", user.getId());
        }
    }



    /**
     * 新增工单
     * 发送浙政钉工作通知
     * @param dto 工单信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(IssueDTO dto) {
        int old_status = null==dto.getStatus()?0:dto.getStatus();
        // 状态更新为 1-已发起
        // 补充: 若发起时，(无窗口负责人)直接选择了"业务科室负责人", 那么需要将状态再+1, 更新为 2-窗口已处理(业务科室处理中)
        dto.setStatus(old_status+1);
        // 补全工单类型名称
        dto.setTypeName(IssueTypeEnum.getByCode(dto.getTypeCode()).getDesc());
        // 补全窗口信息
        UserDetail user = SecurityUser.getUser();
        dto.setWindowsId(user.getWindowId());
        dto.setWindowsCode(user.getWindowNo());
        // 补全部门信息
        SysDeptDTO dept = sysDeptService.get(user.getDeptId());
        dto.setDeptId(user.getDeptId());
        dto.setDeptName(dept.getName());

        dto.setIssueCode(DateUtils.format(new Date(), "yyyyMMddHHmmssSSS"));
        super.save(dto);

        // 写入issue_user (工单-处理人关联)
        IssueUserDTO issueUserDTO = new IssueUserDTO(dto.getId(), dto.getTodoUserId(), 1);
        issueUserService.save(issueUserDTO);

        // 向 "待处理人" 发送工作通知
        sendNotifyToUser(dto);
    }

    /**
     * 处理工单 (更新工单信息)
     * 发送浙政钉工作通知
     * @param dto 工单信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(IssueDTO dto) {
        IssueDTO issueFromDB = get(dto.getId());
        if (null==issueFromDB) {
            throw new HmitException("未找到工单");
        }
        // 获取当前用户信息 (校验处理人)
        UserDetail user = SecurityUser.getUser();
        if (!user.getId().equals(issueFromDB.getTodoUserId())){
            throw new HmitException("当前用户不是该工单指定的处理人");
        }
        // 工单状态码
        int status = issueFromDB.getStatus();
        // 待处理人 -> 已处理人
        dto.setDoneUserId(issueFromDB.getTodoUserId());
        dto.setDoneUserRealName(issueFromDB.getTodoUserRealName());
        switch (issueFromDB.getStatus()) {
            case 1:
                // 窗口处理工单
                process(dto, 2);
                break;
            case 2:
                // 业务科室处理工单
                process(dto, 3);
                break;
            case 3:
                // 网管处理工单
                // 网管处理附件对应 fileHandle, 需要更新补充 综合科的处理附件 fileHandle2
                if (StringUtils.isEmpty(dto.getFileHandle2()) && !StringUtils.isEmpty(issueFromDB.getFileHandle2())) {
                    dto.setFileHandle2(issueFromDB.getFileHandle2());
                }
                process(dto, 4);
                break;
            case 4:
                // 综合科人员处理工单
                // 综合科的处理附件 fileHandle2, 需要更新补充 网管处理附件 fileHandle
                if (StringUtils.isEmpty(dto.getFileHandle()) && !StringUtils.isEmpty(issueFromDB.getFileHandle())) {
                    dto.setFileHandle(issueFromDB.getFileHandle());
                }
                process(dto, 5);
                break;
            default:
                break;
        }

        // 查找并更新状态为 2-已处理
        List<IssueUserEntity> issueUserEntityList = issueUserService.findByIssueIdAndUserId(dto.getId(), user.getId(),1);
        if (CollectionUtils.isNotEmpty(issueUserEntityList)) {
            IssueUserEntity issueUserEntity = issueUserEntityList.get(0);
            issueUserEntity.setStatus(2);
            // 设置该人员的处理描述
            issueUserEntity.setHandleRemark(dto.getRemark());
            // 设置该人员角色
            if (status>0 && status<5) {
                issueUserEntity.setRoleName(roleNames[status]);
                issueUserEntity.setUserRealName(user.getRealName());
            }
            // 更新时间(处理时间)
            issueUserEntity.setUpdateDate(new Date());
            issueUserService.updateById(issueUserEntity);
        }

        // 若存在下一环节的处理人, 则继续新增 issue_user 关联记录
        if (null!=dto.getTodoUserId() && dto.getTodoUserId()>0) {
            IssueUserDTO issueUserDTO = new IssueUserDTO(dto.getId(), dto.getTodoUserId(), 1);
            issueUserService.save(issueUserDTO);
        }

        // 更新后, 工单状态不等于5 (未完结), 则向下一环节的处理人发送工作通知
        if (5!=dto.getStatus()) {
            sendNotifyToUser(dto);
        }

    }

    /**
     * 向指定用户发送工作通知
     * @param dto 工单
     */
    public void sendNotifyToUser(IssueDTO dto) {
        try {
            SysUserDTO todoUser = sysUserService.get(dto.getTodoUserId());
            if (null==todoUser || StringUtils.isEmpty(todoUser.getAccountId())) {
                throw new HmitException("未查询到待处理人浙政钉账号信息: "+dto.getTodoUserId());
            }

            // accountId 用 SM4的CBC模式解密
            String[] receiverIds = { SM4Utils.decryptData_CBC(todoUser.getAccountId())};
            log.info("WorkNotify receivers = {}", String.join(",", receiverIds));
            String dt = DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN);

            // 查询工单发起人
            SysUserDTO creator = sysUserService.get(dto.getCreator());
            String creatorName = null==creator ?"/" :creator.getRealName();

            // 通知消息格式如下:
            /** 您有一条工单，请及时处理：
                类型： 意见反馈
                窗口： E201
                时间： 2025-06-09 10:20:35
                内容： E201窗口机器故障，请派人维修
                发起人： 张三
             */
            String msg = "您有一条工单，请及时处理：  \\n" +
                    "\\n类型： " + dto.getTypeName() +
                    "\\n窗口： " + (StringUtils.isEmpty(dto.getWindowsCode())?"无":dto.getWindowsCode()) +
                    "\\n时间： " + dt +
                    "\\n内容： " + dto.getContent() +
                    "\\n发起人： " + creatorName ;
            String bizMsgId = UUID.randomUUID().toString().replace("-","");
            String organizationCodes="";
            DingResponse dingResponse = dingService.sendWorkNotificationText(receiverIds, tenantId, msg, bizMsgId, organizationCodes);
            log.info("WorkNotifyInfo = {}",dingResponse);
        } catch (Exception e) {
            log.error("工作通知发送失败: {}", e.getLocalizedMessage());
        }
    }

    /**
     * 驳回工单 (更新工单信息)
     * @param dto 工单信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reject(IssueDTO dto) {
        IssueDTO issueFromDB = get(dto.getId());
        if (null==issueFromDB) {
            throw new HmitException("未找到工单");
        }
        // 获取当前用户信息 (校验处理人)
        UserDetail user = SecurityUser.getUser();
        if (!user.getId().equals(issueFromDB.getTodoUserId())){
            throw new HmitException("当前用户不是该工单指定的处理人");
        }
        // 待处理人 -> 已处理人
        dto.setDoneUserId(issueFromDB.getTodoUserId());
        dto.setDoneUserRealName(issueFromDB.getTodoUserRealName());

        // 状态更新为 5-工单结束
        dto.setStatus(5);
        super.update(dto);

        // 查找并更新工单用户状态为 2-已处理
        List<IssueUserEntity> issueUserEntityList = issueUserService.findByIssueIdAndUserId(dto.getId(), user.getId(),1);
        if (CollectionUtils.isNotEmpty(issueUserEntityList)) {
            IssueUserEntity issueUserEntity = issueUserEntityList.get(0);
            issueUserEntity.setStatus(2);
            issueUserService.updateById(issueUserEntity);
        }
    }

    /**
     * 获取工单详情
     * @param id 工单ID
     * @return
     */
    @Override
    public IssueDTO get(Long id) {
        IssueDTO dto = super.get(id);
        // 补充抄送人姓名
        if (StringUtils.isNotBlank(dto.getCcUsers())) {
            Map<String, Object> params = new HashMap<>();
            params.put("ids", dto.getCcUsers().trim().split(","));
            List<SysUserDTO> userDTOS = sysUserService.list(params);
            dto.setCcUserRealNames(userDTOS.stream()
                    .map(SysUserDTO::getRealName)
                    .collect(Collectors.joining(",")));
        }

        // 查询工单发起人
        SysUserDTO creator = sysUserService.get(dto.getCreator());
        String creatorName = (null==creator) ? "/" : creator.getRealName();
        dto.setCreatorName(creatorName);

        return dto;
    }

    /**
     * 工单处理
     * @param dto 工单信息
     * @param nextStatus 下一环节状态码
     */
    public void process(IssueDTO dto, int nextStatus) {
        dto.setStatus(nextStatus);
        // 若工单已解决(resolved==1), 则状态直接更新为 5-完结
        if (dto.getResolved()==1) {
            dto.setStatus(5);
        }
        super.update(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long[] ids) {
        super.delete(ids);
        issueUserService.deleteByIssueIds(ids);
    }

    public IssueDTO processByWindow(IssueDTO issueFromDB, IssueDTO dto) {
        // 检查角色为 "窗口负责人"
        // 检查状态为 1: 否则表示已处理
        // 状态更新为 2-窗口已处理
        issueFromDB.setStatus(2);
        // "待处理人" 更新为: "已处理人"
        issueFromDB.setDoneUserId(issueFromDB.getTodoUserId());
        issueFromDB.setTodoUserId(dto.getTodoUserId());
//        issueFromDB.setFileReply(dto.getFileReply());
        if (StringUtils.isNotBlank(dto.getContent())) {
            issueFromDB.setContent(dto.getContent());
        }
        issueFromDB.setResolved(dto.getResolved());
        return issueFromDB;
    }

    public void saveIssueUser(Long issueId, Long userId, int status) {
        // 写入issue_user
        IssueUserDTO issueUserDTO = new IssueUserDTO();
        issueUserDTO.setIssueId(issueId);
        issueUserDTO.setUserId(userId);
        issueUserDTO.setStatus(status);
        issueUserService.save(issueUserDTO);
    }

    private final String[] roleNames = {"窗口工作人员", "窗口负责人", "业务科室联系人", "驻点网管", "综合科相关人员"};

    /**
     * 1877184691224412162	窗口工作人员
     * 1910155723619938305	窗口负责人
     * 1910155784097607681	业务科室联系人
     * 1910155847574204417	驻点网管
     * @return
     */
    public List<SysUserDTO> todoUserList(int issueStatus){
        // 对于未在 "处理中" 状态的工单, 返回空列表
        if (issueStatus<0 || issueStatus>=4) return new ArrayList<>();
        UserDetail user = SecurityUser.getUser();
        Map<String,Object> params = new HashMap<>();

        // 根据工单状态, 查找下一环节处理人的角色
        String roleName = roleNames[issueStatus+1];
        QueryWrapper<SysRoleEntity> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(roleName), "name", roleName);
        List<SysRoleDTO> roleDTOS = sysRoleService.list(wrapper);
        if (CollectionUtils.isEmpty(roleDTOS)) {
            throw new HmitException("未找到角色: "+roleNames[issueStatus+1]);
        }
        params.put("roleId", roleDTOS.get(0).getId());
        List<SysUserDTO> userList = userList(params);
        // 状态为新发起的工单
        if (issueStatus==0) {
            // 查询工作人员对应的窗口负责人
            QueryWrapper<SysUserRelationEntity> relationWrapper = new QueryWrapper<>();
            // 匹配工作人员id
            relationWrapper.eq("user_id", user.getId());
            // 匹配上级用户角色 (包含 "窗口负责人")
            relationWrapper.like("parent_role_name", roleNames[1]);
            List<SysUserRelationEntity> relationList =  sysUserRelationDao.selectList(relationWrapper);
            if (CollectionUtils.isNotEmpty(relationList)) {
                Set<Long> pids = relationList.stream().map(SysUserRelationEntity::getPid).collect(Collectors.toSet());
                userList = userList.stream()
                        .filter(one->pids.contains(one.getId()))
                        .collect(Collectors.toList());
            }
            else {
                userList = new ArrayList<>();
            }
            SysUserDTO noUser = new SysUserDTO();
            noUser.setRealName("若无窗口负责人，点击选择业务科室联系人");
            userList.add(noUser);
        }
        return userList;
    }

    /**
     * 查询用户列表
     * @param params 查询参数
     * @return List: SysUserDTO
     */
    public List<SysUserDTO> userList(Map<String, Object> params) {
        UserDetail user = SecurityUser.getUser();
        // 根据角色 更新查询条件
        updateParamsForRole(params);
        List<SysUserEntity> entityList = sysUserDao.getList(params);
        return ConvertUtils.sourceToTarget(entityList, SysUserDTO.class);
    }


    /**
     * 根据角色 更新查询条件
     * @param params
     */
    public void updateParamsForRole(Map<String,Object> params) {
        if (!params.containsKey("roleId")) return;
        Long roleId = Long.valueOf(params.get("roleId").toString());
        SysRoleDTO sysRoleDTO = sysRoleService.get(roleId);
        if (null==sysRoleDTO) {
            throw new HmitException("未找到角色");
        }
        List<Long> userIdList = sysRoleUserService.getUserIdList(roleId);
        if (CollectionUtils.isNotEmpty(userIdList)) {
            params.put("ids", userIdList);
        }
    }


}