package io.hmit.modules.issue.service;

import io.hmit.common.service.CrudService;
import io.hmit.modules.issue.dto.HandledRemarkDTO;
import io.hmit.modules.issue.dto.IssueUserDTO;
import io.hmit.modules.issue.entity.IssueUserEntity;

import java.util.List;

/**
 * <h1>工单用户(处理人)关系 Service</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-01
 */
public interface IssueUserService extends CrudService<IssueUserEntity, IssueUserDTO> {

    List<IssueUserEntity> findByIssueIdAndUserId(Long issueId, Long userId, int status);

    /** 根据issueId删除 **/
    void deleteByIssueIds(Long[] ids);

    /** 查询工单处理历史描述列表 **/
    List<HandledRemarkDTO> handledRemarkList(Long issueId);
}