package io.hmit.modules.issue.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.issue.IssueTypeEnum;
import io.hmit.modules.issue.dto.HandledRemarkDTO;
import io.hmit.modules.issue.dto.IssueDTO;
import io.hmit.modules.issue.excel.IssueExcel;
import io.hmit.modules.issue.service.IssueService;
import io.hmit.modules.issue.service.IssueUserService;
import io.hmit.modules.sys.dto.SysUserDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>工单</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-03-31
 */
@RestController
@RequestMapping("issue")
@Api(tags="工单")
public class IssueController {

    private final IssueService issueService;
    private final IssueUserService issueUserService;

    public IssueController(IssueService issueService, IssueUserService issueUserService) {
        this.issueService = issueService;
        this.issueUserService = issueUserService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "issueCode", value = "工单编码", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "content", value = "工单内容", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "deptId", value = "部门ID", paramType = "query", dataType="Long"),
            @ApiImplicitParam(name = "windowsId", value = "窗口ID", paramType = "query", dataType="Long"),
            @ApiImplicitParam(name = "createDateStart", value = "发起时间起点", paramType = "query", dataType="Date"),
            @ApiImplicitParam(name = "createDateEnd", value = "发起时间终点", paramType = "query", dataType="Date"),
            @ApiImplicitParam(name = "typeCode", value = "工单类型码", paramType = "query", dataType="Integer"),
            @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType="Integer")
    })
    @RequiresPermissions("issue:page")
    public Result<PageData<IssueDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<IssueDTO> page = issueService.page(params);

        return new Result<PageData<IssueDTO>>().ok(page);
    }

    @GetMapping("pageForInit")
    @ApiOperation("用户发起的工单列表-分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "issueCode", value = "工单编码", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "content", value = "工单内容", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "deptId", value = "部门ID", paramType = "query", dataType="Long"),
            @ApiImplicitParam(name = "windowsId", value = "窗口ID", paramType = "query", dataType="Long"),
            @ApiImplicitParam(name = "createDateStart", value = "发起时间 起点", paramType = "query", dataType="Date"),
            @ApiImplicitParam(name = "createDateEnd", value = "发起时间 终点", paramType = "query", dataType="Date"),
            @ApiImplicitParam(name = "typeCode", value = "工单类型码", paramType = "query", dataType="Integer"),
            @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType="Integer")
    })
    @RequiresPermissions("issue:init:page")
    public Result<PageData<IssueDTO>> pageForInit(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<IssueDTO> page = issueService.pageForInit(params);

        return new Result<PageData<IssueDTO>>().ok(page);
    }

    @GetMapping("pageForTodoList")
    @ApiOperation("待处理工单列表-分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "issueCode", value = "工单编码", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "content", value = "工单内容", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "deptId", value = "部门ID", paramType = "query", dataType="Long"),
            @ApiImplicitParam(name = "windowsId", value = "窗口ID", paramType = "query", dataType="Long"),
            @ApiImplicitParam(name = "createDateStart", value = "发起时间起点", paramType = "query", dataType="Date"),
            @ApiImplicitParam(name = "createDateEnd", value = "发起时间终点", paramType = "query", dataType="Date"),
            @ApiImplicitParam(name = "typeCode", value = "工单类型码", paramType = "query", dataType="Integer"),
            @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType="Integer")
    })
    @RequiresPermissions("issue:todo:page")
    public Result<PageData<IssueDTO>> pageForTodoList(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<IssueDTO> page = issueService.pageForTodoAndDoneList(params, 1);

        return new Result<PageData<IssueDTO>>().ok(page);
    }

    @GetMapping("pageForDoneList")
    @ApiOperation("已处理工单列表-分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "issueCode", value = "工单编码", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "content", value = "工单内容", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "deptId", value = "部门ID", paramType = "query", dataType="Long"),
            @ApiImplicitParam(name = "windowsId", value = "窗口ID", paramType = "query", dataType="Long"),
            @ApiImplicitParam(name = "createDateStart", value = "发起时间起点", paramType = "query", dataType="Date"),
            @ApiImplicitParam(name = "createDateEnd", value = "发起时间终点", paramType = "query", dataType="Date"),
            @ApiImplicitParam(name = "typeCode", value = "工单类型码", paramType = "query", dataType="Integer"),
            @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType="Integer")
    })
    @RequiresPermissions("issue:done:page")
    public Result<PageData<IssueDTO>> pageForDoneList(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<IssueDTO> page = issueService.pageForTodoAndDoneList(params, 2);

        return new Result<PageData<IssueDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("issue:info")
    public Result<IssueDTO> get(@PathVariable("id") Long id){
        IssueDTO data = issueService.get(id);

        return new Result<IssueDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("issue:save")
    public Result<Object> save(@RequestBody IssueDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        issueService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("issue:update")
    public Result<Object> update(@RequestBody IssueDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        issueService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("issue:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        issueService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("issue:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<IssueDTO> list = issueService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, IssueExcel.class);
    }

    @GetMapping("type/dict")
    @ApiOperation("工单类型字典")
    public Result<List<Map<String,Object>>> issueTypeDict(){
        return new Result<List<Map<String,Object>>>().ok(IssueTypeEnum.typeMap());
    }

    @GetMapping("todoUserList")
    @ApiOperation("待处理人列表")
    @RequiresPermissions("issue:done:page")
    public Result<List<SysUserDTO>> list(@RequestParam int status) {
        List<SysUserDTO> list = issueService.todoUserList(status);
        return new Result<List<SysUserDTO>>().ok(list);
    }

    // 目前工单流程中暂无“驳回”环节
    @PutMapping("reject")
    @ApiOperation("驳回工单")
    @RequiresPermissions("issue:update")
    public Result<Object> reject(@RequestBody IssueDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        issueService.reject(dto);

        return new Result<>();
    }


    @GetMapping("exportForInit")
    @ApiOperation("用户发起的工单列表-导出")
    @ApiImplicitParams({})
    @RequiresPermissions("issue:export")
    public void exportForInit(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<IssueDTO> list = issueService.initList(params);

        ExcelUtils.exportExcelToTarget(response, null, list, IssueExcel.class);
    }

    @GetMapping("exportForTodoList")
    @ApiOperation("待处理工单列表-导出")
    @ApiImplicitParams({})
    @RequiresPermissions("issue:export")
    public void exportForTodoList(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<IssueDTO> list = issueService.todoAndDoneList(params, 1);

        ExcelUtils.exportExcelToTarget(response, null, list, IssueExcel.class);
    }

    @GetMapping("exportForDoneList")
    @ApiOperation("已处理工单列表-导出")
    @ApiImplicitParams({})
    @RequiresPermissions("issue:export")
    public void exportForDoneList(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<IssueDTO> list = issueService.todoAndDoneList(params, 2);

        ExcelUtils.exportExcelToTarget(response, null, list, IssueExcel.class);
    }

    @GetMapping("handledRemarkList")
    @ApiOperation("工单处理历史描述列表")
    @ApiImplicitParams({})
    @RequiresPermissions("issue:info")
    public Result<List<HandledRemarkDTO>> handledRemarkList(Long issueId){
        List<HandledRemarkDTO> list = issueUserService.handledRemarkList(issueId);
        return new Result<List<HandledRemarkDTO>>().ok(list);
    }

}