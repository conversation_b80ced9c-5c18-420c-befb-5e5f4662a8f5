package io.hmit.modules.issue.service;

import io.hmit.common.page.PageData;
import io.hmit.common.service.CrudService;
import io.hmit.modules.issue.dto.IssueDTO;
import io.hmit.modules.issue.entity.IssueEntity;
import io.hmit.modules.sys.dto.SysUserDTO;

import java.util.List;
import java.util.Map;

/**
 * <h1>工单 Service</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-03-31
 */
public interface IssueService extends CrudService<IssueEntity, IssueDTO> {

    /** 分页查询 **/
    @Override
    PageData<IssueDTO> page(Map<String, Object> params);

    /** 我发起的工单 分页列表 **/
    PageData<IssueDTO> pageForInit(Map<String,Object> params);

    /** 待处理和已处理的工单 分页列表 **/
    PageData<IssueDTO> pageForTodoAndDoneList(Map<String,Object> params, int isTodo);

    /** 我发起的工单 列表 **/
    List<IssueDTO> initList(Map<String,Object> params);

    /** 待处理和已处理的工单 列表 **/
    List<IssueDTO> todoAndDoneList(Map<String,Object> params, int isTodo);


    /** 新增工单 **/
    @Override
    void save(IssueDTO dto);

    /** 处理工单 **/
    @Override
    void update(IssueDTO dto);

    /** 驳回工单 **/
    void reject(IssueDTO dto);

    @Override
    void delete(Long[] ids);

    /** 待处理人员列表 **/
    List<SysUserDTO> todoUserList(int issueStatus);

    //    /** 窗口处理 **/
//    void processByWindow(IssueDTO dto);
//
//    /** 业务科室处理 **/
//    void processByBusinessOffice(IssueDTO dto);
//
//    /** 运维人员处理 **/
//    void processByOpsStaff(IssueDTO dto);

}