package io.hmit.modules.backend.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.backend.dto.YuzhiDTO;
import io.hmit.modules.backend.excel.YuzhiExcel;
import io.hmit.modules.backend.service.YuzhiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>阈值设置</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-02-20
 */
@RestController
@RequestMapping("backend/yuzhi")
@Api(tags="阈值设置")
public class YuzhiController {

    private final YuzhiService yuzhiService;

    public YuzhiController(YuzhiService yuzhiService) {
        this.yuzhiService = yuzhiService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("backend:yuzhi:page")
    public Result<PageData<YuzhiDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<YuzhiDTO> page = yuzhiService.page(params);

        return new Result<PageData<YuzhiDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("backend:yuzhi:info")
    public Result<YuzhiDTO> get(@PathVariable("id") Long id){
        YuzhiDTO data = yuzhiService.get(id);

        return new Result<YuzhiDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("backend:yuzhi:save")
    public Result<Object> save(@RequestBody YuzhiDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        yuzhiService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("backend:yuzhi:update")
    public Result<Object> update(@RequestBody YuzhiDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        yuzhiService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("backend:yuzhi:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        yuzhiService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("backend:yuzhi:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<YuzhiDTO> list = yuzhiService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, YuzhiExcel.class);
    }

}