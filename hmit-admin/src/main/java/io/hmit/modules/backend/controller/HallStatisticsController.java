package io.hmit.modules.backend.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ConvertUtils;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.backend.dto.CoreIndicatorsDTO;
import io.hmit.modules.backend.dto.HallStatisticsBusinessDTO;
import io.hmit.modules.backend.dto.HallStatisticsDTO;
import io.hmit.modules.backend.dto.NumberTrendDTO;
import io.hmit.modules.backend.excel.HallStatisticsExcel;
import io.hmit.modules.backend.service.HallStatisticsBusinessService;
import io.hmit.modules.backend.service.HallStatisticsDeptService;
import io.hmit.modules.backend.service.HallStatisticsService;
import io.hmit.modules.backend.service.HallStatisticsWindowsService;
import io.hmit.modules.qh.dao.BusinessDao;
import io.hmit.modules.qh.dao.WindowsDao;
import io.hmit.modules.qh.entity.BusinessEntity;
import io.hmit.modules.qh.entity.WindowsEntity;
import io.hmit.modules.security.user.SecurityUser;
import io.hmit.modules.security.user.UserDetail;
import io.hmit.modules.sys.dao.SysDeptDao;
import io.hmit.modules.sys.entity.SysDeptEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>大厅统计信息</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-24
 */
@RestController
@RequestMapping("backend/hallstatistics")
@Api(tags="大厅统计信息")
public class HallStatisticsController {

    private final HallStatisticsService hallStatisticsService;

    public HallStatisticsController(HallStatisticsService hallStatisticsService) {
        this.hallStatisticsService = hallStatisticsService;
    }

    @Autowired
    private HallStatisticsDeptService hallStatisticsDeptService;
    @Autowired
    private HallStatisticsWindowsService hallStatisticsWindowsService;
    @Autowired
    private HallStatisticsBusinessService hallStatisticsBusinessService;
    @Autowired
    private SysDeptDao sysDeptDao;
    @Autowired
    private WindowsDao windowsDao;
    @Autowired
    private BusinessDao businessDao;


    // 数据分析-取号趋势，参数：起始时间、结束时间
    @GetMapping("getNumberTrend")
    @ApiOperation("数据分析-取号趋势，参数：起始时间、结束时间")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "起始时间", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "String")
    })
    public Result<List<NumberTrendDTO>> getNumberTrend(@RequestParam(value = "startDate",required = false)  String startDate,
                                                       @RequestParam(value = "endDate",required = false)  String endDate) {



        endDate = endDate == null ? DateUtil.today() : endDate;
        startDate = startDate == null ?  DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(endDate),-14)): startDate; // 取前14天

        // 判断起始时间不能大于结束时间
        if (DateUtil.parse(startDate).isAfter(DateUtil.parse(endDate))) {
            return new Result<List<NumberTrendDTO>>().error("起始时间不能大于结束时间");
        }

        List<NumberTrendDTO> numberTrendDTOS = hallStatisticsService.getNumberTrend(startDate, endDate);
        return new Result<List<NumberTrendDTO>>().ok(numberTrendDTOS);
    }

    // 数据分析-核心指标接口，参数：起始时间、结束时间
    @GetMapping("getHallStatistics")
    @ApiOperation("数据分析-核心指标接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "起始时间", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "String")
    })
    public Result<CoreIndicatorsDTO> getHallStatistics(@RequestParam(value = "startDate",required = false)  String startDate,
                                                       @RequestParam(value = "endDate",required = false)  String endDate) {
        CoreIndicatorsDTO coreIndicatorsDTO = hallStatisticsService.getHallStatistics(startDate, endDate);
        return new Result<CoreIndicatorsDTO>().ok(coreIndicatorsDTO);
    }

    // 首页——根据日期获取大厅统计信息
    @GetMapping("getHallStatisticsByDate")
    @ApiOperation("根据日期获取大厅统计信息：2024-11-11")
    public Result<HallStatisticsDTO> getHallStatisticsByDate(@RequestParam(value = "date",required = false)  String date) {
        // 若没有传日期，则默认当天
        if (date == null || date.equals("")) {
            date = DateUtil.today();
        }
        
        // 获取当前用户信息
        UserDetail user = SecurityUser.getUser();
        
        // 如果是超级管理员或部门是余姚市政务服务中心，返回所有数据
        if (user.getSuperAdmin() != null && user.getSuperAdmin() == 1 || user.getDeptId() == 1907321385142767617L) {
            HallStatisticsDTO hallStatisticsDTO = hallStatisticsService.getHallStatisticsByDate(date);
            return new Result<HallStatisticsDTO>().ok(hallStatisticsDTO);
        }
        
        // 普通用户只能看本部门数据
        if (user.getDeptId() != null) {
            // 转换部门ID：内部系统ID -> 杭州智慧部门ID
            LambdaQueryWrapper<SysDeptEntity> eq = new QueryWrapper<SysDeptEntity>()
                    .lambda().eq(SysDeptEntity::getId, user.getDeptId());
            SysDeptEntity sysDeptEntity = sysDeptDao.selectOne(eq);
            
            if (sysDeptEntity != null && sysDeptEntity.getCode() != null) {
                // 调用部门级统计数据接口
                HallStatisticsBusinessDTO businessDTO = hallStatisticsDeptService
                        .getHallStatisticsByDepartmentIdAndDate(sysDeptEntity.getCode(), date, sysDeptEntity.getName());
                
                // 转换为HallStatisticsDTO
                if (businessDTO != null) {
                    HallStatisticsDTO hallStatisticsDTO = ConvertUtils.sourceToTarget(businessDTO, HallStatisticsDTO.class);
                    return new Result<HallStatisticsDTO>().ok(hallStatisticsDTO);
                }
            }
        }
        
        // 如果没有部门信息或转换失败，返回空数据
        return new Result<HallStatisticsDTO>().ok(new HallStatisticsDTO());
    }

    // 运营检测中心——根据部门(departmentId)、窗口（windowId）、业务（businessId）、日期获取大厅统计信息
    @GetMapping("getHallStatisticsByIds")
    @ApiOperation("根据部门(departmentId)、窗口（windowId）、业务（businessId）、日期获取大厅统计信息：2024-11-11")
    public Result<HallStatisticsBusinessDTO> getHallStatisticsByIds(@RequestParam(value = "departmentId",required = false)  Long departmentId,
                                                                    @RequestParam(value = "windowId",required = false)  Long windowId,
                                                                    @RequestParam(value = "businessId",required = false)  Long businessId,
                                                                    @RequestParam(value = "date",required = false)  String date) {
        // 若没有传日期，则默认当天
        if (date == null || date.equals("")) {
            date = DateUtil.today();
        }

        // 0.windowId businessId departmentId 都没传
        if (windowId == null && businessId == null && departmentId == null) {
            HallStatisticsDTO hallStatisticsByDate = hallStatisticsService.getHallStatisticsByDate(date);
            HallStatisticsBusinessDTO retDto = ConvertUtils.sourceToTarget(hallStatisticsByDate, HallStatisticsBusinessDTO.class);
            return new Result<HallStatisticsBusinessDTO>().ok(retDto);
        }

        // 1.只传递了departmentId
        if (windowId == null && businessId == null && departmentId != null) {

            HallStatisticsBusinessDTO hallStatisticsBusinessDTO=null;
            // 在HallStatisticsBusiness表中，保存的是杭州智慧的departmentId，而参数中的departmentId是系统表的，因此在查询时需要转换
            LambdaQueryWrapper<SysDeptEntity> eq = new QueryWrapper<SysDeptEntity>().lambda().eq(SysDeptEntity::getId, departmentId);
            SysDeptEntity sysDeptEntity = sysDeptDao.selectOne(eq);
            if(sysDeptEntity != null){
                hallStatisticsBusinessDTO = hallStatisticsDeptService.getHallStatisticsByDepartmentIdAndDate(sysDeptEntity.getCode(), date, sysDeptEntity.getName());
            }
            return new Result<HallStatisticsBusinessDTO>().ok(hallStatisticsBusinessDTO);
        }

        // 2.传递了windowId且没传businessId
        if (windowId != null && businessId == null) {
            HallStatisticsBusinessDTO hallStatisticsBusinessDTO=null;
            // 在HallStatisticsWindows表中，保存的是杭州智慧的departmentId和windowId，而参数中的departmentId和windowId是系统表的，因此在查询时需要转换
            LambdaQueryWrapper<SysDeptEntity> eqDept = new QueryWrapper<SysDeptEntity>().lambda().eq(SysDeptEntity::getId, departmentId);
            SysDeptEntity sysDeptEntity = sysDeptDao.selectOne(eqDept);
            LambdaQueryWrapper<WindowsEntity> eqWin = new QueryWrapper<WindowsEntity>().lambda().eq(WindowsEntity::getId, windowId);
            WindowsEntity windowsEntity = windowsDao.selectOne(eqWin);
            if(windowsEntity != null){
                hallStatisticsBusinessDTO = hallStatisticsWindowsService.getHallStatisticsByDepartmentIdAndWindowIdAndDate(sysDeptEntity==null?null:sysDeptEntity.getCode(), windowsEntity.getWindowUniqueNum().toString(), date, sysDeptEntity==null?null:sysDeptEntity.getName(), windowsEntity.getWindowCode());
            }
            return new Result<HallStatisticsBusinessDTO>().ok(hallStatisticsBusinessDTO);
        }

        // 3.传递了businessId
        if (windowId != null && businessId != null) {
            HallStatisticsBusinessDTO hallStatisticsBusinessDTO=null;
            // 在HallStatisticsBusiness表中，保存的是杭州智慧的departmentId和windowId和businessId，而参数中的departmentId、windowId和businessId是系统表的，因此在查询时需要转换
            LambdaQueryWrapper<SysDeptEntity> eqDept = new QueryWrapper<SysDeptEntity>().lambda().eq(SysDeptEntity::getId, departmentId);
            SysDeptEntity sysDeptEntity = sysDeptDao.selectOne(eqDept);
            LambdaQueryWrapper<WindowsEntity> eqWin = new QueryWrapper<WindowsEntity>().lambda().eq(WindowsEntity::getId, windowId);
            WindowsEntity windowsEntity = windowsDao.selectOne(eqWin);
            LambdaQueryWrapper<BusinessEntity> eqBusiness = new QueryWrapper<BusinessEntity>().lambda().eq(BusinessEntity::getId, businessId);
            BusinessEntity businessEntity = businessDao.selectOne(eqBusiness);
            if(businessEntity != null){
                hallStatisticsBusinessDTO = hallStatisticsBusinessService.getHallStatisticsByDepartmentIdAndWindowIdAndBusinessIdAndDate(sysDeptEntity==null?null:sysDeptEntity.getCode(), windowsEntity==null?null: windowsEntity.getWindowUniqueNum().toString(), businessEntity.getBusinessCode(), date,sysDeptEntity==null?null: sysDeptEntity.getName(),windowsEntity==null?null: windowsEntity.getWindowCode(), businessEntity.getBusinessName());
            }
            return new Result<HallStatisticsBusinessDTO>().ok(hallStatisticsBusinessDTO);
        }

        return new Result<HallStatisticsBusinessDTO>().ok(null);
    }


    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    //@RequiresPermissions("backend:hallstatistics:page")
    public Result<PageData<HallStatisticsDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<HallStatisticsDTO> page = hallStatisticsService.page(params);

        return new Result<PageData<HallStatisticsDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    //@RequiresPermissions("backend:hallstatistics:info")
    public Result<HallStatisticsDTO> get(@PathVariable("id") Long id){
        HallStatisticsDTO data = hallStatisticsService.get(id);

        return new Result<HallStatisticsDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    //@RequiresPermissions("backend:hallstatistics:save")
    public Result<Object> save(@RequestBody HallStatisticsDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        hallStatisticsService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    //@RequiresPermissions("backend:hallstatistics:update")
    public Result<Object> update(@RequestBody HallStatisticsDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        hallStatisticsService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    //@RequiresPermissions("backend:hallstatistics:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        hallStatisticsService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    //@RequiresPermissions("backend:hallstatistics:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<HallStatisticsDTO> list = hallStatisticsService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, HallStatisticsExcel.class);
    }

}
