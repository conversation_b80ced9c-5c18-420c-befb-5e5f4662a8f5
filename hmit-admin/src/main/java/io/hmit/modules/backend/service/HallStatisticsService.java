package io.hmit.modules.backend.service;

import io.hmit.common.service.CrudService;
import io.hmit.modules.backend.dto.CoreIndicatorsDTO;
import io.hmit.modules.backend.dto.HallStatisticsDTO;
import io.hmit.modules.backend.dto.NumberTrendDTO;
import io.hmit.modules.backend.entity.HallStatisticsEntity;

import java.util.List;

/**
 * <h1>大厅统计信息 Service</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-24
 */
public interface HallStatisticsService extends CrudService<HallStatisticsEntity, HallStatisticsDTO> {

    //根据日期获取大厅统计信息
    HallStatisticsDTO getHallStatisticsByDate(String date);

    // 数据分析——核心指标接口
    CoreIndicatorsDTO getHallStatistics(String startDate, String endDate);

    // 数据分析——取号趋势
    List<NumberTrendDTO> getNumberTrend(String startDate, String endDate);

    // 调用杭州智慧接口，生成统计信息，按天数保存至表sys_hall_statistics
    void createHallStatistics();
}
