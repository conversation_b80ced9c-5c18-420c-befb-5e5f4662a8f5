package io.hmit.modules.backend.service;

import io.hmit.common.service.CrudService;
import io.hmit.modules.backend.dto.HallStatisticsBusinessDTO;
import io.hmit.modules.backend.dto.HallStatisticsWindowsDTO;
import io.hmit.modules.backend.entity.HallStatisticsWindowsEntity;

/**
 * <h1>大厅统计信息-窗口 Service</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-25
 */
public interface HallStatisticsWindowsService extends CrudService<HallStatisticsWindowsEntity, HallStatisticsWindowsDTO> {

    HallStatisticsBusinessDTO getHallStatisticsByDepartmentIdAndWindowIdAndDate(String departmentId, String windowId, String date,String dName,String wName);

    // 调用杭州智慧接口，生成窗口的统计数据，按天数保存至表sys_hall_statistics_window
    void createHallStatistics();
}
