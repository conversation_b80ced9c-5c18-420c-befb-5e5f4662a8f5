package io.hmit.modules.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.backend.dao.EarlyWarningDao;
import io.hmit.modules.backend.dao.HallStatisticsDao;
import io.hmit.modules.backend.dto.CoreIndicatorsDTO;
import io.hmit.modules.backend.dto.HallStatisticsDTO;
import io.hmit.modules.backend.dto.NumberTrendDTO;
import io.hmit.modules.backend.entity.EarlyWarningEntity;
import io.hmit.modules.backend.entity.HallStatisticsEntity;
import io.hmit.modules.backend.service.HallStatisticsService;
import io.hmit.modules.cockpit.dto.HallOverviewDTO;
import io.hmit.modules.cockpit.dto.TodayCallStatusResponse;
import io.hmit.modules.cockpit.service.HallOverviewService;
import io.hmit.modules.cockpit.service.impl.ExternalApiServiceImpl;
import io.hmit.modules.job.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <h1>大厅统计信息 Service Impl</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-24
 */
@Slf4j
@Service
public class HallStatisticsServiceImpl extends CrudServiceImpl<HallStatisticsDao, HallStatisticsEntity, HallStatisticsDTO> implements HallStatisticsService {

    private final ExternalApiServiceImpl externalApiService;
    private EarlyWarningDao earlyWarningDao;
    private HallOverviewService hallOverviewService;
    // 构造函数注入
    public HallStatisticsServiceImpl(EarlyWarningDao earlyWarningDao, HallOverviewService hallOverviewService, ExternalApiServiceImpl externalApiService) {
        this.earlyWarningDao = earlyWarningDao;
        this.hallOverviewService=hallOverviewService;
        this.externalApiService = externalApiService;
    }

    @Value("${HZZH.IP}")
    private String apiIp;

    @Value("${HZZH.STATISTIC_IP}")
    private String statisticIP;

    private final RestTemplate restTemplate = new RestTemplate();


    @Override
    public QueryWrapper<HallStatisticsEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<HallStatisticsEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    // 根据日期获取大厅统计信息
    @Override
    public HallStatisticsDTO getHallStatisticsByDate(String date) {


        //======大厅统计数据从数据库yy_hall_statistics中获取，yy_hall_statistics中的数据由定时任务填入=======
        LambdaQueryWrapper<HallStatisticsEntity> apply = new QueryWrapper<HallStatisticsEntity>().lambda().apply("CAST(create_date AS DATE) = {0}", date);
        HallStatisticsEntity hallStatisticsEntity = baseDao.selectOne(apply);
        HallStatisticsDTO hallStatisticsDTO = new HallStatisticsDTO();
        if(hallStatisticsEntity != null)
            BeanUtil.copyProperties(hallStatisticsEntity, hallStatisticsDTO);

        // =========预警统计数据需要实时统计================
        // 获取当日的预警统计总次数
        LambdaQueryWrapper<EarlyWarningEntity> warningNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda().apply("CAST(create_date AS DATE) = {0}", date);
        hallStatisticsDTO.setWarningNum(earlyWarningDao.selectCount(warningNumWrapper).toString());
        // 获取当日的超时接待次数
        LambdaQueryWrapper<EarlyWarningEntity> cNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda().apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 2);
        hallStatisticsDTO.setCNum(earlyWarningDao.selectCount(cNumWrapper));
        // 获取当日的排队过长次数
        LambdaQueryWrapper<EarlyWarningEntity> pNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda().apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 4);
        hallStatisticsDTO.setPNum(earlyWarningDao.selectCount(pNumWrapper));
        // 获取当日的离岗检测提醒次数
        LambdaQueryWrapper<EarlyWarningEntity> lNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda().apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 3);
        hallStatisticsDTO.setLNum(earlyWarningDao.selectCount(lNumWrapper));
        // 获取当日的差评提醒次数
        LambdaQueryWrapper<EarlyWarningEntity> hNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda().apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 1);
        hallStatisticsDTO.setHNum(earlyWarningDao.selectCount(hNumWrapper));

        List<HallOverviewDTO> hallOverviewDTOList = hallOverviewService.list(new HashMap<>());
        if(hallOverviewDTOList.size()>0){
            hallStatisticsDTO.setWindowNum(String.valueOf(hallOverviewDTOList.get(0).getWindowCount()));
        }

        try {
            TodayCallStatusResponse todayCallStatus = externalApiService.getTodayCallStatus();
            hallStatisticsDTO.setTodayPickUp(String.valueOf(todayCallStatus.getData().getGettotal()));
        } catch (Exception e) {
            hallStatisticsDTO.setTodayPickUp(null);
            e.printStackTrace();
        }
        return hallStatisticsDTO;
    }

    @Override
    public CoreIndicatorsDTO getHallStatistics(String startDate, String endDate) {

        CoreIndicatorsDTO coreIndicatorsDTO = new CoreIndicatorsDTO();
        String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        if (startDate == null || startDate.trim().isEmpty()) startDate = today;
        if (endDate == null || endDate.trim().isEmpty()) endDate = today;

        // ===产生办件窗口数+未产生办件窗口数===
        String winUrl = statisticIP + "/smartqueue/report/win?sStart=" + startDate + "&sEnd=" + endDate;
        ResponseEntity<WindowStatResponse> response =
                restTemplate.getForEntity(winUrl, WindowStatResponse.class);
        WindowStatResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            System.out.println("调用窗口统计接口失败：" + (body != null ? body.getMsg() : "未知错误"));
            throw new RuntimeException("调用窗口统计接口失败：" + (body != null ? body.getMsg() : "未知错误"));
        }

        int activeCount = 0;
        int inactiveCount = 0;

        for (WindowStat w : body.getData()) {
            if (w.getNTotal() > 0) {
                activeCount++;
            } else {
                inactiveCount++;
            }
        }

        coreIndicatorsDTO.setWindowNum(activeCount);
        coreIndicatorsDTO.setNoWindowNum(inactiveCount);

        // === 大厅窗口好评数+大厅窗口评价数 ===
        String hallUrl = statisticIP + "/smartqueue/report/hall?sStart=" + startDate + "&sEnd=" + endDate;
        ResponseEntity<HallStatResponse> hallResponse = restTemplate.getForEntity(hallUrl, HallStatResponse.class);
        HallStatResponse hallBody = hallResponse.getBody();

        if (hallBody == null || hallBody.getCode() != 0 || hallBody.getData() == null) {
            System.out.println("接口调用失败：" + (hallBody != null ? hallBody.getMsg() : "未知错误"));
            throw new RuntimeException("接口调用失败：" + (hallBody != null ? hallBody.getMsg() : "未知错误"));
        }

        Integer nTotal = hallBody.getData().getNTotal();
        List<Integer> nApp = hallBody.getData().getNApp();
        int goodReviewCount = (nApp != null && nApp.size() > 0) ? nApp.get(0) : 0;
        int totalReviews = (nApp != null) ? nApp.stream().mapToInt(Integer::intValue).sum() : 0;
        coreIndicatorsDTO.setApplauseNum(goodReviewCount);
        coreIndicatorsDTO.setEvaluationNum(totalReviews);
        coreIndicatorsDTO.setWindowReceiveNum(nTotal);
        // 其它端办件量为0（暂时无法获取该数据）
        coreIndicatorsDTO.setOtherReceiveNum(0);
        return coreIndicatorsDTO;
    }

    // 数据分析——取号趋势
    @Override
    public List<NumberTrendDTO> getNumberTrend(String startDate, String endDate) {
        // 获取日期的差值
        //long between = DateUtil.between(DateUtil.parse(endDate), DateUtil.parse(startDate), DateUnit.DAY);

        List<NumberTrendDTO> numberTrendDTOS = new ArrayList<>();

        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.parse(startDate), DateUtil.parse(endDate), DateField.DAY_OF_YEAR);

        for (DateTime dateTime : dateTimes) {
            System.out.println(DateUtil.formatDate(dateTime));
            NumberTrendDTO numberTrendDTO = new NumberTrendDTO();
            numberTrendDTO.setTimeUnit(DateUtil.formatDate(dateTime));
            // 查询大厅数据-调用杭州智慧接口
            String hallUrl = statisticIP + "/smartqueue/report/hall?sStart=" + DateUtil.formatDate(dateTime) + "&sEnd=" + DateUtil.formatDate(dateTime);
            ResponseEntity<HallStatResponse> hallResponse = restTemplate.getForEntity(hallUrl, HallStatResponse.class);
            HallStatResponse hallBody = hallResponse.getBody();

            if (hallBody == null || hallBody.getCode() != 0 || hallBody.getData() == null) {
                System.out.println("接口调用失败：" + (hallBody != null ? hallBody.getMsg() : "未知错误"));
                throw new RuntimeException("接口调用失败：" + (hallBody != null ? hallBody.getMsg() : "未知错误"));
            }

            Integer nTotal = hallBody.getData().getNTotal();
            List<Integer> nApp = hallBody.getData().getNApp();
            int goodReviewCount = (nApp != null && nApp.size() > 0) ? nApp.get(0) : 0;
            int totalReviews = (nApp != null) ? nApp.stream().mapToInt(Integer::intValue).sum() : 0;

            numberTrendDTO.setNumber(nTotal);
            numberTrendDTO.setApplause(goodReviewCount);
            numberTrendDTOS.add(numberTrendDTO);
        }

        return numberTrendDTOS;
    }

    // 调用杭州智慧接口，生成统计信息，按天数保存至表sys_hall_statistics
    @Override
    public void createHallStatistics() {

        // 大厅的窗口数量
        int windowCount = getWindowCount();
        // 在岗工作人员数量
        int onlineStaffCount = getOnlineStaffCount();
        /*
        今日取号人数	接口返回记录总数
今日办件人数	有效的“结束时间”数量（非空）
平均等待时长	(叫号时间 - 取号时间) 平均值
最短等待时长	最小的 (叫号时间 - 取号时间)
最长等待时长	最大的 (叫号时间 - 取号时间)
总办理时长	所有 (结束时间 - 叫号时间) 的总和
办结率	办件人数 / 取号人数
好评率	满意评价人数 / 已评价人数
满意度评分	所有评价值的平均分
         */
        HallStatisticsDTO hallStatisticsDTO = computeTodayStatistics(DateUtil.formatDate(new Date()));


        // 判断是否存在当天的统计数据
        String date = DateUtil.formatDate(new Date());
        LambdaQueryWrapper<HallStatisticsEntity> apply = new QueryWrapper<HallStatisticsEntity>().lambda().apply("CAST(create_date AS DATE) = {0}", date);
        HallStatisticsEntity hallStatisticsEntity = baseDao.selectOne(apply);
        if(hallStatisticsEntity != null){
            // 存在，则更新数据
            hallStatisticsEntity.setWindowNum(windowCount+"");
            hallStatisticsEntity.setWorkerNum(onlineStaffCount+"");
            hallStatisticsEntity.setTodayPickUp(hallStatisticsDTO.getTodayPickUp());
            hallStatisticsEntity.setTodayProcessed(hallStatisticsDTO.getTodayProcessed());
            hallStatisticsEntity.setAverageWaitingTime(hallStatisticsDTO.getAverageWaitingTime());
            hallStatisticsEntity.setMinWaitingTime(hallStatisticsDTO.getMinWaitingTime());
            hallStatisticsEntity.setMaxWaitingTime(hallStatisticsDTO.getMaxWaitingTime());
            hallStatisticsEntity.setTodayTotalProcessingTime(hallStatisticsDTO.getTodayTotalProcessingTime());
            hallStatisticsEntity.setTodayCompletionRate(hallStatisticsDTO.getTodayCompletionRate());
            hallStatisticsEntity.setSatisfactionRate(hallStatisticsDTO.getSatisfactionRate());
            hallStatisticsEntity.setApplauseRate(hallStatisticsDTO.getApplauseRate());
            hallStatisticsEntity.setCreateDate(new Date());
            this.updateById(hallStatisticsEntity);
        }else{
            // 不存在，则新增数据
            HallStatisticsEntity hallStatisticsEntityNull = new HallStatisticsEntity();
            hallStatisticsEntityNull.setWindowNum(windowCount+"");
            hallStatisticsEntityNull.setWorkerNum(onlineStaffCount+"");
            hallStatisticsEntityNull.setTodayPickUp(hallStatisticsDTO.getTodayPickUp());
            hallStatisticsEntityNull.setTodayProcessed(hallStatisticsDTO.getTodayProcessed());
            hallStatisticsEntityNull.setAverageWaitingTime(hallStatisticsDTO.getAverageWaitingTime());
            hallStatisticsEntityNull.setMinWaitingTime(hallStatisticsDTO.getMinWaitingTime());
            hallStatisticsEntityNull.setMaxWaitingTime(hallStatisticsDTO.getMaxWaitingTime());
            hallStatisticsEntityNull.setTodayTotalProcessingTime(hallStatisticsDTO.getTodayTotalProcessingTime());
            hallStatisticsEntityNull.setTodayCompletionRate(hallStatisticsDTO.getTodayCompletionRate());
            hallStatisticsEntityNull.setSatisfactionRate(hallStatisticsDTO.getSatisfactionRate());
            hallStatisticsEntityNull.setApplauseRate(hallStatisticsDTO.getApplauseRate());
            hallStatisticsEntityNull.setCreateDate(new Date());
            this.insert(hallStatisticsEntityNull);
        }

    }


    // 获得窗口数量
    public int getWindowCount() {
        ResponseEntity<WindowListResponse> response =
                restTemplate.getForEntity(statisticIP+"/smartqueue/other/wins", WindowListResponse.class);

        WindowListResponse body = response.getBody();

        if (body != null && body.getCode() == 0 && body.getData() != null) {
            int count = body.getData().size();
            System.out.println("窗口数量为：" + count);
            return count;
        } else {
            System.out.println("获取窗口列表失败：" + (body != null ? body.getMsg() : "未知错误"));
            return 0;
        }
    }

    // 获得在岗工作人员数量
    public int getOnlineStaffCount() {
        ResponseEntity<WinMonitorResponse> response =
                restTemplate.getForEntity(apiIp+"/other/GetWinMonitor", WinMonitorResponse.class);

        WinMonitorResponse body = response.getBody();

        if (body != null && body.getIRet() == 0 && body.getData() != null) {
            int count = body.getData().size();
            System.out.println("当前在岗工作人员数量：" + count);
            return count;
        } else {
            System.out.println("获取在岗数据失败：" + (body != null ? body.getSMsg() : "接口错误"));
            return 0;
        }
    }

    // 获取指定日期的流水信息
    public HallStatisticsDTO computeTodayStatistics(String date) {
        String url = statisticIP+"/smartqueue/report/listrec" + "?sDate=" + date;
        ResponseEntity<FlowResponse> response = restTemplate.getForEntity(url, FlowResponse.class);
        FlowResponse body = response.getBody();

        if (body == null || body.getCode() != 0 || body.getData() == null) {
            System.out.println("接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
            return null;
        }

        List<FlowRecord> records = body.getData();

        int totalPeople = records.size();
        int completedCount = 0;
        long totalWait = 0, totalHandle = 0;
        long minWait = 0, maxWait = 0;

        int ratedCount = 0, satisfiedCount = 0;
        double appValueSum = 0;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (FlowRecord r : records) {
            try {
                Date regTime = sdf.parse(r.getS_reg_time());
                Date callTime = sdf.parse(r.getS_call_time());
                long waitMillis = callTime.getTime() - regTime.getTime();

                totalWait += waitMillis;
                if(minWait==0)
                    minWait = waitMillis;
                else
                    minWait = Math.min(minWait, waitMillis);
                maxWait = Math.max(maxWait, waitMillis);

                if (r.getS_end_time() != null && !r.getS_end_time().isEmpty()) {
                    Date endTime = sdf.parse(r.getS_end_time());
                    long handleMillis = endTime.getTime() - callTime.getTime();
                    totalHandle += handleMillis;
                    completedCount++;
                }

                if (r.getApp_value() != null && r.getApp_value() > 0) {
                    ratedCount++;
                    appValueSum += r.getApp_value();
                    if (r.getApp_value() == 1) {
                        satisfiedCount++;
                    }
                }

            } catch (Exception e) {
                e.printStackTrace(); // 可加入日志
            }
        }

        HallStatisticsDTO hallStatisticsDTO = new HallStatisticsDTO();
        hallStatisticsDTO.setTodayPickUp(totalPeople + "");
        hallStatisticsDTO.setTodayProcessed(completedCount + "");
        if(totalPeople == 0)
            hallStatisticsDTO.setAverageWaitingTime("0");
        else
            hallStatisticsDTO.setAverageWaitingTime(formatDuration(totalWait / totalPeople));
        hallStatisticsDTO.setMinWaitingTime(formatDuration(minWait));
        hallStatisticsDTO.setMaxWaitingTime(formatDuration(maxWait));
        hallStatisticsDTO.setTodayTotalProcessingTime(formatDuration(totalHandle));
        hallStatisticsDTO.setTodayCompletionRate(percent(completedCount, totalPeople));
        hallStatisticsDTO.setSatisfactionRate(ratedCount > 0 ? String.format("%.2f", appValueSum / ratedCount) : "0");
        hallStatisticsDTO.setApplauseRate(percent(satisfiedCount, ratedCount));


        // 输出统计
       /* System.out.println(" 取号人数：" + totalPeople);
        System.out.println(" 办件人数：" + completedCount);
        System.out.println(" 平均等待时长：" + formatDuration(totalWait / totalPeople));
        System.out.println("️ 最短等待：" + formatDuration(minWait));
        System.out.println(" 最长等待：" + formatDuration(maxWait));
        System.out.println(" 总办理时长：" + formatDuration(totalHandle));
        System.out.println(" 办结率：" + percent(completedCount, totalPeople));
        System.out.println(" 好评率：" + percent(satisfiedCount, ratedCount));
        System.out.println(" 满意度评分：" + (ratedCount > 0 ? String.format("%.2f", appValueSum / ratedCount) : "N/A"));
*/
        return hallStatisticsDTO;
    }

    private String formatDuration(long millis) {
        long minutes = millis / 1000 / 60;
        return minutes+"";
    }

    private String percent(int numerator, int denominator) {
        return denominator == 0 ? "0" : String.format("%.2f", 100.0 * numerator / denominator);
    }

}
