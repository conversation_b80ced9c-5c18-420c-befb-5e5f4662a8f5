package io.hmit.modules.backend.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.backend.dto.HallStatisticsWindowsDTO;
import io.hmit.modules.backend.excel.HallStatisticsWindowsExcel;
import io.hmit.modules.backend.service.HallStatisticsWindowsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>大厅统计信息-窗口</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-25
 */
@RestController
@RequestMapping("backend/hallstatisticswindows")
//@Api(tags="大厅统计信息-窗口")
public class HallStatisticsWindowsController {

    private final HallStatisticsWindowsService hallStatisticsWindowsService;

    public HallStatisticsWindowsController(HallStatisticsWindowsService hallStatisticsWindowsService) {
        this.hallStatisticsWindowsService = hallStatisticsWindowsService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("backend:hallstatisticswindows:page")
    public Result<PageData<HallStatisticsWindowsDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<HallStatisticsWindowsDTO> page = hallStatisticsWindowsService.page(params);

        return new Result<PageData<HallStatisticsWindowsDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("backend:hallstatisticswindows:info")
    public Result<HallStatisticsWindowsDTO> get(@PathVariable("id") Long id){
        HallStatisticsWindowsDTO data = hallStatisticsWindowsService.get(id);

        return new Result<HallStatisticsWindowsDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("backend:hallstatisticswindows:save")
    public Result<Object> save(@RequestBody HallStatisticsWindowsDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        hallStatisticsWindowsService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("backend:hallstatisticswindows:update")
    public Result<Object> update(@RequestBody HallStatisticsWindowsDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        hallStatisticsWindowsService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("backend:hallstatisticswindows:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        hallStatisticsWindowsService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("backend:hallstatisticswindows:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<HallStatisticsWindowsDTO> list = hallStatisticsWindowsService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, HallStatisticsWindowsExcel.class);
    }

}
