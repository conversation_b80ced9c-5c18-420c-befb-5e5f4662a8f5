package io.hmit.modules.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.backend.dao.EarlyWarningDao;
import io.hmit.modules.backend.dao.HallStatisticsDeptDao;
import io.hmit.modules.backend.dto.HallStatisticsBusinessDTO;
import io.hmit.modules.backend.dto.HallStatisticsDTO;
import io.hmit.modules.backend.dto.HallStatisticsDeptDTO;
import io.hmit.modules.backend.entity.EarlyWarningEntity;
import io.hmit.modules.backend.entity.HallStatisticsDeptEntity;
import io.hmit.modules.backend.entity.HallStatisticsEntity;
import io.hmit.modules.backend.service.HallStatisticsDeptService;
import io.hmit.modules.job.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <h1>大厅统计信息-部门 Service Impl</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-25
 */
@Slf4j
@Service
public class HallStatisticsDeptServiceImpl extends CrudServiceImpl<HallStatisticsDeptDao, HallStatisticsDeptEntity, HallStatisticsDeptDTO> implements HallStatisticsDeptService {

    private EarlyWarningDao earlyWarningDao;
    // 构造函数注入
    public HallStatisticsDeptServiceImpl(EarlyWarningDao earlyWarningDao) {
        this.earlyWarningDao = earlyWarningDao;
    }

    @Value("${HZZH.IP}")
    private String apiIp;

    @Value("${HZZH.STATISTIC_IP}")
    private String statisticIP;

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public QueryWrapper<HallStatisticsDeptEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<HallStatisticsDeptEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public HallStatisticsBusinessDTO getHallStatisticsByDepartmentIdAndDate(String departmentId, String date,String dName) {
        LambdaQueryWrapper<HallStatisticsDeptEntity> apply = new QueryWrapper<HallStatisticsDeptEntity>().lambda().eq(HallStatisticsDeptEntity::getDepartmentId, departmentId).apply("CAST(create_date AS DATE) = {0}", date);
        HallStatisticsDeptEntity hallStatisticsDeptEntity = baseDao.selectOne(apply);
        if(hallStatisticsDeptEntity==null){
            return null;
        }
        HallStatisticsBusinessDTO hallStatisticsBusinessDTO = new HallStatisticsBusinessDTO();
        if(hallStatisticsDeptEntity != null)
            BeanUtil.copyProperties(hallStatisticsDeptEntity, hallStatisticsBusinessDTO);

        // =========预警统计数据需要实时统计================
        // 获取当日的预警统计总次数
        LambdaQueryWrapper<EarlyWarningEntity> warningNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getDeptName, dName);
        hallStatisticsBusinessDTO.setWarningNum(earlyWarningDao.selectCount(warningNumWrapper).toString());
        // 获取当日的超时接待次数
        LambdaQueryWrapper<EarlyWarningEntity> cNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 2).eq(EarlyWarningEntity::getDeptName, dName);
        hallStatisticsBusinessDTO.setCNum(earlyWarningDao.selectCount(cNumWrapper));
        // 获取当日的排队过长次数
        LambdaQueryWrapper<EarlyWarningEntity> pNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 4).eq(EarlyWarningEntity::getDeptName, dName);
        hallStatisticsBusinessDTO.setPNum(earlyWarningDao.selectCount(pNumWrapper));
        // 获取当日的离岗检测提醒次数
        LambdaQueryWrapper<EarlyWarningEntity> lNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 3).eq(EarlyWarningEntity::getDeptName, dName);
        hallStatisticsBusinessDTO.setLNum(earlyWarningDao.selectCount(lNumWrapper));
        // 获取当日的差评提醒次数
        LambdaQueryWrapper<EarlyWarningEntity> hNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 1).eq(EarlyWarningEntity::getDeptName, dName);
        hallStatisticsBusinessDTO.setHNum(earlyWarningDao.selectCount(hNumWrapper));

        return hallStatisticsBusinessDTO;
    }

    //调用杭州智慧接口，生成部门的统计数据，按天数保存至表sys_hall_statistics_dept
    @Override
    public void createHallStatistics() {
        // 当天日期
        String date = DateUtil.formatDate(new Date());
        // 获得所有部门
        ResponseEntity<DepartmentListResponse> deptResponse =
                restTemplate.getForEntity(statisticIP+"/smartqueue/other/deps", DepartmentListResponse.class);
        DepartmentListResponse deptBody = deptResponse.getBody();

        if (deptBody != null && deptBody.getCode() == 0 && deptBody.getData() != null) {
            System.out.println("获取到的部门列表如下：");
            for (DepartmentInfo dep : deptBody.getData()) {


                // 部门的窗口数量
                int windowCount = getWindowCount(dep.getDep_id());
                // 部门的在岗工作人员数量
                int onlineStaffCount = getOnlineStaffCount(dep.getDep_id());

                        /*
        今日取号人数	接口返回记录总数
今日办件人数	有效的“结束时间”数量（非空）
平均等待时长	(叫号时间 - 取号时间) 平均值
最短等待时长	最小的 (叫号时间 - 取号时间)
最长等待时长	最大的 (叫号时间 - 取号时间)
总办理时长	所有 (结束时间 - 叫号时间) 的总和
办结率	办件人数 / 取号人数
好评率	满意评价人数 / 已评价人数
满意度评分	所有评价值的平均分
         */
                HallStatisticsDTO hallStatisticsDTO = computeTodayStatistics(dep.getDep_id(),DateUtil.formatDate(new Date()));


                 // 判断是否存在当天该部门的统计数据
                LambdaQueryWrapper<HallStatisticsDeptEntity> eq = new QueryWrapper<HallStatisticsDeptEntity>().lambda().apply("CAST(create_date AS DATE) = {0}", date).eq(HallStatisticsDeptEntity::getDepartmentId, dep.getDep_id());
                HallStatisticsDeptEntity hallStatisticsDeptEntity = baseDao.selectOne(eq);
                if (hallStatisticsDeptEntity == null) {
                    // 不存在，则插入数据
                    HallStatisticsDeptEntity hallStatisticsDeptNull = new HallStatisticsDeptEntity();
                    hallStatisticsDeptNull.setWindowNum(windowCount+"");
                    hallStatisticsDeptNull.setWorkerNum(onlineStaffCount+"");
                    hallStatisticsDeptNull.setTodayPickUp(hallStatisticsDTO.getTodayPickUp());
                    hallStatisticsDeptNull.setTodayProcessed(hallStatisticsDTO.getTodayProcessed());
                    hallStatisticsDeptNull.setAverageWaitingTime(hallStatisticsDTO.getAverageWaitingTime());
                    hallStatisticsDeptNull.setMinWaitingTime(hallStatisticsDTO.getMinWaitingTime());
                    hallStatisticsDeptNull.setMaxWaitingTime(hallStatisticsDTO.getMaxWaitingTime());
                    hallStatisticsDeptNull.setTodayTotalProcessingTime(hallStatisticsDTO.getTodayTotalProcessingTime());
                    hallStatisticsDeptNull.setTodayCompletionRate(hallStatisticsDTO.getTodayCompletionRate());
                    hallStatisticsDeptNull.setSatisfactionRate(hallStatisticsDTO.getSatisfactionRate());
                    hallStatisticsDeptNull.setApplauseRate(hallStatisticsDTO.getApplauseRate());
                    hallStatisticsDeptNull.setCreateDate(new Date());
                    hallStatisticsDeptNull.setDepartmentId(dep.getDep_id());
                    this.insert(hallStatisticsDeptNull);

                }else{
                    // 存在，则更新数据
                    hallStatisticsDeptEntity.setWindowNum(windowCount+"");
                    hallStatisticsDeptEntity.setWorkerNum(onlineStaffCount+"");
                    hallStatisticsDeptEntity.setTodayPickUp(hallStatisticsDTO.getTodayPickUp());
                    hallStatisticsDeptEntity.setTodayProcessed(hallStatisticsDTO.getTodayProcessed());
                    hallStatisticsDeptEntity.setAverageWaitingTime(hallStatisticsDTO.getAverageWaitingTime());
                    hallStatisticsDeptEntity.setMinWaitingTime(hallStatisticsDTO.getMinWaitingTime());
                    hallStatisticsDeptEntity.setMaxWaitingTime(hallStatisticsDTO.getMaxWaitingTime());
                    hallStatisticsDeptEntity.setTodayTotalProcessingTime(hallStatisticsDTO.getTodayTotalProcessingTime());
                    hallStatisticsDeptEntity.setTodayCompletionRate(hallStatisticsDTO.getTodayCompletionRate());
                    hallStatisticsDeptEntity.setSatisfactionRate(hallStatisticsDTO.getSatisfactionRate());
                    hallStatisticsDeptEntity.setApplauseRate(hallStatisticsDTO.getApplauseRate());
                    hallStatisticsDeptEntity.setCreateDate(new Date());
                    hallStatisticsDeptEntity.setDepartmentId(dep.getDep_id());
                    this.updateById(hallStatisticsDeptEntity);
                }

            }
        } else {
            System.out.println("接口调用失败：" + (deptBody != null ? deptBody.getMsg() : "未知错误"));
            throw new RuntimeException("接口调用失败：" + (deptBody != null ? deptBody.getMsg() : "未知错误"));
        }


    }

    private HallStatisticsDTO computeTodayStatistics(String depId, String date) {

        String url = statisticIP+"/smartqueue/report/listrec" + "?sDate=" + date;
        ResponseEntity<FlowResponse> response = restTemplate.getForEntity(url, FlowResponse.class);
        FlowResponse body = response.getBody();

        if (body == null || body.getCode() != 0 || body.getData() == null) {
            System.out.println("接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
            return null;
        }

        // 过滤部门
        List<FlowRecord> records = body.getData().stream().filter(Objects::nonNull).filter(r -> r.getDep_id() != null && r.getDep_id().equals(depId)).collect(Collectors.toList());

        int totalPeople = records.size();
        int completedCount = 0;
        long totalWait = 0, totalHandle = 0;
        long minWait = 0, maxWait = 0;

        int ratedCount = 0, satisfiedCount = 0;
        double appValueSum = 0;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (FlowRecord r : records) {
            try {
                // 检查必需的时间字段是否为空
                if (r.getS_reg_time() == null || r.getS_reg_time().trim().isEmpty() ||
                    r.getS_call_time() == null || r.getS_call_time().trim().isEmpty()) {
                    System.out.println("跳过数据记录：缺少必需的时间字段 - 取号时间: " + r.getS_reg_time() + ", 叫号时间: " + r.getS_call_time());
                    continue;
                }
                
                Date regTime = sdf.parse(r.getS_reg_time());
                Date callTime = sdf.parse(r.getS_call_time());
                long waitMillis = callTime.getTime() - regTime.getTime();

                totalWait += waitMillis;
                if(minWait==0)
                    minWait = waitMillis;
                else
                    minWait = Math.min(minWait, waitMillis);
                maxWait = Math.max(maxWait, waitMillis);

                if (r.getS_end_time() != null && !r.getS_end_time().trim().isEmpty()) {
                    Date endTime = sdf.parse(r.getS_end_time());
                    long handleMillis = endTime.getTime() - callTime.getTime();
                    totalHandle += handleMillis;
                    completedCount++;
                }

                if (r.getApp_value() != null && r.getApp_value() > 0) {
                    ratedCount++;
                    appValueSum += r.getApp_value();
                    if (r.getApp_value() == 1) {
                        satisfiedCount++;
                    }
                }

            } catch (Exception e) {
                System.out.println("处理统计数据时发生异常，跳过该记录: " + e.getMessage());
            }
        }

        HallStatisticsDTO hallStatisticsDTO = new HallStatisticsDTO();
        hallStatisticsDTO.setTodayPickUp(totalPeople + "");
        hallStatisticsDTO.setTodayProcessed(completedCount + "");
        if(totalPeople == 0)
            hallStatisticsDTO.setAverageWaitingTime("0");
        else
            hallStatisticsDTO.setAverageWaitingTime(formatDuration(totalWait / totalPeople));
        hallStatisticsDTO.setMinWaitingTime(formatDuration(minWait));
        hallStatisticsDTO.setMaxWaitingTime(formatDuration(maxWait));
        hallStatisticsDTO.setTodayTotalProcessingTime(formatDuration(totalHandle));
        hallStatisticsDTO.setTodayCompletionRate(percent(completedCount, totalPeople));
        hallStatisticsDTO.setSatisfactionRate(ratedCount > 0 ? String.format("%.2f", appValueSum / ratedCount) : "0");
        hallStatisticsDTO.setApplauseRate(percent(satisfiedCount, ratedCount));


        // 输出统计
       /* System.out.println(" 取号人数：" + totalPeople);
        System.out.println(" 办件人数：" + completedCount);
        System.out.println(" 平均等待时长：" + formatDuration(totalWait / totalPeople));
        System.out.println("️ 最短等待：" + formatDuration(minWait));
        System.out.println(" 最长等待：" + formatDuration(maxWait));
        System.out.println(" 总办理时长：" + formatDuration(totalHandle));
        System.out.println(" 办结率：" + percent(completedCount, totalPeople));
        System.out.println(" 好评率：" + percent(satisfiedCount, ratedCount));
        System.out.println(" 满意度评分：" + (ratedCount > 0 ? String.format("%.2f", appValueSum / ratedCount) : "N/A"));
*/
        return hallStatisticsDTO;

    }

    // 部门的在岗人员数量
    private int getOnlineStaffCount(String depId) {
        ResponseEntity<WinMonitorResponse> response =
                restTemplate.getForEntity(apiIp+"/other/GetWinMonitor", WinMonitorResponse.class);

        WinMonitorResponse body = response.getBody();

        if (body != null && body.getIRet() == 0 && body.getData() != null) {
           List<WinMonitorData> all = body.getData();
            List<WinMonitorData> filtered = all.stream()
                    .filter(w -> w.getDepId().equals(depId))
                    .collect(Collectors.toList());
            return filtered.size();
        } else {
            System.out.println("获取在岗数据失败：" + (body != null ? body.getSMsg() : "接口错误"));
            return 0;
        }
    }

    // 根据部门ID，获得窗口数量
    private int getWindowCount(String depId) {
        ResponseEntity<WindowListResponse> response =
                restTemplate.getForEntity(statisticIP+"/smartqueue/other/wins", WindowListResponse.class);

        WindowListResponse body = response.getBody();

        if (body != null && body.getCode() == 0 && body.getData() != null) {
            List<WindowInfo> all = body.getData();
            List<WindowInfo> filtered = all.stream()
                    .filter(w -> w.getDep_id().equals(depId))
                    .collect(Collectors.toList());

            return filtered.size();
        } else {
            System.out.println("获取窗口列表失败：" + (body != null ? body.getMsg() : "未知错误"));
            return 0;
        }
    }

    private String formatDuration(long millis) {
        long minutes = millis / 1000 / 60;
        return minutes+"";
    }

    private String percent(int numerator, int denominator) {
        return denominator == 0 ? "0" : String.format("%.2f", 100.0 * numerator / denominator);
    }
}
