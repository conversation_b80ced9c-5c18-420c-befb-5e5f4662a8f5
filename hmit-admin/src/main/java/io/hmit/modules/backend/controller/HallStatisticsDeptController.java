package io.hmit.modules.backend.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.backend.dto.HallStatisticsDeptDTO;
import io.hmit.modules.backend.excel.HallStatisticsDeptExcel;
import io.hmit.modules.backend.service.HallStatisticsDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>大厅统计信息-部门</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-25
 */
@RestController
@RequestMapping("backend/hallstatisticsdept")
//@Api(tags="大厅统计信息-部门")
public class HallStatisticsDeptController {

    private final HallStatisticsDeptService hallStatisticsDeptService;

    public HallStatisticsDeptController(HallStatisticsDeptService hallStatisticsDeptService) {
        this.hallStatisticsDeptService = hallStatisticsDeptService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("backend:hallstatisticsdept:page")
    public Result<PageData<HallStatisticsDeptDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<HallStatisticsDeptDTO> page = hallStatisticsDeptService.page(params);

        return new Result<PageData<HallStatisticsDeptDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("backend:hallstatisticsdept:info")
    public Result<HallStatisticsDeptDTO> get(@PathVariable("id") Long id){
        HallStatisticsDeptDTO data = hallStatisticsDeptService.get(id);

        return new Result<HallStatisticsDeptDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("backend:hallstatisticsdept:save")
    public Result<Object> save(@RequestBody HallStatisticsDeptDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        hallStatisticsDeptService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("backend:hallstatisticsdept:update")
    public Result<Object> update(@RequestBody HallStatisticsDeptDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        hallStatisticsDeptService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("backend:hallstatisticsdept:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        hallStatisticsDeptService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("backend:hallstatisticsdept:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<HallStatisticsDeptDTO> list = hallStatisticsDeptService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, HallStatisticsDeptExcel.class);
    }

}
