package io.hmit.modules.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.backend.dao.YuzhiDao;
import io.hmit.modules.backend.dto.YuzhiDTO;
import io.hmit.modules.backend.entity.YuzhiEntity;
import io.hmit.modules.backend.service.YuzhiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>阈值设置 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-02-20
 */
@Slf4j
@Service
public class YuzhiServiceImpl extends CrudServiceImpl<YuzhiDao, YuzhiEntity, YuzhiDTO> implements YuzhiService {

    @Override
    public QueryWrapper<YuzhiEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<YuzhiEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}