package io.hmit.modules.backend.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.hmit.common.page.PageData;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.common.utils.StringUtils;
import io.hmit.modules.backend.dao.EarlyWarningDao;
import io.hmit.modules.backend.dto.EarlyWarningDTO;
import io.hmit.modules.backend.dto.EarlyWarningIndicatorsDTO;
import io.hmit.modules.backend.dto.EarlyWarningRankingDTO;
import io.hmit.modules.backend.entity.EarlyWarningEntity;
import io.hmit.modules.backend.service.EarlyWarningService;
import io.hmit.modules.job.dto.*;
import io.hmit.common.constant.WarningThresholdConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <h1>预警信息表 Service Impl</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class EarlyWarningServiceImpl extends CrudServiceImpl<EarlyWarningDao, EarlyWarningEntity, EarlyWarningDTO> implements EarlyWarningService {


    @Value("${HZZH.IP}")
    private String apiIp;

    @Value("${HZZH.STATISTIC_IP}")
    private String statisticIP;

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public QueryWrapper<EarlyWarningEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");
        String warningTypeStr = (String) params.get("warningType");
        Integer warningType = null;
        String deptName = (String) params.get("deptName");
        String windowNo = (String) params.get("windowNo");
        String name = (String) params.get("name");
        String workNo = (String) params.get("workNo");
        String warningGrade = (String) params.get("warningGrade");
        String businessName = (String) params.get("businessName");
        String duration = (String) params.get("duration");

        // 时间筛选参数
        String seTimeStart = (String) params.get("seTimeStart");
        String seTimeEnd = (String) params.get("seTimeEnd");
        String createTimeStart = (String) params.get("createTimeStart");
        String createTimeEnd = (String) params.get("createTimeEnd");
        String appTimeStart = (String) params.get("appTimeStart");
        String appTimeEnd = (String) params.get("appTimeEnd");

        if (warningTypeStr != null) {
            try {
                warningType = Integer.parseInt(warningTypeStr);
            } catch (NumberFormatException e) {
                log.warn("Invalid warningType parameter: {}", warningTypeStr);
            }
        }

        QueryWrapper<EarlyWarningEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        wrapper.eq(warningType != null && warningType != 0, "warning_type", warningType);
        wrapper.eq(StringUtils.isNotBlank(deptName), "dept_name", deptName);
        wrapper.like(StringUtils.isNotBlank(windowNo), "window_no", windowNo);
        wrapper.like(StringUtils.isNotBlank(name), "name", name);
        wrapper.like(StringUtils.isNotBlank(workNo), "work_no", workNo);
        wrapper.eq(StringUtils.isNotBlank(warningGrade), "warning_grade", warningGrade);
        wrapper.like(StringUtils.isNotBlank(businessName), "business_name", businessName);
        wrapper.eq(StringUtils.isNotBlank(duration), "duration", duration);

        // seTime 时间范围筛选 (字符串类型，需要转换为日期比较)
        if (StringUtils.isNotBlank(seTimeStart)) {
            try {
                DateTime startDate = DateUtil.parseDateTime(seTimeStart);
                wrapper.ge("se_time", DateUtil.formatDateTime(startDate));
            } catch (Exception e) {
                log.warn("Invalid seTimeStart parameter: {}", seTimeStart);
            }
        }
        if (StringUtils.isNotBlank(seTimeEnd)) {
            try {
                DateTime endDate = DateUtil.parseDateTime(seTimeEnd);
                wrapper.le("se_time", DateUtil.formatDateTime(endDate));
            } catch (Exception e) {
                log.warn("Invalid seTimeEnd parameter: {}", seTimeEnd);
            }
        }

        // createDate 时间范围筛选
        if (StringUtils.isNotBlank(createTimeStart)) {
            try {
                DateTime startDate = DateUtil.parseDateTime(createTimeStart);
                wrapper.ge("create_date", startDate);
            } catch (Exception e) {
                log.warn("Invalid createTimeStart parameter: {}", createTimeStart);
            }
        }
        if (StringUtils.isNotBlank(createTimeEnd)) {
            try {
                DateTime endDate = DateUtil.parseDateTime(createTimeEnd);
                wrapper.le("create_date", endDate);
            } catch (Exception e) {
                log.warn("Invalid createTimeEnd parameter: {}", createTimeEnd);
            }
        }

        // appTime 时间范围筛选
        if (StringUtils.isNotBlank(appTimeStart)) {
            try {
                DateTime startDate = DateUtil.parseDateTime(appTimeStart);
                wrapper.ge("app_time", startDate);
            } catch (Exception e) {
                log.warn("Invalid appTimeStart parameter: {}", appTimeStart);
            }
        }
        if (StringUtils.isNotBlank(appTimeEnd)) {
            try {
                DateTime endDate = DateUtil.parseDateTime(appTimeEnd);
                wrapper.le("app_time", endDate);
            } catch (Exception e) {
                log.warn("Invalid appTimeEnd parameter: {}", appTimeEnd);
            }
        }

        return wrapper;
    }

    @Override
    public PageData<EarlyWarningDTO> page(Map<String, Object> params) {
        QueryWrapper<EarlyWarningEntity> wrapper = getWrapper(params);
        wrapper.orderByDesc("create_date", "se_time", "app_time");
        
        IPage<EarlyWarningEntity> page = baseDao.selectPage(
                getPage(params, null, false),
                wrapper
        );

        return getPageData(page, currentDtoClass());
    }


    @Override
    public List<EarlyWarningRankingDTO> ranking(Integer warningType) {

        List<EarlyWarningRankingDTO> result = new ArrayList<>();

        // 若warningType为1，2，3 则按照name字段分组统计数量，按照数量从大到小排列，取前10条数据
        if (warningType == 1 || warningType == 2 || warningType == 3) {
            List<EarlyWarningEntity> earlyWarningEntities = baseDao.selectList(new QueryWrapper<EarlyWarningEntity>()
                    .select("dept_name","window_no","name", "count(name) as num")
                     .eq("warning_type", warningType)
                    .groupBy("dept_name","window_no","name")
                    .orderByDesc("num")
                    .last("limit 10")
            );
            for (EarlyWarningEntity earlyWarningEntity : earlyWarningEntities) {
                EarlyWarningRankingDTO earlyWarningRankingDTO = new EarlyWarningRankingDTO();
                earlyWarningRankingDTO.setDeptName(earlyWarningEntity.getDeptName());
                earlyWarningRankingDTO.setWindowNo(earlyWarningEntity.getWindowNo());
                earlyWarningRankingDTO.setName(earlyWarningEntity.getName());
                earlyWarningRankingDTO.setNum(earlyWarningEntity.getNum());
                result.add(earlyWarningRankingDTO);
            }

        }

        // 若warningType为4 则按照dept_name、business_name字段分组统计数量，按照数量从大到小排列，取前10条数据
        if (warningType == 4) {
            List<EarlyWarningEntity> earlyWarningEntities = baseDao.selectList(new QueryWrapper<EarlyWarningEntity>()
                    .select("dept_name","business_name", "count(business_name) as num")
                    .eq("warning_type", warningType)
                    .groupBy("dept_name","business_name")
                    .orderByDesc("num")
                    .last("limit 10")
            );
            for (EarlyWarningEntity earlyWarningEntity : earlyWarningEntities) {
                EarlyWarningRankingDTO earlyWarningRankingDTO = new EarlyWarningRankingDTO();
                earlyWarningRankingDTO.setDeptName(earlyWarningEntity.getDeptName());
                earlyWarningRankingDTO.setBusinessName(earlyWarningEntity.getBusinessName());
                earlyWarningRankingDTO.setNum(earlyWarningEntity.getNum());
                result.add(earlyWarningRankingDTO);
            }
        }

        return result;

    }

    // 决策报告——预警指标
    @Override
    public EarlyWarningIndicatorsDTO indicators(Integer warningType) {

        EarlyWarningIndicatorsDTO earlyWarningIndicatorsDTO = new EarlyWarningIndicatorsDTO();

        // 超时接待次数
        Integer timeoutReception = baseDao.selectCount(new QueryWrapper<EarlyWarningEntity>()
                .eq("warning_type", 2));
        earlyWarningIndicatorsDTO.setTimeoutReceptionCount(timeoutReception);

        // 总办件量 —— 调用杭州智慧接口
        String hallUrl = statisticIP + "/smartqueue/report/hall?sStart=2025-03-01&sEnd=2050-12-30";
        ResponseEntity<HallStatResponse> hallResponse = restTemplate.getForEntity(hallUrl, HallStatResponse.class);
        HallStatResponse hallBody = hallResponse.getBody();

        if (hallBody == null || hallBody.getCode() != 0 || hallBody.getData() == null) {
            System.out.println("接口调用失败：" + (hallBody != null ? hallBody.getMsg() : "未知错误"));
            throw new RuntimeException("接口调用失败：" + (hallBody != null ? hallBody.getMsg() : "未知错误"));
        }

        Integer nTotal = hallBody.getData().getNTotal();
        List<Integer> nApp = hallBody.getData().getNApp();
        int goodReviewCount = (nApp != null && nApp.size() > 0) ? nApp.get(0) : 0;
        int totalReviews = (nApp != null) ? nApp.stream().mapToInt(Integer::intValue).sum() : 0;
        earlyWarningIndicatorsDTO.setTotalNumber(nTotal);

        // 排队过长次数
        Integer queueOverlong = baseDao.selectCount(new QueryWrapper<EarlyWarningEntity>()
                .eq("warning_type", 4));
        earlyWarningIndicatorsDTO.setQueueOverlongCount(queueOverlong);

        // 队列总长度——调用杭州智慧接口
        ResponseEntity<QueueListResponse> queueResponse =
                restTemplate.getForEntity(statisticIP+"/smartqueue/other/queues", QueueListResponse.class);
        QueueListResponse body = queueResponse.getBody();
        if (body != null && body.getCode() == 0 && body.getData() != null) {
            int count = body.getData().size();
            earlyWarningIndicatorsDTO.setQueueTotalLength(count);
        } else {
            System.out.println("接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
            throw new RuntimeException("接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }

        // 离岗检测次数
        Integer offDutyDetection = baseDao.selectCount(new QueryWrapper<EarlyWarningEntity>()
                .eq("warning_type", 3));
        earlyWarningIndicatorsDTO.setOffDutyDetectionCount(offDutyDetection);

        // 总人数——调用杭州智慧接口
        ResponseEntity<UserListResponse> userResponse = restTemplate.getForEntity(statisticIP+"/smartqueue/other/users", UserListResponse.class);
        UserListResponse userBody = userResponse.getBody();

        if (userBody != null && userBody.getCode() == 0 && userBody.getData() != null) {
            int userCount = userBody.getData().size();
            earlyWarningIndicatorsDTO.setTotalNumberOfPeople(userCount);
        } else {
            System.out.println("接口调用失败：" + (userBody != null ? userBody.getMsg() : "未知错误"));
            throw new RuntimeException("接口调用失败：" + (userBody != null ? userBody.getMsg() : "未知错误"));
        }

        return earlyWarningIndicatorsDTO;
    }

    // 排队过长
    @Override
    public void checkQueueAndAlert() {
        ResponseEntity<QueueInfoResponse> response = restTemplate.getForEntity(apiIp+"/other/GetQueueWait", QueueInfoResponse.class);
        QueueInfoResponse body = response.getBody();

        if (body != null && body.getIRet() == 0) {
            for (QueueInfo queue : body.getData()) {
                log.info("队列信息：" + queue);
                int wait = queue.getWait();
                WarningThresholdConstants.WarningLevel levelEnum = WarningThresholdConstants.getQueueWarningLevel(wait);

                if (levelEnum != null) {
                    // 报警逻辑
                    System.out.println("【报警】" + queue.getQueueName() + " 等候人数超限: " +
                            wait + "   " + levelEnum.getName());
                    // 保存至数据库
                    EarlyWarningDTO earlyWarningDTO = new EarlyWarningDTO();
                    earlyWarningDTO.setDeptName(queue.getDepName());
                    earlyWarningDTO.setWarningType(4);
                    earlyWarningDTO.setBusinessName(queue.getQueueName());
                    earlyWarningDTO.setCurrentQueueSize(queue.getWait());
                    earlyWarningDTO.setWarningGrade(levelEnum.getCode());
                    earlyWarningDTO.setStandardNumber(queue.getLimit() + "");
                    earlyWarningDTO.setWarningInfo("您好\n根据政务服务中心排队叫号系统的数据分析，目前【"+queue.getDepName()+"】的【"+queue.getQueueName()+"】取号事项等待人数已达到【"+wait+"】");
                    earlyWarningDTO.setCreateDate(new Date());
                    this.save(earlyWarningDTO);

                }
            }
        } else {
            System.out.println("接口调用失败：" + (body != null ? body.getSMsg() : "null"));
        }
    }



    // 差评提醒
    public void fetchAndSaveBadReviews() {
        ResponseEntity<BadReviewResponse> response = restTemplate.getForEntity(apiIp+"/other/GetBad", BadReviewResponse.class);
        BadReviewResponse body = response.getBody();

        if (body != null && body.getIRet() == 0) {
            for (BadReviewData review : body.getData()) {


                Integer callIdNum = baseDao.selectCount(new QueryWrapper<EarlyWarningEntity>().eq("call_id", review.getCallId()));

                if (callIdNum == 0) {
                    System.out.println("新差评信息：" + review);
                    EarlyWarningDTO dto = new EarlyWarningDTO();
                    dto.setCallId(review.getCallId());
                    dto.setDeptName(review.getDepName());
                    dto.setWindowNo(review.getWinName());
                    dto.setName(review.getUserName());
                    dto.setWorkNo(review.getUserId());
                    dto.setWarningInfo("收到差评，等级：" + review.getAppValue());
                    dto.setWarningGrade(review.getAppValue().toString()); // 自定义等级，可根据appValue映射
                    dto.setWarningType(1); // 差评提醒
                    dto.setCallNumbe(review.getCallNumber());
                    dto.setAppTime(parseDateTime(review.getAppTime()));
                    dto.setCreateDate(new Date());
                    dto.setUpdateDate(new Date());

                    this.save(dto);
                    System.out.println("【差评已入库】" + review.getUserName() + " - " + review.getCallNumber());
                }
            }
        } else {
            System.out.println("调用接口失败：" + (body != null ? body.getSMsg() : "未知错误"));
        }
    }

    private Date parseDateTime(String dateTimeStr) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateTimeStr);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 计算时间段与工作时间的重合分钟数
     * 工作时间：8:30-12:00 和 13:30-16:30
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 重合的分钟数
     */
    private static int calculateWorkHoursOverlap(Date startTime, Date endTime) {
        if (startTime == null || endTime == null || !startTime.before(endTime)) {
            return 0;
        }

        // 构建当天的工作时间段
        DateTime date = DateUtil.date(startTime);
        DateTime morningStart = DateUtil.parseDateTime(DateUtil.formatDate(date) + " 08:30:00");
        DateTime morningEnd = DateUtil.parseDateTime(DateUtil.formatDate(date) + " 12:00:00");
        DateTime afternoonStart = DateUtil.parseDateTime(DateUtil.formatDate(date) + " 13:30:00");
        DateTime afternoonEnd = DateUtil.parseDateTime(DateUtil.formatDate(date) + " 16:30:00");

        int overlapMinutes = 0;

        // 计算与上午工作时间的重合
        DateTime startDateTime = DateUtil.date(startTime);
        DateTime endDateTime = DateUtil.date(endTime);

        DateTime morningOverlapStart = startDateTime.isAfter(morningStart) ? startDateTime : morningStart;
        DateTime morningOverlapEnd = endDateTime.isBefore(morningEnd) ? endDateTime : morningEnd;
        if (morningOverlapStart.isBefore(morningOverlapEnd)) {
            overlapMinutes += (int) ((morningOverlapEnd.getTime() - morningOverlapStart.getTime()) / (1000 * 60));
        }

        // 计算与下午工作时间的重合
        DateTime afternoonOverlapStart = startDateTime.isAfter(afternoonStart) ? startDateTime : afternoonStart;
        DateTime afternoonOverlapEnd = endDateTime.isBefore(afternoonEnd) ? endDateTime : afternoonEnd;
        if (afternoonOverlapStart.isBefore(afternoonOverlapEnd)) {
            overlapMinutes += (int) ((afternoonOverlapEnd.getTime() - afternoonOverlapStart.getTime()) / (1000 * 60));
        }

        return overlapMinutes;
    }

    // 超时接待、离岗检测
    public void checkWindowStatusAndAlert() {
        ResponseEntity<WinMonitorResponse> response = restTemplate.getForEntity(apiIp+"/other/GetWinMonitor", WinMonitorResponse.class);
        WinMonitorResponse body = response.getBody();

        if (body != null && body.getIRet() == 0) {
            for (WinMonitorData win : body.getData()) {
                Date now = new Date();

                // 正在办理，超时接待
                if (win.getStatus() == 1 && win.getCallId() != 0L
                        && win.getTimelimit() != null && win.getLen() > win.getTimelimit()
                        && win.getLen() >= WarningThresholdConstants.TIMEOUT_LIGHT * 60) {

                    Integer callIdNum = baseDao.selectCount(new QueryWrapper<EarlyWarningEntity>().eq("call_id", win.getCallId()));

                    if (callIdNum == 0) {
                        Integer len = win.getLen();
                        int lenMinutes = len / 60;
                        int lenSeconds = len % 60;
                        
                        WarningThresholdConstants.WarningLevel warningLevel = WarningThresholdConstants.getTimeoutWarningLevel(lenMinutes);
                        int thresholdMinutes = 0;
                        if (warningLevel == WarningThresholdConstants.WarningLevel.LIGHT) {
                            thresholdMinutes = WarningThresholdConstants.TIMEOUT_LIGHT;
                        } else if (warningLevel == WarningThresholdConstants.WarningLevel.MEDIUM) {
                            thresholdMinutes = WarningThresholdConstants.TIMEOUT_MEDIUM;
                        } else if (warningLevel == WarningThresholdConstants.WarningLevel.SEVERE) {
                            thresholdMinutes = WarningThresholdConstants.TIMEOUT_SEVERE;
                        }
                        
                        EarlyWarningDTO dto = new EarlyWarningDTO();
                        dto.setCallId(win.getCallId());
                        dto.setDeptName(win.getDepName());
                        dto.setWindowNo(win.getWinName());
                        dto.setName(win.getUserName());
                        dto.setWorkNo(win.getUserId());
                        dto.setCallNumbe(win.getCallNumber());
                        dto.setWarningGrade(warningLevel != null ? warningLevel.getCode() : null);
                        dto.setWarningType(2); // 类型2：超时接待
                        dto.setDuration(len + "秒");
                        dto.setCreateDate(now);
                        dto.setUpdateDate(now);
                        dto.setSeTime(DateUtil.formatDateTime(DateUtil.offsetSecond(DateUtil.date(), -len)));
                        dto.setWarningInfo("接待时间已超过预设阈值" + thresholdMinutes + "分钟，当前接待时间为" + lenMinutes + "分钟" + lenSeconds + "秒。为了确保服务质量，避免客户长时间等待，请您立即采取相应措施，如增派工作人员、调整接待流程等，以提高服务效率。");


                        this.save(dto);
                        System.out.println("【超时接待预警】" + win.getUserName() + " - " + win.getCallNumber());
                    }
                }

                // 离岗检测（与工作时间重合超过阈值）
                if (win.getStatus() == 2) {
                    Date t1 = DateUtil.offsetSecond(now, -win.getLen());
                    Date t2 = now;
                    
                    int workHoursOverlapMinutes = calculateWorkHoursOverlap(t1, t2);
                    
                    if (workHoursOverlapMinutes >= WarningThresholdConstants.ABSENCE_LIGHT) {

                        Integer len = win.getLen();
                        int lenMinutes = len / 60;
                        int lenSeconds = len % 60;
                        
                        WarningThresholdConstants.WarningLevel warningLevel = WarningThresholdConstants.getAbsenceWarningLevel(workHoursOverlapMinutes);

                        EarlyWarningDTO dto = new EarlyWarningDTO();
                        dto.setDeptName(win.getDepName());
                        dto.setWindowNo(win.getWinName());
                        dto.setName(win.getUserName());
                        dto.setWorkNo(win.getUserId());
                        dto.setWarningGrade(warningLevel != null ? warningLevel.getCode() : null);
                        dto.setWarningType(3); // 离岗检测
                        dto.setDuration(len + "秒");
                        dto.setCreateDate(now);
                        dto.setUpdateDate(now);
                        dto.setSeTime(DateUtil.formatDateTime(DateUtil.offsetSecond(DateUtil.date(), -len)));
                        dto.setWarningInfo("您好！您已离开岗位超过" + lenMinutes + "分钟" + lenSeconds + "秒，请及时处理相关问题!");

                        this.save(dto);
                        System.out.println("【离岗检测预警】" + win.getUserName());

                    }
                }
            }
        } else {
            System.out.println("接口调用失败：" + (body != null ? body.getSMsg() : "未知错误"));
        }
    }

}
