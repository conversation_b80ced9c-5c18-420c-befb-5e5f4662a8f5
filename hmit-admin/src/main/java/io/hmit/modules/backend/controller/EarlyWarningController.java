package io.hmit.modules.backend.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.backend.dto.EarlyWarningDTO;
import io.hmit.modules.backend.dto.EarlyWarningIndicatorsDTO;
import io.hmit.modules.backend.dto.EarlyWarningRankingDTO;
import io.hmit.modules.backend.excel.EarlyWarningExcel;
import io.hmit.modules.backend.service.EarlyWarningService;
import io.hmit.modules.security.user.SecurityUser;
import io.hmit.modules.security.user.UserDetail;
import io.hmit.modules.sys.enums.SuperAdminEnum;
import io.hmit.modules.sys.service.SysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>预警信息表</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("backend/earlywarning")
@Api(tags="预警信息表")
public class EarlyWarningController {

    private final EarlyWarningService earlyWarningService;
    private final SysDeptService sysDeptService;

    public EarlyWarningController(EarlyWarningService earlyWarningService, SysDeptService sysDeptService) {
        this.earlyWarningService = earlyWarningService;
        this.sysDeptService = sysDeptService;
    }

    // 决策报告——预警指标分析
    @GetMapping("indicators")
    @ApiOperation("预警指标分析")
    public Result<EarlyWarningIndicatorsDTO> indicators(@RequestParam(value = "warningType",required = false) Integer warningType) {
        EarlyWarningIndicatorsDTO list = earlyWarningService.indicators(warningType);
        return new Result<EarlyWarningIndicatorsDTO>().ok(list);
    }

    // 决策报告——预警排行榜统计，参数：预警类型 warningType
    @GetMapping("ranking")
    @ApiOperation("预警排行榜统计")
    public Result<List<EarlyWarningRankingDTO>> ranking(@RequestParam("warningType") Integer warningType) {
        List<EarlyWarningRankingDTO> list = earlyWarningService.ranking(warningType);
        return new Result<List<EarlyWarningRankingDTO>>().ok(list);
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "warningType", value = "预警类型(1差评提醒；2超时接待；3离岗检测；4排队过长)", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "deptName", value = "部门", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = "windowNo", value = "窗口", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = "name", value = "姓名", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = "workNo", value = "工号", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = "warningGrade", value = "预警等级", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "businessName", value = "业务名称", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "duration", value = "时长", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "seTime", value = "起止时间(格式: yyyy-MM-dd)", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "createTime", value = "创建时间(格式: yyyy-MM-dd)", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "appTime", value = "评价时间(格式: yyyy-MM-dd)", paramType = "query", dataType="String")
    })
    //@RequiresPermissions("backend:earlywarning:page")
    public Result<PageData<EarlyWarningDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        
        String deptName = (String) params.get("deptName");
        if (deptName != null && "余姚市政务服务中心".equals(deptName.trim())) {
            params.remove("deptName");
        }
        
        PageData<EarlyWarningDTO> page = earlyWarningService.page(params);

        return new Result<PageData<EarlyWarningDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    //@RequiresPermissions("backend:earlywarning:info")
    public Result<EarlyWarningDTO> get(@PathVariable("id") Long id){
        EarlyWarningDTO data = earlyWarningService.get(id);

        return new Result<EarlyWarningDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    //RequiresPermissions("backend:earlywarning:save")
    public Result<Object> save(@RequestBody EarlyWarningDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        earlyWarningService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    //@RequiresPermissions("backend:earlywarning:update")
    public Result<Object> update(@RequestBody EarlyWarningDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        earlyWarningService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    //@RequiresPermissions("backend:earlywarning:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        earlyWarningService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    //@RequiresPermissions("backend:earlywarning:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EarlyWarningDTO> list = earlyWarningService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, EarlyWarningExcel.class);
    }

}
