package io.hmit.modules.backend.service;

import io.hmit.common.service.CrudService;
import io.hmit.modules.backend.dto.EarlyWarningDTO;
import io.hmit.modules.backend.dto.EarlyWarningIndicatorsDTO;
import io.hmit.modules.backend.dto.EarlyWarningRankingDTO;
import io.hmit.modules.backend.entity.EarlyWarningEntity;

import java.util.List;

/**
 * <h1>预警信息表 Service</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-23
 */
public interface EarlyWarningService extends CrudService<EarlyWarningEntity, EarlyWarningDTO> {

    List<EarlyWarningRankingDTO> ranking(Integer warningType);

    // 决策报告——预警指标
    EarlyWarningIndicatorsDTO indicators(Integer warningType);

    void checkQueueAndAlert();

    void fetchAndSaveBadReviews();

    void checkWindowStatusAndAlert();
}
