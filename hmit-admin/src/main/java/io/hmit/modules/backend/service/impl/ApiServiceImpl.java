package io.hmit.modules.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.backend.dao.ApiDao;
import io.hmit.modules.backend.dto.ApiDTO;
import io.hmit.modules.backend.entity.ApiEntity;
import io.hmit.modules.backend.service.ApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>api接口 Service Impl</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-26
 */
@Slf4j
@Service
public class ApiServiceImpl extends CrudServiceImpl<ApiDao, ApiEntity, ApiDTO> implements ApiService {

    @Override
    public QueryWrapper<ApiEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");
        String apiName = (String)params.get("apiName");
        String apiCname = (String)params.get("apiCname");
        String belongingSystem = (String)params.get("belongingSystem");
        Integer status = null;

        try {
            status = Integer.parseInt((String)params.get("status"));
        } catch (NumberFormatException e) {
            log.warn("Invalid status parameter: {}", params.get("status"));
        }


        QueryWrapper<ApiEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        wrapper.like(StringUtils.isNotBlank(apiName), "api_name", apiName);
        wrapper.like(StringUtils.isNotBlank(apiCname), "api_cname", apiCname);
        wrapper.like(StringUtils.isNotBlank(belongingSystem), "belonging_system", belongingSystem);
        wrapper.eq(status != null && status != 0, "status", status);

        return wrapper;
    }


}
