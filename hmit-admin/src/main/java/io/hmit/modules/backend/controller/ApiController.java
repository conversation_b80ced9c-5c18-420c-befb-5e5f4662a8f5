package io.hmit.modules.backend.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.backend.dto.ApiDTO;
import io.hmit.modules.backend.excel.ApiExcel;
import io.hmit.modules.backend.service.ApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <h1>api接口</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-26
 */
@RestController
@RequestMapping("backend/api")
@Api(tags="api接口")
public class ApiController {

    private final ApiService apiService;

    public ApiController(ApiService apiService) {
        this.apiService = apiService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "belongingSystem", value = "归属系统", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = "apiName", value = "API名称", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = "apiCname", value = "API中文名", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType="int") ,
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("backend:api:page")
    public Result<PageData<ApiDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<ApiDTO> page = apiService.page(params);

        return new Result<PageData<ApiDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("backend:api:info")
    public Result<ApiDTO> get(@PathVariable("id") Long id){
        ApiDTO data = apiService.get(id);

        return new Result<ApiDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("backend:api:save")
    public Result<Object> save(@RequestBody ApiDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        apiService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("backend:api:update")
    public Result<Object> update(@RequestBody ApiDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        dto.setUpdateDate(new Date());

        apiService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("backend:api:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        apiService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("backend:api:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<ApiDTO> list = apiService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, ApiExcel.class);
    }

}
