package io.hmit.modules.backend.service;

import io.hmit.common.service.CrudService;
import io.hmit.modules.backend.dto.HallStatisticsBusinessDTO;
import io.hmit.modules.backend.dto.HallStatisticsDTO;
import io.hmit.modules.backend.dto.HallStatisticsDeptDTO;
import io.hmit.modules.backend.entity.HallStatisticsDeptEntity;

/**
 * <h1>大厅统计信息-部门 Service</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-25
 */
public interface HallStatisticsDeptService extends CrudService<HallStatisticsDeptEntity, HallStatisticsDeptDTO> {

    HallStatisticsBusinessDTO getHallStatisticsByDepartmentIdAndDate(String departmentId, String date,String dName);

    // 调用杭州智慧接口，生成部门的统计数据，按天数保存至表sys_hall_statistics_dept
    void createHallStatistics();
}
