package io.hmit.modules.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.backend.dao.EarlyWarningDao;
import io.hmit.modules.backend.dao.HallStatisticsWindowsDao;
import io.hmit.modules.backend.dto.HallStatisticsBusinessDTO;
import io.hmit.modules.backend.dto.HallStatisticsDTO;
import io.hmit.modules.backend.dto.HallStatisticsWindowsDTO;
import io.hmit.modules.backend.entity.EarlyWarningEntity;
import io.hmit.modules.backend.entity.HallStatisticsWindowsEntity;
import io.hmit.modules.backend.service.HallStatisticsWindowsService;
import io.hmit.modules.job.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <h1>大厅统计信息-窗口 Service Impl</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-25
 */
@Slf4j
@Service
public class HallStatisticsWindowsServiceImpl extends CrudServiceImpl<HallStatisticsWindowsDao, HallStatisticsWindowsEntity, HallStatisticsWindowsDTO> implements HallStatisticsWindowsService {

    private EarlyWarningDao earlyWarningDao;
    // 构造函数注入
    public HallStatisticsWindowsServiceImpl(EarlyWarningDao earlyWarningDao) {
        this.earlyWarningDao = earlyWarningDao;
    }

    @Value("${HZZH.IP}")
    private String apiIp;

    @Value("${HZZH.STATISTIC_IP}")
    private String statisticIP;

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public QueryWrapper<HallStatisticsWindowsEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<HallStatisticsWindowsEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public HallStatisticsBusinessDTO getHallStatisticsByDepartmentIdAndWindowIdAndDate(String departmentId, String windowId, String date,String dName,String wName) {
        LambdaQueryWrapper<HallStatisticsWindowsEntity> apply = new QueryWrapper<HallStatisticsWindowsEntity>().lambda().eq(departmentId!=null,HallStatisticsWindowsEntity::getDepartmentId, departmentId).eq(HallStatisticsWindowsEntity::getWindowId, windowId).apply("CAST(create_date AS DATE) = {0}", date);
        HallStatisticsWindowsEntity hallStatisticsWindowsEntity = baseDao.selectOne(apply);
        if(hallStatisticsWindowsEntity == null) {
            return null;
        }
        HallStatisticsBusinessDTO hallStatisticsBusinessDTO = new HallStatisticsBusinessDTO();
        if(hallStatisticsWindowsEntity != null)
            BeanUtil.copyProperties(hallStatisticsWindowsEntity, hallStatisticsBusinessDTO);

        // =========预警统计数据需要实时统计================
        // 获取当日的预警统计总次数
        LambdaQueryWrapper<EarlyWarningEntity> warningNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWindowNo, wName);
        hallStatisticsBusinessDTO.setWarningNum(earlyWarningDao.selectCount(warningNumWrapper).toString());
        // 获取当日的超时接待次数
        LambdaQueryWrapper<EarlyWarningEntity> cNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 2).eq(EarlyWarningEntity::getWindowNo, wName);
        hallStatisticsBusinessDTO.setCNum(earlyWarningDao.selectCount(cNumWrapper));
        // 获取当日的排队过长次数
        LambdaQueryWrapper<EarlyWarningEntity> pNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 4).eq(EarlyWarningEntity::getWindowNo, wName);
        hallStatisticsBusinessDTO.setPNum(earlyWarningDao.selectCount(pNumWrapper));
        // 获取当日的离岗检测提醒次数
        LambdaQueryWrapper<EarlyWarningEntity> lNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 3).eq(EarlyWarningEntity::getWindowNo, wName);
        hallStatisticsBusinessDTO.setLNum(earlyWarningDao.selectCount(lNumWrapper));
        // 获取当日的差评提醒次数
        LambdaQueryWrapper<EarlyWarningEntity> hNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 1).eq(EarlyWarningEntity::getWindowNo, wName);
        hallStatisticsBusinessDTO.setHNum(earlyWarningDao.selectCount(hNumWrapper));


        return hallStatisticsBusinessDTO;
    }

    // 调用杭州智慧接口，生成窗口的统计数据，按天数保存至表sys_hall_statistics_window
    @Override
    public void createHallStatistics() {

        // 当天日期
        String date = DateUtil.formatDate(new Date());
        // 获得所有窗口
        ResponseEntity<WindowListResponse> response =
                restTemplate.getForEntity(statisticIP+"/smartqueue/other/wins", WindowListResponse.class);

        WindowListResponse body = response.getBody();

        if (body != null && body.getCode() == 0 && body.getData() != null) {

            for (WindowInfo windowInfo : body.getData()){
                // 针对窗口的统计，因此窗口数量为1
                int windowCount=1;
                // 窗口的在岗工作人员数量
                int onlineStaffCount = getOnlineStaffCount(windowInfo.getWin_id());
                 /*
        今日取号人数	接口返回记录总数
今日办件人数	有效的“结束时间”数量（非空）
平均等待时长	(叫号时间 - 取号时间) 平均值
最短等待时长	最小的 (叫号时间 - 取号时间)
最长等待时长	最大的 (叫号时间 - 取号时间)
总办理时长	所有 (结束时间 - 叫号时间) 的总和
办结率	办件人数 / 取号人数
好评率	满意评价人数 / 已评价人数
满意度评分	所有评价值的平均分
         */
                HallStatisticsDTO hallStatisticsDTO = computeTodayStatistics(windowInfo.getWin_id()+"",DateUtil.formatDate(new Date()));

                // 判断是否存在当天该窗口的统计数据
                LambdaQueryWrapper<HallStatisticsWindowsEntity> apply = new LambdaQueryWrapper<HallStatisticsWindowsEntity>()
                        .eq(HallStatisticsWindowsEntity::getDepartmentId, windowInfo.getDep_id())
                        .eq(HallStatisticsWindowsEntity::getWindowId, windowInfo.getWin_id())
                        .apply("CAST(create_date AS DATE) = {0}", date);
                HallStatisticsWindowsEntity hallStatisticsWindowsEntity = baseDao.selectOne(apply);
                if (hallStatisticsWindowsEntity == null) {
                    // 不存在，则新增数据
                    HallStatisticsWindowsEntity hallStatisticsWindowsEntityNull = new HallStatisticsWindowsEntity();
                    hallStatisticsWindowsEntityNull.setWindowNum(windowCount+"");
                    hallStatisticsWindowsEntityNull.setWorkerNum(onlineStaffCount+"");
                    hallStatisticsWindowsEntityNull.setTodayPickUp(hallStatisticsDTO.getTodayPickUp());
                    hallStatisticsWindowsEntityNull.setTodayProcessed(hallStatisticsDTO.getTodayProcessed());
                    hallStatisticsWindowsEntityNull.setAverageWaitingTime(hallStatisticsDTO.getAverageWaitingTime());
                    hallStatisticsWindowsEntityNull.setMinWaitingTime(hallStatisticsDTO.getMinWaitingTime());
                    hallStatisticsWindowsEntityNull.setMaxWaitingTime(hallStatisticsDTO.getMaxWaitingTime());
                    hallStatisticsWindowsEntityNull.setTodayTotalProcessingTime(hallStatisticsDTO.getTodayTotalProcessingTime());
                    hallStatisticsWindowsEntityNull.setTodayCompletionRate(hallStatisticsDTO.getTodayCompletionRate());
                    hallStatisticsWindowsEntityNull.setSatisfactionRate(hallStatisticsDTO.getSatisfactionRate());
                    hallStatisticsWindowsEntityNull.setApplauseRate(hallStatisticsDTO.getApplauseRate());
                    hallStatisticsWindowsEntityNull.setCreateDate(new Date());
                    hallStatisticsWindowsEntityNull.setDepartmentId(windowInfo.getDep_id());
                    hallStatisticsWindowsEntityNull.setWindowId(windowInfo.getWin_id().toString());
                    this.insert(hallStatisticsWindowsEntityNull);

                }else{
                    // 存在，则更新数据
                    hallStatisticsWindowsEntity.setWindowNum(windowCount+"");
                    hallStatisticsWindowsEntity.setWorkerNum(onlineStaffCount+"");
                    hallStatisticsWindowsEntity.setTodayPickUp(hallStatisticsDTO.getTodayPickUp());
                    hallStatisticsWindowsEntity.setTodayProcessed(hallStatisticsDTO.getTodayProcessed());
                    hallStatisticsWindowsEntity.setAverageWaitingTime(hallStatisticsDTO.getAverageWaitingTime());
                    hallStatisticsWindowsEntity.setMinWaitingTime(hallStatisticsDTO.getMinWaitingTime());
                    hallStatisticsWindowsEntity.setMaxWaitingTime(hallStatisticsDTO.getMaxWaitingTime());
                    hallStatisticsWindowsEntity.setTodayTotalProcessingTime(hallStatisticsDTO.getTodayTotalProcessingTime());
                    hallStatisticsWindowsEntity.setTodayCompletionRate(hallStatisticsDTO.getTodayCompletionRate());
                    hallStatisticsWindowsEntity.setSatisfactionRate(hallStatisticsDTO.getSatisfactionRate());
                    hallStatisticsWindowsEntity.setApplauseRate(hallStatisticsDTO.getApplauseRate());
                    hallStatisticsWindowsEntity.setCreateDate(new Date());
                    hallStatisticsWindowsEntity.setDepartmentId(windowInfo.getDep_id());
                    hallStatisticsWindowsEntity.setWindowId(windowInfo.getWin_id().toString());
                    this.updateById(hallStatisticsWindowsEntity);
                }

            }


        } else {
            System.out.println("获取窗口列表失败：" + (body != null ? body.getMsg() : "未知错误"));
            throw new RuntimeException("获取窗口列表失败：" + (body != null ? body.getMsg() : "未知错误"));
        }

    }

    private HallStatisticsDTO computeTodayStatistics(String winId, String date) {

        String url = statisticIP+"/smartqueue/report/listrec" + "?sDate=" + date;
        ResponseEntity<FlowResponse> response = restTemplate.getForEntity(url, FlowResponse.class);
        FlowResponse body = response.getBody();

        if (body == null || body.getCode() != 0 || body.getData() == null) {
            System.out.println("接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
            return null;
        }

        // 过滤窗口
        List<FlowRecord> records = body.getData().stream().filter(Objects::nonNull).filter(r -> r.getCaller_id() != null && r.getCaller_id().equals(winId)).collect(Collectors.toList());

        int totalPeople = records.size();
        int completedCount = 0;
        long totalWait = 0, totalHandle = 0;
        long minWait = 0, maxWait = 0;

        int ratedCount = 0, satisfiedCount = 0;
        double appValueSum = 0;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (FlowRecord r : records) {
            try {
                Date regTime = sdf.parse(r.getS_reg_time());
                Date callTime = sdf.parse(r.getS_call_time());
                long waitMillis = callTime.getTime() - regTime.getTime();

                totalWait += waitMillis;
                if(minWait==0)
                    minWait = waitMillis;
                else
                    minWait = Math.min(minWait, waitMillis);
                maxWait = Math.max(maxWait, waitMillis);

                if (r.getS_end_time() != null && !r.getS_end_time().isEmpty()) {
                    Date endTime = sdf.parse(r.getS_end_time());
                    long handleMillis = endTime.getTime() - callTime.getTime();
                    totalHandle += handleMillis;
                    completedCount++;
                }

                if (r.getApp_value() != null && r.getApp_value() > 0) {
                    ratedCount++;
                    appValueSum += r.getApp_value();
                    if (r.getApp_value() == 1) {
                        satisfiedCount++;
                    }
                }

            } catch (Exception e) {
                e.printStackTrace(); // 可加入日志
            }
        }

        HallStatisticsDTO hallStatisticsDTO = new HallStatisticsDTO();
        hallStatisticsDTO.setTodayPickUp(totalPeople + "");
        hallStatisticsDTO.setTodayProcessed(completedCount + "");
        if(totalPeople == 0)
            hallStatisticsDTO.setAverageWaitingTime("0");
        else
            hallStatisticsDTO.setAverageWaitingTime(formatDuration(totalWait / totalPeople));
        hallStatisticsDTO.setMinWaitingTime(formatDuration(minWait));
        hallStatisticsDTO.setMaxWaitingTime(formatDuration(maxWait));
        hallStatisticsDTO.setTodayTotalProcessingTime(formatDuration(totalHandle));
        hallStatisticsDTO.setTodayCompletionRate(percent(completedCount, totalPeople));
        hallStatisticsDTO.setSatisfactionRate(ratedCount > 0 ? String.format("%.2f", appValueSum / ratedCount) : "0");
        hallStatisticsDTO.setApplauseRate(percent(satisfiedCount, ratedCount));


        // 输出统计
       /* System.out.println(" 取号人数：" + totalPeople);
        System.out.println(" 办件人数：" + completedCount);
        System.out.println(" 平均等待时长：" + formatDuration(totalWait / totalPeople));
        System.out.println("️ 最短等待：" + formatDuration(minWait));
        System.out.println(" 最长等待：" + formatDuration(maxWait));
        System.out.println(" 总办理时长：" + formatDuration(totalHandle));
        System.out.println(" 办结率：" + percent(completedCount, totalPeople));
        System.out.println(" 好评率：" + percent(satisfiedCount, ratedCount));
        System.out.println(" 满意度评分：" + (ratedCount > 0 ? String.format("%.2f", appValueSum / ratedCount) : "N/A"));
*/
        return hallStatisticsDTO;

    }

    // 窗口在岗人员数量
    private int getOnlineStaffCount(Integer winId) {
        ResponseEntity<WinMonitorResponse> response =
                restTemplate.getForEntity(apiIp+"/other/GetWinMonitor", WinMonitorResponse.class);

        WinMonitorResponse body = response.getBody();

        if (body != null && body.getIRet() == 0 && body.getData() != null) {
            List<WinMonitorData> all = body.getData();
            List<WinMonitorData> filtered = all.stream()
                    .filter(w->w.getWinId().equals(winId))
                    .collect(Collectors.toList());
            return filtered.size();
        } else {
            System.out.println("获取在岗数据失败：" + (body != null ? body.getSMsg() : "接口错误"));
            return 0;
        }

    }

    private String formatDuration(long millis) {
        long minutes = millis / 1000 / 60;
        return minutes+"";
    }

    private String percent(int numerator, int denominator) {
        return denominator == 0 ? "0" : String.format("%.2f", 100.0 * numerator / denominator);
    }
}
