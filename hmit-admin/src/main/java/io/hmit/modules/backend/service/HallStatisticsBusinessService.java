package io.hmit.modules.backend.service;

import io.hmit.common.service.CrudService;
import io.hmit.modules.backend.dto.HallStatisticsBusinessDTO;
import io.hmit.modules.backend.entity.HallStatisticsBusinessEntity;

/**
 * <h1>大厅统计信息-业务队列 Service</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-25
 */
public interface HallStatisticsBusinessService extends CrudService<HallStatisticsBusinessEntity, HallStatisticsBusinessDTO> {

    HallStatisticsBusinessDTO getHallStatisticsByDepartmentIdAndWindowIdAndBusinessIdAndDate(String departmentId, String windowId, String businessId, String date,String dName,String wName,String bName);

    // 调用杭州智慧接口，生成队列的统计数据，按天数保存至表sys_hall_statistics_business
    void createHallStatistics();
}
