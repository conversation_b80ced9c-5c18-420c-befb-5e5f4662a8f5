package io.hmit.modules.demo.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.demo.dto.FkContentDTO;
import io.hmit.modules.demo.dto.FkContentData;
import io.hmit.modules.demo.dto.FkContentVideoDTO;
import io.hmit.modules.demo.entity.FkContentEntity;
import io.hmit.modules.demo.service.FkContentService;
import io.hmit.modules.demo.vo.FkContentVo;
import io.hmit.modules.security.user.SecurityUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <h1>知识模块内容</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-01
 */
@RestController
@RequestMapping("demo/fkcontent")
@Api(tags = "知识模块内容")
public class FkContentController {

    private final FkContentService fkContentService;

    public FkContentController(FkContentService fkContentService) {
        this.fkContentService = fkContentService;
    }

    @ApiOperation("删除内容与视频")
    @DeleteMapping("deleteContentAndVideo/{nodeId}")
    @RequiresPermissions("demo:fkcontent:delete")
    public Result<Object> deleteContentAndVideo(@PathVariable("nodeId") Long nodeId) {
        fkContentService.deleteContentAndVideo(nodeId);
        return new Result<>();
    }

    @ApiOperation("查询内容与视频")
    @GetMapping("getContentAndVideo/{nodeId}")
    @RequiresPermissions("demo:fkcontent:info")
    public Result<FkContentVideoDTO> getContentAndVideo(@PathVariable("nodeId") Long nodeId) {
        FkContentVideoDTO data = fkContentService.getContentAndVideo(nodeId);
        return new Result<FkContentVideoDTO>().ok(data);
    }

    @ApiOperation("修改内容与视频")
    @PostMapping("updateContentAndVideo")
    @RequiresPermissions("demo:fkcontent:update")
    public Result<List<Long>> updateContentAndVideo(@RequestBody FkContentVideoDTO fkContentVideoDTO) {
        List<Long> nodeIdList = fkContentVideoDTO.getNodeIdList();
        fkContentService.updateContentAndVideo(fkContentVideoDTO);
        return new Result<List<Long>>().ok(nodeIdList);
    }

    @ApiOperation("保存内容与视频")
    @PostMapping("saveContentAndVideo")
    @RequiresPermissions("demo:fkcontent:save")
    public Result<List<Long>> saveContentAndVideo(@RequestBody FkContentData fkContentData) {
        List<Long> nodeIdList = fkContentData.getNodeIdList();
        fkContentService.saveContentAndVideo(fkContentData);
        return new Result<List<Long>>().ok(nodeIdList);
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("demo:fkcontent:page")
    public Result<PageData<FkContentVo>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<FkContentVo> page = fkContentService.selectPage(params);
        return new Result<PageData<FkContentVo>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("demo:fkcontent:info")
    public Result<FkContentDTO> get(@PathVariable("id") Long id) {
        FkContentDTO data = fkContentService.get(id);
        return new Result<FkContentDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("demo:fkcontent:save")
    public Result<Object> save(@RequestBody FkContentDTO dto) {
        // 校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        Long userId = SecurityUser.getUserId();
        dto.setCreator(userId);
        dto.setCreateDate(new Date());
        fkContentService.save(dto);
        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("demo:fkcontent:update")
    public Result<Object> update(@RequestBody FkContentDTO dto) {
        // 校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        Long userId = SecurityUser.getUserId();
        dto.setUpdater(userId);
        dto.setUpdateDate(new Date());
        fkContentService.update(dto);
        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("demo:fkcontent:delete")
    public Result<Object> delete(@RequestBody Long[] ids) {
        // 校验数据
        AssertUtils.isArrayEmpty(ids, "id");
        fkContentService.delete(ids);
        return new Result<>();
    }
}