package io.hmit.modules.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.hmit.common.constant.Constant;
import io.hmit.common.exception.HmitException;
import io.hmit.common.page.PageData;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.demo.dao.FkContentDao;
import io.hmit.modules.demo.dto.FkContentDTO;
import io.hmit.modules.demo.dto.FkContentData;
import io.hmit.modules.demo.dto.FkContentVideoDTO;
import io.hmit.modules.demo.entity.FkContentEntity;
import io.hmit.modules.demo.entity.FkNodesEntity;
import io.hmit.modules.demo.entity.FkVideoEntity;
import io.hmit.modules.demo.service.FileService;
import io.hmit.modules.demo.service.FkContentService;
import io.hmit.modules.demo.service.FkNodesService;
import io.hmit.modules.demo.service.FkVideoService;
import io.hmit.modules.demo.vo.FkContentVo;
import io.hmit.modules.security.user.SecurityUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <h1>知识模块内容 Service Impl</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-01
 */
@Slf4j
@Service
public class FkContentServiceImpl extends CrudServiceImpl<FkContentDao, FkContentEntity, FkContentDTO> implements FkContentService {

    @Autowired
    private FkVideoService fkVideoService;

    @Autowired
    private FkNodesService fkNodesService;

    @Autowired
    private FileService fileService;

    @Override
    public QueryWrapper<FkContentEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<FkContentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    /**
     * 保存内容与视频
     *
     * @param fkContentData 知识模块内容与视频
     */
    @Transactional
    @Override
    public void saveContentAndVideo(FkContentData fkContentData) {
        List<Long> nodeIdList = fkContentData.getNodeIdList();
        if (nodeIdList == null || nodeIdList.size() == 0) {
            throw new HmitException("请传入正确的节点id");
        }
        Long nodeId = nodeIdList.get(nodeIdList.size() - 1);
        String content = fkContentData.getContent();
        Long userId = SecurityUser.getUserId();
        Date date = new Date();

        // 保存知识模块内容
        FkContentEntity fkContentEntity = new FkContentEntity();
        fkContentEntity.setNodeId(nodeId);
        fkContentEntity.setContent(content);
        fkContentEntity.setCreator(userId);
        fkContentEntity.setCreateDate(date);
        baseDao.insert(fkContentEntity);

        // 保存知识模块视频
        List<FkContentData.Video> videoList = fkContentData.getVideoList();
        List<FkVideoEntity> list = new ArrayList<>();
        for (FkContentData.Video video : videoList) {
            FkVideoEntity fkVideoEntity = new FkVideoEntity();
            BeanUtils.copyProperties(video, fkVideoEntity);
            fkVideoEntity.setNodeId(nodeId);
            fkVideoEntity.setCreator(userId);
            fkVideoEntity.setCreateDate(date);
            list.add(fkVideoEntity);
        }
        fkVideoService.insertBatch(list);
    }

    /**
     * 修改内容与视频
     *
     * @param fkContentVideoDTO 知识模块内容与视频完整信息
     */
    @Override
    public void updateContentAndVideo(FkContentVideoDTO fkContentVideoDTO) {
        List<Long> nodeIdList = fkContentVideoDTO.getNodeIdList();
        if (nodeIdList == null || nodeIdList.size() == 0) {
            throw new HmitException("请传入正确的节点id");
        }
        Long userId = SecurityUser.getUserId();
        Date date = new Date();

        // 修改知识模块内容
        FkContentEntity fkContentEntity = fkContentVideoDTO.getFkContentEntity();
        fkContentEntity.setUpdater(userId);
        fkContentEntity.setUpdateDate(date);
        baseDao.updateById(fkContentEntity);

        // 修改知识模块视频
        List<FkVideoEntity> fkVideoEntityList = fkContentVideoDTO.getFkVideoEntityList();
        for (FkVideoEntity video : fkVideoEntityList) {
            video.setUpdater(userId);
            video.setUpdateDate(date);
        }
        fkVideoService.updateBatchById(fkVideoEntityList);
    }

    /**
     * 查询内容与视频
     *
     * @param nodeId 节点id
     * @return 知识模块内容与视频完整信息
     */
    @Override
    public FkContentVideoDTO getContentAndVideo(Long nodeId) {
        FkContentVideoDTO fkContentVideoDTO = new FkContentVideoDTO();

        // 内容
        LambdaQueryWrapper<FkContentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FkContentEntity::getNodeId, nodeId);
        FkContentEntity fkContentEntity = baseDao.selectOne(wrapper);
        fkContentVideoDTO.setFkContentEntity(fkContentEntity);

        // 视频
        List<FkVideoEntity> fkVideoEntityList = fkVideoService.getListByNodeId(nodeId);
        fkContentVideoDTO.setFkVideoEntityList(fkVideoEntityList);

        // 节点id数组
        FkNodesEntity fkNodesEntity = fkNodesService.getById(nodeId);
        List<FkNodesEntity> allList = fkNodesService.getList();
        List<Long> list = new ArrayList<>();
        list.add(nodeId);
        List<Long> nodeIdList = getParent(list, fkNodesEntity, allList);
        Collections.reverse(nodeIdList);
        fkContentVideoDTO.setNodeIdList(nodeIdList);

        return fkContentVideoDTO;
    }

    /**
     * 查询分页
     *
     * @param params 分页参数
     * @return 结果
     */
    @Override
    public PageData<FkContentVo> selectPage(Map<String, Object> params) {
        IPage<FkContentVo> page = new Page<>(Long.parseLong((String) params.get(Constant.PAGE)), Long.parseLong((String) params.get(Constant.LIMIT)));
        IPage<FkContentVo> pageResult = baseDao.getDetailList(page, new QueryWrapper<>());
        return getPageData(pageResult, FkContentVo.class);
    }

    /**
     * 删除内容与视频
     *
     * @param nodeId 节点id
     */
    @Transactional
    @Override
    public void deleteContentAndVideo(Long nodeId) {
        // 删除内容
        LambdaQueryWrapper<FkContentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FkContentEntity::getNodeId, nodeId);
        baseDao.delete(wrapper);

        // 删除视频
        List<FkVideoEntity> fkVideoEntityList = fkVideoService.getListByNodeId(nodeId);
        List<Long> idList = new ArrayList<>();
        for (FkVideoEntity fkVideoEntity : fkVideoEntityList) {
            String videoUrl = fkVideoEntity.getVideoUrl();
            if (!StringUtils.isEmpty(videoUrl)) {
                fileService.delete(videoUrl);
            }
            String videoCover = fkVideoEntity.getVideoCover();
            if (!StringUtils.isEmpty(videoCover)) {
                fileService.delete(videoCover);
            }
            idList.add(fkVideoEntity.getId());
        }

        fkVideoService.deleteBatchIds(idList);
    }

    private List<Long> getParent(List<Long> idList, FkNodesEntity node, List<FkNodesEntity> allList) {
        FkNodesEntity cur = allList.stream().filter(root -> root.getId().equals(node.getParentId())).findFirst().get();
        Long parentId = cur.getId();
        idList.add(parentId);
        if (cur.getParentId() != 0) {
            getParent(idList, cur, allList);
        }
        return idList;
    }
}
