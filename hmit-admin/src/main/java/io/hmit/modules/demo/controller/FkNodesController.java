package io.hmit.modules.demo.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.demo.dto.FkNodesDTO;
import io.hmit.modules.demo.entity.FkNodesEntity;
import io.hmit.modules.demo.service.FkNodesService;
import io.hmit.modules.demo.vo.FkNodesVo;
import io.hmit.modules.security.user.SecurityUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <h1>知识图谱信息</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-01
 */
@RestController
@RequestMapping("demo/fknodes")
@Api(tags = "知识图谱信息")
public class FkNodesController {

    private final FkNodesService fkNodesService;

    public FkNodesController(FkNodesService fkNodesService) {
        this.fkNodesService = fkNodesService;
    }

    @GetMapping("getNodesByParentId/{parentId}")
    @ApiOperation("根据父节点id查询所有子节点")
    @RequiresPermissions("demo:fknodes:info")
    public Result<List<FkNodesEntity>> getSecondLevelNodes(@PathVariable("parentId") Long parentId) {
        List<FkNodesEntity> list = fkNodesService.getNodesByParentId(parentId);
        return new Result<List<FkNodesEntity>>().ok(list);
    }

    @GetMapping("getFirstLevelNodes")
    @ApiOperation("获取所有一级节点")
    @RequiresPermissions("demo:fknodes:info")
    public Result<List<FkNodesEntity>> getFirstLevelNodes() {
        List<FkNodesEntity> list = fkNodesService.getFirstLevelNodes();
        return new Result<List<FkNodesEntity>>().ok(list);
    }

    @GetMapping("getNestedTreeList")
    @ApiOperation("获取节点列表")
    @RequiresPermissions("demo:fknodes:info")
    public Result<List<FkNodesVo>> getNestedTreeList() {
        List<FkNodesVo> list = fkNodesService.getNestedTreeList();
        return new Result<List<FkNodesVo>>().ok(list);
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("demo:fknodes:page")
    public Result<PageData<FkNodesDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<FkNodesDTO> page = fkNodesService.page(params);
        return new Result<PageData<FkNodesDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("demo:fknodes:info")
    public Result<FkNodesDTO> get(@PathVariable("id") Long id) {
        FkNodesDTO data = fkNodesService.get(id);
        return new Result<FkNodesDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("demo:fknodes:save")
    public Result<Object> save(@RequestBody FkNodesDTO dto) {
        // 校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        Long userId = SecurityUser.getUserId();
        dto.setCreator(userId);
        dto.setCreateDate(new Date());
        fkNodesService.save(dto);
        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("demo:fknodes:update")
    public Result<Object> update(@RequestBody FkNodesDTO dto) {
        // 校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        Long userId = SecurityUser.getUserId();
        dto.setUpdater(userId);
        dto.setUpdateDate(new Date());
        fkNodesService.update(dto);
        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("demo:fknodes:delete")
    public Result<Object> delete(@RequestBody Long[] ids) {
        // 校验数据
        AssertUtils.isArrayEmpty(ids, "id");
        fkNodesService.delete(ids);
        return new Result<>();
    }
}