package io.hmit.modules.demo.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.demo.dto.CaseSceneDTO;
import io.hmit.modules.demo.service.CaseSceneService;
import io.hmit.modules.security.user.SecurityUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.Map;


/**
 * <h1>案例场景分析信息</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-06-01
 */
@RestController
@RequestMapping("demo/casescene")
@Api(tags="案例场景分析信息")
public class CaseSceneController {

    private final CaseSceneService caseSceneService;

    public CaseSceneController(CaseSceneService caseSceneService) {
        this.caseSceneService = caseSceneService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("demo:casescene:page")
    public Result<PageData<CaseSceneDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<CaseSceneDTO> page = caseSceneService.page(params);
        return new Result<PageData<CaseSceneDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("demo:casescene:info")
    public Result<CaseSceneDTO> get(@PathVariable("id") Long id){
        CaseSceneDTO data = caseSceneService.get(id);
        return new Result<CaseSceneDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("demo:casescene:save")
    public Result<Object> save(@RequestBody CaseSceneDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        Long userId = SecurityUser.getUserId();
        dto.setCreator(userId);
        dto.setCreateDate(new Date());
        caseSceneService.save(dto);
        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("demo:casescene:update")
    public Result<Object> update(@RequestBody CaseSceneDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        Long userId = SecurityUser.getUserId();
        dto.setUpdater(userId);
        dto.setUpdateDate(new Date());
        caseSceneService.update(dto);
        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("demo:casescene:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");
        caseSceneService.delete(ids);
        return new Result<>();
    }
}