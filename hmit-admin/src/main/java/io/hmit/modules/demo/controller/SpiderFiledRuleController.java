package io.hmit.modules.demo.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.demo.dto.SpiderFiledRuleDTO;
import io.hmit.modules.demo.excel.SpiderFiledRuleExcel;
import io.hmit.modules.demo.service.SpiderFiledRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1></h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-28
 */
@RestController
@RequestMapping("demo/spiderfiledrule")
@Api(tags="配置规则")
public class SpiderFiledRuleController {

    private final SpiderFiledRuleService spiderFiledRuleService;

    public SpiderFiledRuleController(SpiderFiledRuleService spiderFiledRuleService) {
        this.spiderFiledRuleService = spiderFiledRuleService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "fieldId", value = "配置字段ID", paramType = "query", dataType="long")
    })
    //@RequiresPermissions("demo:spiderfiledrule:page")
    public Result<PageData<SpiderFiledRuleDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<SpiderFiledRuleDTO> page = spiderFiledRuleService.page(params);

        return new Result<PageData<SpiderFiledRuleDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    //@RequiresPermissions("demo:spiderfiledrule:info")
    public Result<SpiderFiledRuleDTO> get(@PathVariable("id") Long id){
        SpiderFiledRuleDTO data = spiderFiledRuleService.get(id);

        return new Result<SpiderFiledRuleDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    //@RequiresPermissions("demo:spiderfiledrule:save")
    public Result<Object> save(@RequestBody SpiderFiledRuleDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        spiderFiledRuleService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    //@RequiresPermissions("demo:spiderfiledrule:update")
    public Result<Object> update(@RequestBody SpiderFiledRuleDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        spiderFiledRuleService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    //@RequiresPermissions("demo:spiderfiledrule:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        spiderFiledRuleService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("demo:spiderfiledrule:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<SpiderFiledRuleDTO> list = spiderFiledRuleService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, SpiderFiledRuleExcel.class);
    }

}
