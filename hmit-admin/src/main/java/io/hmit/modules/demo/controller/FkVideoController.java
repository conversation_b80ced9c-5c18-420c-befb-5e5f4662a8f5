package io.hmit.modules.demo.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.demo.dto.FkVideoDTO;
import io.hmit.modules.demo.service.FkVideoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * <h1>知识图谱视频</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-06-27
 */
@RestController
@RequestMapping("demo/fkvideo")
@Api(tags="知识图谱视频")
public class FkVideoController {

    private final FkVideoService fkVideoService;

    public FkVideoController(FkVideoService fkVideoService) {
        this.fkVideoService = fkVideoService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("demo:fkvideo:page")
    public Result<PageData<FkVideoDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<FkVideoDTO> page = fkVideoService.page(params);
        return new Result<PageData<FkVideoDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("demo:fkvideo:info")
    public Result<FkVideoDTO> get(@PathVariable("id") Long id){
        FkVideoDTO data = fkVideoService.get(id);
        return new Result<FkVideoDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("demo:fkvideo:save")
    public Result<Object> save(@RequestBody FkVideoDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        fkVideoService.save(dto);
        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("demo:fkvideo:update")
    public Result<Object> update(@RequestBody FkVideoDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        fkVideoService.update(dto);
        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("demo:fkvideo:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");
        fkVideoService.delete(ids);
        return new Result<>();
    }
}