package io.hmit.modules.demo.service;

import io.hmit.common.service.CrudService;
import io.hmit.modules.demo.dto.CaseSocialBenefitDTO;
import io.hmit.modules.demo.entity.CaseSocialBenefitEntity;

/**
 * <h1>案例社会效益 Service</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-25
 */
public interface CaseSocialBenefitService extends CrudService<CaseSocialBenefitEntity, CaseSocialBenefitDTO> {

    /**
     * 根据案例id查询
     *
     * @param caseId 案例id
     * @return 社会效益信息
     */
    CaseSocialBenefitEntity selectByCaseId(Long caseId);

    /**
     * 根据案例id删除
     *
     * @param caseId 案例id
     */
    void deleteByCaseId(Long caseId);
}