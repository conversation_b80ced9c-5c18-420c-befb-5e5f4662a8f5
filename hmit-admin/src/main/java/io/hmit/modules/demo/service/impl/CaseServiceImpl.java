package io.hmit.modules.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.demo.dao.CaseDao;
import io.hmit.modules.demo.dto.CaseDTO;
import io.hmit.modules.demo.dto.CaseFormData;
import io.hmit.modules.demo.entity.*;
import io.hmit.modules.demo.enums.CaseStatusEnum;
import io.hmit.modules.demo.service.*;
import io.hmit.modules.security.user.SecurityUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <h1>案例信息 Service Impl</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-01
 */
@Slf4j
@Service
public class CaseServiceImpl extends CrudServiceImpl<CaseDao, CaseEntity, CaseDTO> implements CaseService {

    @Autowired
    private CaseSceneInsightService caseSceneInsightService;

    @Autowired
    private CaseSceneService caseSceneService;

    @Autowired
    private CaseAppExpService caseAppExpService;

    @Autowired
    private CaseAppExpContentService caseAppExpContentService;

    @Autowired
    private CaseSocialBenefitService caseSocialBenefitService;

    @Autowired
    private FileService fileService;

    @Override
    public QueryWrapper<CaseEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<CaseEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    /**
     * 保存案例完整信息
     *
     * @param caseFormData 案例表单数据
     */
    @Transactional
    @Override
    public void saveAll(CaseFormData caseFormData) {
        Long userId = SecurityUser.getUserId();
        Date date = new Date();

        // 保存案例信息
        CaseEntity caseEntity = caseFormData.getCaseEntity();
        caseEntity.setStatus(CaseStatusEnum.UNPUBLISHED.getCode());
        caseEntity.setCreator(userId);
        caseEntity.setCreateDate(date);
        baseDao.insert(caseEntity);
        // 获取案例id
        Long caseId = caseEntity.getId();

        // 保存场景洞察信息
        CaseSceneInsightEntity caseSceneInsightEntity = caseFormData.getCaseSceneInsightEntity();
        caseSceneInsightEntity.setCaseId(caseId);
        caseSceneInsightEntity.setCreator(userId);
        caseSceneInsightEntity.setCreateDate(date);
        caseSceneInsightService.insert(caseSceneInsightEntity);

        // 保存场景分析信息
        CaseSceneEntity caseSceneEntity = caseFormData.getCaseSceneEntity();
        caseSceneEntity.setCaseId(caseId);
        caseSceneEntity.setCreator(userId);
        caseSceneEntity.setCreateDate(date);
        caseSceneService.insert(caseSceneEntity);

        // 保存应用体验信息
        CaseAppExpEntity caseAppExpEntity = caseFormData.getCaseAppExpEntity();
        caseAppExpEntity.setCaseId(caseId);
        caseAppExpEntity.setCreator(userId);
        caseAppExpEntity.setCreateDate(date);
        caseAppExpService.insert(caseAppExpEntity);
        // 获取应用体验id
        Long appExpId = caseAppExpEntity.getId();

        // 保存应用体验内容
        List<CaseAppExpContentEntity> caseAppExpContentList = caseFormData.getCaseAppExpContentList();
        for (CaseAppExpContentEntity caseAppExpContentEntity : caseAppExpContentList) {
            caseAppExpContentEntity.setAppExpId(appExpId);
            caseAppExpContentEntity.setCreator(userId);
            caseAppExpContentEntity.setCreateDate(date);
        }
        caseAppExpContentService.insertBatch(caseAppExpContentList);

        // 保存社会效益信息
        CaseSocialBenefitEntity socialBenefitEntity = caseFormData.getSocialBenefitEntity();
        socialBenefitEntity.setCaseId(caseId);
        socialBenefitEntity.setCreator(userId);
        socialBenefitEntity.setCreateDate(date);
        caseSocialBenefitService.insert(socialBenefitEntity);
    }

    /**
     * 修改案例信息
     *
     * @param caseFormData 案例表单数据
     */
    @Transactional
    @Override
    public void updateALl(CaseFormData caseFormData) {
        Long userId = SecurityUser.getUserId();
        Date date = new Date();

        // 修改案例信息
        CaseEntity caseEntity = caseFormData.getCaseEntity();
        caseEntity.setUpdater(userId);
        caseEntity.setUpdateDate(date);
        baseDao.updateById(caseEntity);

        // 修改场景洞察信息
        CaseSceneInsightEntity caseSceneInsightEntity = caseFormData.getCaseSceneInsightEntity();
        caseSceneInsightEntity.setUpdater(userId);
        caseSceneInsightEntity.setUpdateDate(date);
        caseSceneInsightService.updateById(caseSceneInsightEntity);

        // 修改场景分析信息
        CaseSceneEntity caseSceneEntity = caseFormData.getCaseSceneEntity();
        caseSceneEntity.setUpdater(userId);
        caseSceneEntity.setUpdateDate(date);
        caseSceneService.updateById(caseSceneEntity);

        // 修改应用体验信息
        CaseAppExpEntity caseAppExpEntity = caseFormData.getCaseAppExpEntity();
        caseAppExpEntity.setUpdater(userId);
        caseAppExpEntity.setUpdateDate(date);
        caseAppExpService.updateById(caseAppExpEntity);

        // 修改应用体验内容
        List<CaseAppExpContentEntity> caseAppExpContentList = caseFormData.getCaseAppExpContentList();
        for (CaseAppExpContentEntity caseAppExpContentEntity : caseAppExpContentList) {
            caseAppExpContentEntity.setUpdater(userId);
            caseAppExpContentEntity.setUpdateDate(date);
        }
        caseAppExpContentService.updateBatchById(caseAppExpContentList);

        // 修改社会效益信息
        CaseSocialBenefitEntity socialBenefitEntity = caseFormData.getSocialBenefitEntity();
        socialBenefitEntity.setUpdater(userId);
        socialBenefitEntity.setUpdateDate(date);
        caseSocialBenefitService.updateById(socialBenefitEntity);
    }

    /**
     * 获取案例完整信息
     *
     * @param caseId 案例id
     * @return 结果
     */
    @Override
    public CaseFormData getAll(Long caseId) {
        CaseFormData data = new CaseFormData();
        // 封装案例信息
        CaseEntity caseEntity = baseDao.selectById(caseId);
        data.setCaseEntity(caseEntity);

        // 封装场景洞察信息
        CaseSceneInsightEntity caseSceneInsightEntity = caseSceneInsightService.selectByCaseId(caseId);
        data.setCaseSceneInsightEntity(caseSceneInsightEntity);

        // 封装场景分析信息
        CaseSceneEntity caseSceneEntity = caseSceneService.selectByCaseId(caseId);
        data.setCaseSceneEntity(caseSceneEntity);

        // 封装应用体验信息
        CaseAppExpEntity caseAppExpEntity = caseAppExpService.selectByCaseId(caseId);
        data.setCaseAppExpEntity(caseAppExpEntity);

        // 封装应用体验内容
        Long appExpId = caseAppExpEntity.getId();
        List<CaseAppExpContentEntity> caseAppExpContentEntityList = caseAppExpContentService.selectByAppExpId(appExpId);
        data.setCaseAppExpContentList(caseAppExpContentEntityList);

        // 封装社会效益信息
        CaseSocialBenefitEntity socialBenefitEntity = caseSocialBenefitService.selectByCaseId(caseId);
        data.setSocialBenefitEntity(socialBenefitEntity);

        return data;
    }

    /**
     * 发布案例
     *
     * @param caseId 案例id
     */
    @Override
    public void publish(Long caseId) {
        CaseEntity caseEntity = new CaseEntity();
        caseEntity.setId(caseId);
        caseEntity.setStatus(CaseStatusEnum.PUBLISHED.getCode());

        Date date = new Date();
        Long userId = SecurityUser.getUserId();
        caseEntity.setPublishTime(date);
        caseEntity.setUpdater(userId);
        caseEntity.setUpdateDate(date);
        baseDao.updateById(caseEntity);
    }

    /**
     * 取消发布案例
     *
     * @param caseId 案例id
     */
    @Override
    public void unpublish(Long caseId) {
        CaseEntity caseEntity = new CaseEntity();
        caseEntity.setId(caseId);
        caseEntity.setStatus(CaseStatusEnum.UNPUBLISHED.getCode());
        caseEntity.setPublishTime(null);
        Long userId = SecurityUser.getUserId();
        caseEntity.setUpdater(userId);
        caseEntity.setUpdateDate(new Date());
        baseDao.updateById(caseEntity);
    }

    /**
     * [移动端]获取案例名称
     *
     * @param caseId 案例id
     * @return 案例名称
     */
    @Override
    public String anonGetCaseTitle(Long caseId) {
        return baseDao.getCaseTitle(caseId);
    }

    /**
     * 删除案例完整信息
     *
     * @param caseId 案例id
     */
    @Transactional
    @Override
    public void deleteAll(Long caseId) {
        // 删除案例信息
        CaseEntity caseEntity = baseDao.selectById(caseId);
        String imageUrl = caseEntity.getImageUrl();
        if (!StringUtils.isEmpty(imageUrl)) {
            fileService.delete(imageUrl);
        }
        baseDao.deleteById(caseId);

        // 删除场景洞察信息
        caseSceneInsightService.deleteByCaseId(caseId);

        // 删除场景分析信息
        caseSceneService.deleteByCaseId(caseId);

        // 删除应用体验信息
        CaseAppExpEntity caseAppExpEntity = caseAppExpService.selectByCaseId(caseId);
        Long appExpId = caseAppExpEntity.getId();
        caseAppExpService.deleteById(appExpId);

        // 删除应用体验内容
        List<CaseAppExpContentEntity> caseAppExpContentEntityList = caseAppExpContentService.selectByAppExpId(appExpId);
        List<Long> caseAppExpContentIdList = new ArrayList<>();
        for (CaseAppExpContentEntity caseAppExpContentEntity : caseAppExpContentEntityList) {
            String videoUrl = caseAppExpContentEntity.getVideoUrl();
            if (!StringUtils.isEmpty(videoUrl)) {
                fileService.delete(videoUrl);
            }
            String img = caseAppExpContentEntity.getImageUrl();
            if (!StringUtils.isEmpty(img)) {
                fileService.delete(img);
            }
            caseAppExpContentIdList.add(caseAppExpContentEntity.getId());
        }
        caseAppExpContentService.deleteBatchIds(caseAppExpContentIdList);

        // 删除社会效益信息
        caseSocialBenefitService.deleteByCaseId(caseId);
    }
}