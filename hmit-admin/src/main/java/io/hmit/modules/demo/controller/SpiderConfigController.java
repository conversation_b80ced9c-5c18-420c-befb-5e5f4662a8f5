package io.hmit.modules.demo.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.demo.dto.SpiderConfigDTO;
import io.hmit.modules.demo.excel.SpiderConfigExcel;
import io.hmit.modules.demo.service.SpiderConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <h1>爬虫配置</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-28
 */
@RestController
@RequestMapping("demo/spiderconfig")
@Api(tags="爬虫配置")
public class SpiderConfigController {

    private final SpiderConfigService spiderConfigService;

    public SpiderConfigController(SpiderConfigService spiderConfigService) {
        this.spiderConfigService = spiderConfigService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    //@RequiresPermissions("demo:spiderconfig:page")
    public Result<PageData<SpiderConfigDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<SpiderConfigDTO> page = spiderConfigService.page(params);

        return new Result<PageData<SpiderConfigDTO>>().ok(page);
    }

    @GetMapping("/getAddConfig")
    @ApiOperation("获取自定义Pipeline+任务结束回调")
    public Result<Map<String,Object>> getAddConfig(){
        List<String> pipelineList= new ArrayList<>();
        pipelineList.add("TestPipeline");
        List<String> spiderMissionFinishServiceList=new ArrayList<>();
        spiderMissionFinishServiceList.add("TestSpiderMissionFinish");
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("pipelineList",pipelineList);
        resultMap.put("spiderMissionFinishServiceList",spiderMissionFinishServiceList);

        return new Result<Map<String,Object>>().ok(resultMap);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    // @RequiresPermissions("demo:spiderconfig:save")
    public Result<Object> save(@RequestBody SpiderConfigDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        spiderConfigService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    //@RequiresPermissions("demo:spiderconfig:update")
    public Result<Object> update(@RequestBody SpiderConfigDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        spiderConfigService.update(dto);

        return new Result<>();
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    //@RequiresPermissions("demo:spiderconfig:info")
    public Result<SpiderConfigDTO> get(@PathVariable("id") Long id){
        SpiderConfigDTO data = spiderConfigService.get(id);

        return new Result<SpiderConfigDTO>().ok(data);
    }


    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    //@RequiresPermissions("demo:spiderconfig:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        spiderConfigService.delete(ids);

        return new Result<>();
    }

    //======over======









    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("demo:spiderconfig:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<SpiderConfigDTO> list = spiderConfigService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, SpiderConfigExcel.class);
    }

}
