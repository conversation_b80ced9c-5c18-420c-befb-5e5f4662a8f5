package io.hmit.modules.demo.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.demo.dto.SpiderMissionDTO;
import io.hmit.modules.demo.excel.SpiderMissionExcel;
import io.hmit.modules.demo.service.SpiderMissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>爬虫任务</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-28
 */
@RestController
@RequestMapping("demo/spidermission")
@Api(tags="爬虫任务")
public class SpiderMissionController {

    private final SpiderMissionService spiderMissionService;

    public SpiderMissionController(SpiderMissionService spiderMissionService) {
        this.spiderMissionService = spiderMissionService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    //@RequiresPermissions("demo:spidermission:page")
    public Result<PageData<SpiderMissionDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<SpiderMissionDTO> page = spiderMissionService.page(params);

        return new Result<PageData<SpiderMissionDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    //@RequiresPermissions("demo:spidermission:info")
    public Result<SpiderMissionDTO> get(@PathVariable("id") Long id){
        SpiderMissionDTO data = spiderMissionService.get(id);

        return new Result<SpiderMissionDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    //@RequiresPermissions("demo:spidermission:save")
    public Result<Object> save(@RequestBody SpiderMissionDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        spiderMissionService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    //@RequiresPermissions("demo:spidermission:update")
    public Result<Object> update(@RequestBody SpiderMissionDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        spiderMissionService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    //@RequiresPermissions("demo:spidermission:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        spiderMissionService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("demo:spidermission:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<SpiderMissionDTO> list = spiderMissionService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, SpiderMissionExcel.class);
    }

}
