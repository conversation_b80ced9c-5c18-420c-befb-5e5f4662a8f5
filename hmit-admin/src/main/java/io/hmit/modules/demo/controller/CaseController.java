package io.hmit.modules.demo.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.demo.dto.CaseDTO;
import io.hmit.modules.demo.dto.CaseFormData;
import io.hmit.modules.demo.service.CaseService;
import io.hmit.modules.security.user.SecurityUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.Map;


/**
 * <h1>案例信息</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-01
 */
@RestController
@RequestMapping("demo/case")
@Api(tags = "案例信息")
public class CaseController {

    private final CaseService caseService;

    public CaseController(CaseService caseService) {
        this.caseService = caseService;
    }

    @PutMapping("unpublish/{caseId}")
    @ApiOperation("取消发布案例")
    @RequiresPermissions("demo:case:update")
    public Result<Object> unpublish(@PathVariable("caseId") Long caseId) {
        caseService.unpublish(caseId);
        return new Result<>();
    }

    @PutMapping("publish/{caseId}")
    @ApiOperation("发布案例")
    @RequiresPermissions("demo:case:update")
    public Result<Object> publish(@PathVariable("caseId") Long caseId) {
        caseService.publish(caseId);
        return new Result<>();
    }

    @GetMapping("anonGetCaseTitle/{caseId}")
    @ApiOperation("[移动端]获取案例名称")
    public Result<String> anonGetCaseTitle(@PathVariable("caseId") Long caseId) {
        String title = caseService.anonGetCaseTitle(caseId);
        return new Result<String>().ok(title);
    }

    @DeleteMapping("deleteAll/{caseId}")
    @ApiOperation("删除案例完整信息")
    @RequiresPermissions("demo:case:delete")
    public Result<Object> deleteAll(@PathVariable("caseId") Long caseId) {
        caseService.deleteAll(caseId);
        return new Result<>();
    }

    @GetMapping("getAll/{caseId}")
    @ApiOperation("获取案例完整信息")
    @RequiresPermissions("demo:case:info")
    public Result<CaseFormData> getAll(@PathVariable("caseId") Long caseId) {
        CaseFormData data = caseService.getAll(caseId);
        return new Result<CaseFormData>().ok(data);
    }

    @PutMapping("updateAll")
    @ApiOperation("修改案例信息")
    @RequiresPermissions("demo:case:update")
    public Result<Object> updateAll(@RequestBody CaseFormData caseFormData) {
        caseService.updateALl(caseFormData);
        return new Result<>();
    }

    @PostMapping("saveAll")
    @ApiOperation("保存案例完整信息")
    @RequiresPermissions("demo:case:save")
    public Result<Object> saveAll(@RequestBody CaseFormData caseFormData) {
        caseService.saveAll(caseFormData);
        return new Result<>();
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("demo:case:page")
    public Result<PageData<CaseDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<CaseDTO> page = caseService.page(params);
        return new Result<PageData<CaseDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("demo:case:info")
    public Result<CaseDTO> get(@PathVariable("id") Long id) {
        CaseDTO data = caseService.get(id);
        return new Result<CaseDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("demo:case:save")
    public Result<Object> save(@RequestBody CaseDTO dto) {
        // 校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        Long userId = SecurityUser.getUserId();
        dto.setCreator(userId);
        dto.setCreateDate(new Date());
        caseService.save(dto);
        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("demo:case:update")
    public Result<Object> update(@RequestBody CaseDTO dto) {
        // 校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        Long userId = SecurityUser.getUserId();
        dto.setUpdater(userId);
        dto.setUpdateDate(new Date());
        caseService.update(dto);
        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("demo:case:delete")
    public Result<Object> delete(@RequestBody Long[] ids) {
        // 校验数据
        AssertUtils.isArrayEmpty(ids, "id");
        caseService.delete(ids);
        return new Result<>();
    }
}