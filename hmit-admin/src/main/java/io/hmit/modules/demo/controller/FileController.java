package io.hmit.modules.demo.controller;

import io.hmit.common.exception.ErrorCode;
import io.hmit.common.utils.Result;
import io.hmit.modules.demo.service.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 文件相关接口
 *
 * <AUTHOR>
 * @date 2023/6/12 10:26
 */
@RestController
@RequestMapping("demo/file")
@Api(tags = "文件相关接口")
public class FileController {

    @Autowired
    private FileService fileService;

    /**
     * 文件上传
     *
     * @param file 文件
     * @return 文件路径
     */
    @ApiOperation(value = "上传文件")
    @PostMapping("/upload")
    public Result<Map<String, Object>> upload(@ApiParam(name = "file", value = "文件", required = true)
                                              @RequestParam("file") MultipartFile file,
                                              HttpServletRequest request) {
        if (file.isEmpty()) {
            return new Result<Map<String, Object>>().error(ErrorCode.UPLOAD_FILE_EMPTY);
        }
        Map<String, Object> map = fileService.upload(file, request);
        return new Result<Map<String, Object>>().ok(map);
    }

    @ApiOperation(value = "删除文件")
    @DeleteMapping("/delete")
    public Result<Object> delete(@RequestParam("url") String url) {
        if (url == null) {
            return new Result<>().error();
        }
        return fileService.delete(url) ? new Result<>().ok("删除成功") : new Result<>().error();
    }
}
