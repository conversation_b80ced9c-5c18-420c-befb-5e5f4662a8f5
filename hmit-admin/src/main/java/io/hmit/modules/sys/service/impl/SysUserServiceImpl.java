package io.hmit.modules.sys.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.hmit.common.constant.Constant;
import io.hmit.common.exception.HmitException;
import io.hmit.common.page.PageData;
import io.hmit.common.service.impl.BaseServiceImpl;
import io.hmit.common.utils.ConvertUtils;
import io.hmit.common.utils.HzzhHttpUtils;
import io.hmit.common.utils.sm.sm4.SM4Utils;
import io.hmit.model.DingResponse;
import io.hmit.modules.qh.dto.ApiResponseDTO;
import io.hmit.modules.qh.dto.UserRequestDTO;
import io.hmit.modules.qh.dto.UserResponseDTO;
import io.hmit.modules.qh.dto.UserUpdateRequestDTO;
import io.hmit.modules.qh.service.HzzhTokenService;
import io.hmit.modules.security.password.PasswordUtils;
import io.hmit.modules.security.user.SecurityUser;
import io.hmit.modules.security.user.UserDetail;
import io.hmit.modules.sys.dao.SysUserDao;
import io.hmit.modules.sys.dto.SysRoleDTO;
import io.hmit.modules.sys.dto.SysUserDTO;
import io.hmit.modules.sys.entity.SysUserEntity;
import io.hmit.modules.sys.enums.SuperAdminEnum;
import io.hmit.modules.sys.service.*;
import io.hmit.service.DingPlusService;
import io.hmit.modules.backend.dao.EarlyWarningDao;
import io.hmit.modules.backend.entity.EarlyWarningEntity;
import io.hmit.modules.job.dto.WinMonitorData;
import io.hmit.modules.job.dto.WinMonitorResponse;
import io.hmit.modules.job.dto.FlowRecord;
import io.hmit.modules.job.dto.FlowResponse;
import io.hmit.modules.backend.dto.HallStatisticsDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;
import cn.hutool.core.date.DateUtil;


/**
 * 系统用户
 *
 * <AUTHOR>
@Service
public class SysUserServiceImpl extends BaseServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {

    @Value("${ding.tenantId}")
    private String TENANT_ID;

    @Value("${HZZH.IP}")
    private String apiIp;

    @Value("${HZZH.STATISTIC_IP}")
    private String statisticIP;

    private final RestTemplate restTemplate;

    private final SysRoleUserService sysRoleUserService;

    private final SysDeptService sysDeptService;

    private final DingPlusService dingPlusService;

    private final HzzhTokenService hzzhTokenService;

    private final SysRoleService sysRoleService;
    private final SysParamsService sysParamsService;
    private final EarlyWarningDao earlyWarningDao;

    public SysUserServiceImpl(SysParamsService sysParamsService,SysRoleUserService sysRoleUserService, SysDeptService sysDeptService, DingPlusService dingPlusService,HzzhTokenService hzzhTokenService, SysRoleService sysRoleService, EarlyWarningDao earlyWarningDao, RestTemplate restTemplate) {
        this.sysRoleUserService = sysRoleUserService;
        this.sysDeptService = sysDeptService;
        this.dingPlusService = dingPlusService;
        this.hzzhTokenService = hzzhTokenService;
        this.sysRoleService = sysRoleService;
        this.sysParamsService = sysParamsService;
        this.earlyWarningDao = earlyWarningDao;
        this.restTemplate = restTemplate;
    }

    @Override
    public PageData<SysUserDTO> page(Map<String, Object> params) {

        //转换成like
        paramsToLike(params, "username");
        paramsToLike(params, "realName");


        //分页
        IPage<SysUserEntity> page = getPage(params, Constant.CREATE_DATE, false);

        //普通管理员，只能查询所属部门及子部门的数据
        UserDetail user = SecurityUser.getUser();
        if (user.getSuperAdmin() == SuperAdminEnum.NO.value()) {
            params.put("deptIdList", sysDeptService.getSubDeptIdList(user.getDeptId()));
        }

        //查询
        List<SysUserEntity> list = baseDao.getList(params);

        return getPageData(list, page.getTotal(), SysUserDTO.class);
    }

    @Override
    public List<SysUserDTO> list(Map<String, Object> params) {

        //普通管理员，只能查询所属部门及子部门的数据
        UserDetail user = SecurityUser.getUser();
        if (user.getSuperAdmin() == SuperAdminEnum.NO.value()) {
            params.put("deptIdList", sysDeptService.getSubDeptIdList(user.getDeptId()));
        }
        // 根据角色 更新查询条件
        updateParamsForRole(params);

        List<SysUserEntity> entityList = baseDao.getList(params);

        return ConvertUtils.sourceToTarget(entityList, SysUserDTO.class);
    }

    @Override
    public List<SysUserDTO> listForQueue(Map<String, Object> params) {

        List<SysUserEntity> sysUserEntities = baseDao.listForQueue(params);
        return ConvertUtils.sourceToTarget(sysUserEntities, SysUserDTO.class);
    }

    @Override
    public SysUserDTO get(Long id) {

        SysUserEntity entity = baseDao.getById(id);

        return ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
    }

    @Override
    public SysUserDTO getByUsername(String username) {

        SysUserEntity entity = baseDao.getByUsername(username);
        return ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
    }

    @Override
    public SysUserDTO getByWorkNo(String workNo) {
        QueryWrapper<SysUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("work_no", workNo);
        SysUserEntity entity = baseDao.selectOne(queryWrapper);
        return ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SysUserDTO dto) {

        SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);

        //密码加密
        String password = PasswordUtils.encode(entity.getPassword());
        entity.setPassword(password);

        //保存用户
        entity.setSuperAdmin(SuperAdminEnum.NO.value());
        insert(entity);

        // 尝试更新用户浙政钉信息
        if (!ObjectUtils.isEmpty(dto.getMobile())) {
            this.bindUserInfo(dto.getMobile());
        }

        //保存角色用户关系
        sysRoleUserService.saveOrUpdate(entity.getId(), dto.getRoleIdList());
    }

    @Override
    public void saveForWorker(SysUserDTO dto) {
        String hzzhToken = hzzhTokenService.createHzzhToken();
        String hzzhUrl = sysParamsService.getValue("HzzhUrl");

        UserRequestDTO userData = new UserRequestDTO();
        userData.setNRole(0);
        userData.setNUserType(0);
        userData.setSDepId(sysDeptService.get(dto.getDeptId()).getCode());
        userData.setSMobilePhone(dto.getMobile());
        userData.setSPassword(MD5.create().digestHex(dto.getPassword()));
        userData.setSPost(dto.getDuties());
        userData.setSUserId(dto.getWorkNo());
        userData.setSUserImg("");
        userData.setSUserName(dto.getRealName());

        ApiResponseDTO apiResponseDTO = HzzhHttpUtils.sendSave(hzzhUrl, "/smartqueue/User/AddUser", userData, hzzhToken);

        if(0==apiResponseDTO.getCode()){
            SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);

            //密码加密
            String password = PasswordUtils.encode(entity.getPassword());
            entity.setPassword(password);

            //保存用户
            entity.setSuperAdmin(SuperAdminEnum.NO.value());
            insert(entity);

            // 尝试更新用户浙政钉信息
//        if (!ObjectUtils.isEmpty(dto.getMobile())) {
//            this.bindUserInfo(dto.getMobile());
//        }

            //保存角色用户关系
            sysRoleUserService.saveOrUpdate(entity.getId(), dto.getRoleIdList());
        }

    }

    @Override
    @Transactional
    public void getAllUserInsert() {
        String hzzhToken = hzzhTokenService.createHzzhToken();
        String hzzhUrl = sysParamsService.getValue("HzzhUrl");
        ApiResponseDTO apiResponseDTO = HzzhHttpUtils.sendGetAllPost(hzzhUrl, "/smartqueue/User/GetUsers", hzzhToken);
        List<UserResponseDTO> userResponseDTOS = JSON.parseArray(JSON.toJSONString(apiResponseDTO.getData()), UserResponseDTO.class);
        List<Long> roleList = new ArrayList<>();

        roleList.add(1930809927180087297l);
        for (UserResponseDTO userResponseDTO : userResponseDTOS) {

            SysUserDTO sysUserDTO = new SysUserDTO();
            sysUserDTO.setStatus(1);
            sysUserDTO.setQueueStatus(1);
            sysUserDTO.setSuperAdmin(0);
            sysUserDTO.setUsername(userResponseDTO.getUser_name()+userResponseDTO.getUser_id());
            sysUserDTO.setDeptId(Long.parseLong(userResponseDTO.getDep_id()));
            sysUserDTO.setRealName(userResponseDTO.getUser_name());
            sysUserDTO.setWorkNo(userResponseDTO.getUser_id());
//            sysUserDTO.setMobile(userResponseDTO.getPhone());
            sysUserDTO.setWindowNo(userResponseDTO.getUserpost());
            sysUserDTO.setPassword(PasswordUtils.encode(userResponseDTO.getUser_id()+"Yy@2025"));
            sysUserDTO.setRoleIdList(roleList);
            SysUserEntity sysUserEntity = ConvertUtils.sourceToTarget(sysUserDTO, SysUserEntity.class);
            insert(sysUserEntity);
            sysRoleUserService.saveOrUpdate(sysUserEntity.getId(), sysUserDTO.getRoleIdList());
        }

    }

    @Override
    public void getWindowUserInsert() {
        List<SysUserEntity> windowUserInsert = baseDao.getWindowUserInsert();
        List<Long> roleList = new ArrayList<>();

        roleList.add(1942472419819769857l);
        for (SysUserEntity sysUserEntity : windowUserInsert) {
            SysUserDTO sysUserDTO = new SysUserDTO();
            sysUserDTO.setStatus(1);
            sysUserDTO.setQueueStatus(0);
            sysUserDTO.setSuperAdmin(0);
            sysUserDTO.setUsername(sysUserEntity.getUsername());
            sysUserDTO.setDeptId(sysUserEntity.getDeptId());
            sysUserDTO.setRealName(sysUserEntity.getUsername());
            sysUserDTO.setMobile(sysUserEntity.getMobile());
            sysUserDTO.setPassword(PasswordUtils.encode("Yuyao@2025"));
            sysUserDTO.setRoleIdList(roleList);
            SysUserEntity sysUserEntity2 = ConvertUtils.sourceToTarget(sysUserDTO, SysUserEntity.class);
            insert(sysUserEntity2);
            sysRoleUserService.saveOrUpdate(sysUserEntity2.getId(), sysUserDTO.getRoleIdList());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysUserDTO dto) {

        SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);

        // 如果用户手机号发生变化同步更新浙政钉信息
        try {
            SysUserDTO oldSysUser = get(dto.getId());
            if (!oldSysUser.getMobile().equals(dto.getMobile())) {
                this.unbindUserInfo(dto.getId());
                this.bindUserInfo(dto.getMobile());
            }
        } catch (Exception ignore) {}

        //密码加密
        if (StringUtils.isBlank(dto.getPassword())) {
            entity.setPassword(null);
        } else {
            String password = PasswordUtils.encode(entity.getPassword());
            entity.setPassword(password);
        }

        //更新用户
        updateById(entity);

        //更新角色用户关系
        sysRoleUserService.saveOrUpdate(entity.getId(), dto.getRoleIdList());
    }

    @Override
    public void updateForWorker(SysUserDTO dto) {

        String hzzhToken = hzzhTokenService.createHzzhToken();
        String hzzhUrl = sysParamsService.getValue("HzzhUrl");
        ApiResponseDTO apiResponseDTO1 = HzzhHttpUtils.sendGetOne(hzzhUrl, "/smartqueue/User/GetUser", dto.getWorkNo(), hzzhToken);
        if(0==apiResponseDTO1.getCode()){
            UserUpdateRequestDTO userUpdateRequestDTO = new UserUpdateRequestDTO();
            userUpdateRequestDTO.setBEditImg(false);
            userUpdateRequestDTO.setSOldUserId(get(dto.getId()).getWorkNo());
            UserRequestDTO userData = new UserRequestDTO();
            userData.setNRole(0);                     // 角色（假设 1 表示管理员）
            userData.setNUserType(0);                 // 用户类型（假设 2 表示普通用户）
            userData.setSDepId(sysDeptService.get(dto.getDeptId()).getCode());             // 部门ID
            userData.setSMobilePhone(dto.getMobile());  // 手机号
            userData.setSPassword(MD5.create().digestHex(dto.getPassword()));      // 密码
            userData.setSPost(dto.getDuties());             // 职位
            userData.setSUserId(dto.getWorkNo());          // 用户ID
            userData.setSUserImg("");       // 用户头像路径/URL
            userData.setSUserName(dto.getRealName());
            userUpdateRequestDTO.setUserRequestDTO(userData);

            ApiResponseDTO apiResponseDTO = HzzhHttpUtils.sendSave(hzzhUrl, "/smartqueue/User/EditUser", userUpdateRequestDTO, hzzhToken);

            if(0==apiResponseDTO.getCode()){
                SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);

                // 如果用户手机号发生变化同步更新浙政钉信息
                try {
                    SysUserDTO oldSysUser = get(dto.getId());
                    if (!oldSysUser.getMobile().equals(dto.getMobile())) {
                        this.unbindUserInfo(dto.getId());
                        this.bindUserInfo(dto.getMobile());
                    }
                } catch (Exception ignore) {}

                //密码加密
                if (StringUtils.isBlank(dto.getPassword())) {
                    entity.setPassword(null);
                } else {
                    String password = PasswordUtils.encode(entity.getPassword());
                    entity.setPassword(password);
                }

                //更新用户
                updateById(entity);

                //更新角色用户关系
                sysRoleUserService.saveOrUpdate(entity.getId(), dto.getRoleIdList());

            }
        }

    }

    @Override
    public void delete(Long[] ids) {

        //删除用户
        baseDao.deleteBatchIds(Arrays.asList(ids));

        //删除角色用户关系
        sysRoleUserService.deleteByUserIds(ids);
    }

    @Override
    public void deleteForWorker(Long[] ids) {
        String hzzhToken = hzzhTokenService.createHzzhToken();
        String hzzhUrl = sysParamsService.getValue("HzzhUrl");

        List<Long> successIds = new ArrayList<>();
        for (Long id : ids) {
            SysUserDTO sysUserDTO = get(id);

            ApiResponseDTO apiResponseDTO = HzzhHttpUtils.sendDelete(hzzhUrl, "/smartqueue/Dep/DelDep", sysUserDTO.getWorkNo(), hzzhToken);

            if(0==apiResponseDTO.getCode()){
                successIds.add(id);
            }
        }
        Long[] longs = successIds.toArray(new Long[0]);

        //删除用户
        baseDao.deleteBatchIds(successIds);

        //删除角色用户关系
        sysRoleUserService.deleteByUserIds(longs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long id, String newPassword) {

        newPassword = PasswordUtils.encode(newPassword);

        baseDao.updatePassword(id, newPassword);
    }

    @Override
    public int getCountByDeptId(Long deptId) {

        return baseDao.getCountByDeptId(deptId);
    }

    /**
     * <h2>根据 accountId 查询用户</h2>
     */
    @Override
    public SysUserDTO selectByAccountId(String accountId) {
        SysUserEntity sysUserEntity = baseDao.selectOne(new QueryWrapper<SysUserEntity>().eq("account_id", accountId));
        if (ObjectUtils.isEmpty(sysUserEntity)) { // 尝试 SM4 加密后查询
            sysUserEntity = baseDao.selectOne(new QueryWrapper<SysUserEntity>().eq("account_id", SM4Utils.encryptData_CBC(accountId)));
        }
        if (ObjectUtils.isEmpty(sysUserEntity) && sysUserEntity.getStatus().equals(Constant.FAIL)) { // 账号停用抛出异常
            throw new HmitException("账号被停用，请联系管理员");
        }
        return ConvertUtils.sourceToTarget(sysUserEntity, SysUserDTO.class);
    }

    /**
     * <h2>根据 mobile 查询用户</h2>
     */
    @Override
    public SysUserDTO selectByMobile(String mobile) {
        SysUserEntity sysUserEntity = baseDao.selectOne(new QueryWrapper<SysUserEntity>().eq("mobile", mobile));
        if (ObjectUtils.isEmpty(sysUserEntity)) { // 尝试 SM4 加密后查询
            sysUserEntity = baseDao.selectOne(new QueryWrapper<SysUserEntity>().eq("mobile", SM4Utils.encryptData_CBC(mobile)));
        }
        if (ObjectUtils.isEmpty(sysUserEntity) && sysUserEntity.getStatus().equals(Constant.FAIL)) { // 账号停用抛出异常
            throw new HmitException("账号被停用，请联系管理员");
        }
        return ConvertUtils.sourceToTarget(sysUserEntity, SysUserDTO.class);
    }

    /**
     * <h2>根据手机号码获取浙政钉信息并绑定用户信息</h2>
     */
    @Override
    @Transactional
    public SysUserDTO bindUserInfo(String mobile) {
        // 根据手机号码获取浙政钉信息
        DingResponse dingResponse = dingPlusService.getUserInfoByMobile("86", TENANT_ID, "local", mobile);
        if (ObjectUtils.isEmpty(dingResponse)) {
            throw new HmitException("无法连接到浙政钉");
        } else if (!dingResponse.isSuccess()) {
            throw new HmitException(dingResponse.getErrorMsg());
        }
        SysUserDTO sysUserDTO = selectByMobile(mobile);
        if (!sysUserDTO.getRealName().equals(dingResponse.getContent().getData().get("employeeName").toString())) {
            throw new HmitException("绑定账号时本地用户和浙政钉用户姓名不一致，绑定失败");
        }
        sysUserDTO.setAccountId(SM4Utils.encryptData_CBC(dingResponse.getContent().getData().get("accountId").toString()));
        sysUserDTO.setEmployeeCode(dingResponse.getContent().getData().get("employeeCode").toString());
        Object govEmpAvatar = dingResponse.getContent().getData().get("govEmpAvatar");
        sysUserDTO.setGovEmpAvatar(ObjectUtils.isEmpty(govEmpAvatar) ? "" : govEmpAvatar.toString());
        update(sysUserDTO);
        return sysUserDTO;
    }

    @Override
    public String bindUserInfoTask(String mobile) {
        // 根据手机号码获取浙政钉信息
        DingResponse dingResponse = dingPlusService.getUserInfoByMobile("86", TENANT_ID, "local", mobile);
        if (ObjectUtils.isEmpty(dingResponse)) {
//            throw new HmitException("无法连接到浙政钉");
            return "无法连接到浙政钉";
        } else if (!dingResponse.getContent().isSuccess()) {
//            throw new HmitException(dingResponse.getErrorMsg());
            return mobile+dingResponse.getContent().getResponseMessage();
        }
        SysUserDTO sysUserDTO = selectByMobile(mobile);
        if (!sysUserDTO.getRealName().equals(dingResponse.getContent().getData().get("employeeName").toString())) {
//            throw new HmitException("绑定账号时本地用户和浙政钉用户姓名不一致，绑定失败");
            return "绑定账号"+mobile+"时本地用户和浙政钉用户姓名不一致，绑定失败";
        }
        sysUserDTO.setAccountId(SM4Utils.encryptData_CBC(dingResponse.getContent().getData().get("accountId").toString()));
        sysUserDTO.setEmployeeCode(dingResponse.getContent().getData().get("employeeCode").toString());
        Object govEmpAvatar = dingResponse.getContent().getData().get("govEmpAvatar");
        sysUserDTO.setGovEmpAvatar(ObjectUtils.isEmpty(govEmpAvatar) ? "" : govEmpAvatar.toString());
        update(sysUserDTO);
        return sysUserDTO.getMobile()+"绑定成功";
    }

    /**
     * <h2>解绑用户信息</h2>
     */
    @Override
    @Transactional
    public void unbindUserInfo(Long userId) {
        SysUserDTO sysUserDTO = get(userId);
        sysUserDTO.setAccountId("");
        sysUserDTO.setEmployeeCode("");
        sysUserDTO.setTenantUserId("");
        sysUserDTO.setHeadUrl("");
        update(sysUserDTO);
    }

    @Override
    public List<SysUserDTO> listByDeptId(String deptId) {
//        LambdaQueryWrapper<SysUserEntity> eq = new QueryWrapper<SysUserEntity>().lambda().eq(SysUserEntity::getDeptId, deptId).isNotNull(SysUserEntity::getWindowId).eq(SysUserEntity::getStatus, 1);
        LambdaQueryWrapper<SysUserEntity> eq = new QueryWrapper<SysUserEntity>().lambda().eq(SysUserEntity::getDeptId, deptId).isNotNull(SysUserEntity::getWindowNo).eq(SysUserEntity::getStatus, 1).orderByDesc(SysUserEntity::getWindowNo);
        List<SysUserEntity> sysUserEntities = baseDao.selectList(eq);

        List<SysUserDTO> result = BeanUtil.copyToList(sysUserEntities, SysUserDTO.class);

        ResponseEntity<WinMonitorResponse> response =
                restTemplate.getForEntity(apiIp + "/other/GetWinMonitor", WinMonitorResponse.class);

        WinMonitorResponse body = response.getBody();
        List<WinMonitorData> winMonitorDataList = null;
        if (body != null && body.getIRet() == 0 && body.getData() != null) {
            winMonitorDataList = body.getData();
        } else {
            System.out.println("获取在岗数据失败：" + (body != null ? body.getSMsg() : "接口错误"));
        }

        for (SysUserDTO sysUserDTO : result) {
            String workNo = sysUserDTO.getWorkNo();
            if (StringUtils.isNotBlank(workNo)) {
                LambdaQueryWrapper<EarlyWarningEntity> baseWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                        .eq(EarlyWarningEntity::getWorkNo, workNo)
                        .apply("CAST(create_date AS DATE) = CURDATE()");

                sysUserDTO.setOffDutyWarning(earlyWarningDao.selectCount(
                        baseWrapper.clone().eq(EarlyWarningEntity::getWarningType, 3))); //离岗预警
                sysUserDTO.setTimeoutReception(earlyWarningDao.selectCount(
                        baseWrapper.clone().eq(EarlyWarningEntity::getWarningType, 2))); //超时接待
                sysUserDTO.setQueueOverTime(earlyWarningDao.selectCount(
                        baseWrapper.clone().eq(EarlyWarningEntity::getWarningType, 4))); //排队过长
                sysUserDTO.setBadComment(earlyWarningDao.selectCount(
                        baseWrapper.clone().eq(EarlyWarningEntity::getWarningType, 1)));// 差评提醒

                // 设置在岗离岗状态
                if (winMonitorDataList != null) {
                    winMonitorDataList.stream()
                            .filter(w -> w.getUserId() != null && w.getUserId().equals(workNo))
                            .findFirst()
                            .ifPresent(w -> {
                                if (w.getStatus() != null && w.getStatus() != 2) {
                                    sysUserDTO.setWorkStatus(1);
                                } else {
                                    sysUserDTO.setWorkStatus(0);
                                }
                            });
                }
            } else {
                sysUserDTO.setOffDutyWarning(0);
                sysUserDTO.setTimeoutReception(0);
                sysUserDTO.setQueueOverTime(0);
                sysUserDTO.setBadComment(0);
                sysUserDTO.setWorkStatus(0);
            }
        }

        return result;
    }

    @Override
    public List<SysUserDTO> getUnbindUser(Map<String, Object> params) {
        List<SysUserEntity> unbindUser = baseDao.getUnbindUser(params);
        return ConvertUtils.sourceToTarget(unbindUser,SysUserDTO.class);
    }

    /**
     * 根据角色 更新查询条件
     * @param params
     */
    public void updateParamsForRole(Map<String,Object> params) {
        if (!params.containsKey("roleId")) return;
        Long roleId = Long.valueOf(params.get("roleId").toString());
        SysRoleDTO sysRoleDTO = sysRoleService.get(roleId);
        if (null==sysRoleDTO) {
            throw new HmitException("未找到角色");
        }
        List<Long> userIdList = sysRoleUserService.getUserIdList(roleId);
        if (CollectionUtils.isNotEmpty(userIdList)) {
            params.put("ids", userIdList);
        }
        else {
            params.put("ids", Lists.newArrayList(-1));
        }
    }

    @Override
    public SysUserDTO getUserWithEnhancedInfo(Long userId) {
        String date = DateUtil.formatDate(new Date());

        SysUserDTO data = get(userId);
        data.setWindowNo(null);
        
        List<Long> roleIdList = sysRoleUserService.getRoleIdList(userId);
        data.setRoleIdList(roleIdList);

        LambdaQueryWrapper<EarlyWarningEntity> cNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 2).eq(EarlyWarningEntity::getWorkNo, data.getWorkNo());
        data.setTimeoutReception(earlyWarningDao.selectCount(cNumWrapper));
        
        LambdaQueryWrapper<EarlyWarningEntity> lNumWrapper = new QueryWrapper<EarlyWarningEntity>().lambda()
                .apply("CAST(create_date AS DATE) = {0}", date).eq(EarlyWarningEntity::getWarningType, 3).eq(EarlyWarningEntity::getWorkNo, data.getWorkNo());
        data.setOffDutyWarning(earlyWarningDao.selectCount(lNumWrapper));

        HallStatisticsDTO hallStatisticsDTO = computeTodayStatistics(data.getWorkNo(), DateUtil.formatDate(new Date()));

        HallStatisticsDTO hallStatisticsDTOYes = computeTodayStatistics(data.getWorkNo(),DateUtil.formatDate(DateUtil.yesterday()));

        ResponseEntity<WinMonitorResponse> response =
                restTemplate.getForEntity(apiIp + "/other/GetWinMonitor", WinMonitorResponse.class);

        WinMonitorResponse body = response.getBody();

        if (body != null && body.getIRet() == 0 && body.getData() != null) {
            List<WinMonitorData> all = body.getData();
            all.stream()
                    .filter(w -> w.getUserId() != null && data.getWorkNo() != null && w.getUserId().equals(data.getWorkNo()))
                    .findFirst()
                    .ifPresent(w -> {
                        if (w.getStatus() != null && w.getStatus() != 2) {
                            data.setWorkStatus(1);
                        } else {
                            data.setWorkStatus(0);
                        }
                        data.setWindowNo(w.getWinName());
                    });
        } else {
            System.out.println("获取在岗数据失败：" + (body != null ? body.getSMsg() : "接口错误"));
        }

        data.setTodayProcess(Integer.parseInt(hallStatisticsDTO.getTodayProcessed()));
        data.setYesterdayProcess(Integer.parseInt(hallStatisticsDTOYes.getTodayProcessed()));
        data.setAverageWaitTime(hallStatisticsDTO.getAverageWaitingTime());
        data.setGoodCommentRate(hallStatisticsDTO.getApplauseRate());
        data.setTodayCompletionRate(hallStatisticsDTO.getTodayCompletionRate());

        return data;
    }

    private HallStatisticsDTO computeTodayStatistics(String workNo, String date) {
        String url = statisticIP+"/smartqueue/report/listrec" + "?sDate=" + date;
        ResponseEntity<FlowResponse> response = restTemplate.getForEntity(url, FlowResponse.class);
        FlowResponse body = response.getBody();

        if (body == null || body.getCode() != 0 || body.getData() == null) {
            System.out.println("接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
            return null;
        }

        List<FlowRecord> records = body.getData().stream().filter(Objects::nonNull).filter(r -> workNo != null && workNo.equals(r.getUser_id())).collect(Collectors.toList());

        int totalPeople = records.size();
        int completedCount = 0;
        long totalWait = 0, totalHandle = 0;
        long minWait = 0, maxWait = 0;

        int ratedCount = 0, satisfiedCount = 0;
        double appValueSum = 0;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (FlowRecord r : records) {
            try {
                Date regTime = sdf.parse(r.getS_reg_time());
                Date callTime = sdf.parse(r.getS_call_time());
                long waitMillis = callTime.getTime() - regTime.getTime();

                totalWait += waitMillis;
                if(minWait==0)
                    minWait = waitMillis;
                else
                    minWait = Math.min(minWait, waitMillis);
                maxWait = Math.max(maxWait, waitMillis);

                if (r.getS_end_time() != null && !r.getS_end_time().isEmpty()) {
                    Date endTime = sdf.parse(r.getS_end_time());
                    long handleMillis = endTime.getTime() - callTime.getTime();
                    totalHandle += handleMillis;
                    completedCount++;
                }

                if (r.getApp_value() != null && r.getApp_value() > 0) {
                    ratedCount++;
                    appValueSum += r.getApp_value();
                    if (r.getApp_value() == 1) {
                        satisfiedCount++;
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        HallStatisticsDTO hallStatisticsDTO = new HallStatisticsDTO();
        hallStatisticsDTO.setTodayPickUp(totalPeople + "");
        hallStatisticsDTO.setTodayProcessed(completedCount + "");
        if(totalPeople == 0)
            hallStatisticsDTO.setAverageWaitingTime("0");
        else
            hallStatisticsDTO.setAverageWaitingTime(formatDuration(totalWait / totalPeople));
        hallStatisticsDTO.setMinWaitingTime(formatDuration(minWait));
        hallStatisticsDTO.setMaxWaitingTime(formatDuration(maxWait));
        hallStatisticsDTO.setTodayTotalProcessingTime(formatDuration(totalHandle));
        hallStatisticsDTO.setTodayCompletionRate(percent(completedCount, totalPeople));
        hallStatisticsDTO.setSatisfactionRate(ratedCount > 0 ? String.format("%.2f", appValueSum / ratedCount) : "0");
        hallStatisticsDTO.setApplauseRate(percent(satisfiedCount, ratedCount));

        return hallStatisticsDTO;
    }

    private String formatDuration(long millis) {
        long minutes = millis / 1000 / 60;
        return minutes+"";
    }

    private String percent(int numerator, int denominator) {
        return denominator == 0 ? "0" : String.format("%.2f", 100.0 * numerator / denominator);
    }

}
