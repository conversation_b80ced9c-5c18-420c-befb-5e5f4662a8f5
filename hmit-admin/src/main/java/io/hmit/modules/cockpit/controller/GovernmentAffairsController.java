package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.GovernmentAffairsDTO;
import io.hmit.modules.cockpit.excel.GovernmentAffairsExcel;
import io.hmit.modules.cockpit.service.GovernmentAffairsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>政务2.0事项部门排名</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-31
 */
@RestController
@RequestMapping("cockpit/governmentaffairs")
@Api(tags="政务2.0事项部门排名")
public class GovernmentAffairsController {

    private final GovernmentAffairsService governmentAffairsService;

    public GovernmentAffairsController(GovernmentAffairsService governmentAffairsService) {
        this.governmentAffairsService = governmentAffairsService;
    }

    @GetMapping
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:governmentaffairs:page")
    public Result<PageData<GovernmentAffairsDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        params.put("page","1");
        params.put("limit","10");
        params.put("orderField","number_cases");
        params.put("order","desc");
        PageData<GovernmentAffairsDTO> page = governmentAffairsService.page(params);

        return new Result<PageData<GovernmentAffairsDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:governmentaffairs:info")
    public Result<GovernmentAffairsDTO> get(@PathVariable("id") Long id){
        GovernmentAffairsDTO data = governmentAffairsService.get(id);

        return new Result<GovernmentAffairsDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:governmentaffairs:save")
    public Result<Object> save(@RequestBody GovernmentAffairsDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        governmentAffairsService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:governmentaffairs:update")
    public Result<Object> update(@RequestBody GovernmentAffairsDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        governmentAffairsService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:governmentaffairs:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        governmentAffairsService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:governmentaffairs:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<GovernmentAffairsDTO> list = governmentAffairsService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, GovernmentAffairsExcel.class);
    }

}