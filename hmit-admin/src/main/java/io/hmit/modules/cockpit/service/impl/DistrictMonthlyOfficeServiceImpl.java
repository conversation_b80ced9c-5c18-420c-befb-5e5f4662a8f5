package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.DistrictMonthlyOfficeDao;
import io.hmit.modules.cockpit.dto.DistrictMonthlyOfficeDTO;
import io.hmit.modules.cockpit.entity.DistrictMonthlyOfficeEntity;
import io.hmit.modules.cockpit.service.DistrictMonthlyOfficeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>地址月办件量 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class DistrictMonthlyOfficeServiceImpl extends CrudServiceImpl<DistrictMonthlyOfficeDao, DistrictMonthlyOfficeEntity, DistrictMonthlyOfficeDTO> implements DistrictMonthlyOfficeService {

    @Override
    public QueryWrapper<DistrictMonthlyOfficeEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<DistrictMonthlyOfficeEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public PageData<DistrictMonthlyOfficeDTO> page(Map<String, Object> params) {
        IPage<DistrictMonthlyOfficeEntity> page = getPage(params, Constant.ID, true);
        QueryWrapper<DistrictMonthlyOfficeEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByAsc(Constant.ID);
        IPage<DistrictMonthlyOfficeEntity> entityPage = baseDao.selectPage(page, wrapper);
        return getPageData(entityPage.getRecords(), entityPage.getTotal(), DistrictMonthlyOfficeDTO.class);
    }
}