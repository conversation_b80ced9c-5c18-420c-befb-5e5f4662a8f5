package io.hmit.modules.cockpit.service;

import io.hmit.common.page.PageData;
import io.hmit.common.service.CrudService;
import io.hmit.common.utils.Result;
import io.hmit.modules.cockpit.dto.CallNumberHappeningDTO;
import io.hmit.modules.cockpit.dto.NumberDto;
import io.hmit.modules.cockpit.dto.TodayCallNumberDataDto;
import io.hmit.modules.cockpit.entity.CallNumberHappeningEntity;

/**
 * <h1>取叫号实况 Service</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
public interface CallNumberHappeningService extends CrudService<CallNumberHappeningEntity, CallNumberHappeningDTO> {


    PageData<CallNumberHappeningDTO> pageDate(String date, Long page, Long limit);

    NumberDto getData();

}