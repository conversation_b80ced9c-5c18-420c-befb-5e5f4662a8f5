package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.SettledDepartmentDao;
import io.hmit.modules.cockpit.dto.SettledDepartmentDTO;
import io.hmit.modules.cockpit.entity.SettledDepartmentEntity;
import io.hmit.modules.cockpit.service.SettledDepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>入驻部门 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-31
 */
@Slf4j
@Service
public class SettledDepartmentServiceImpl extends CrudServiceImpl<SettledDepartmentDao, SettledDepartmentEntity, SettledDepartmentDTO> implements SettledDepartmentService {

    @Override
    public QueryWrapper<SettledDepartmentEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<SettledDepartmentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}