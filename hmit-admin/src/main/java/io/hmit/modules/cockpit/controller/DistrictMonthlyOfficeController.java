package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.DistrictMonthlyOfficeDTO;
import io.hmit.modules.cockpit.excel.DistrictMonthlyOfficeExcel;
import io.hmit.modules.cockpit.service.DistrictMonthlyOfficeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>地址月办件量</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/districtmonthlyoffice")
@Api(tags="地址月办件量")
public class DistrictMonthlyOfficeController {

    private final DistrictMonthlyOfficeService districtMonthlyOfficeService;

    public DistrictMonthlyOfficeController(DistrictMonthlyOfficeService districtMonthlyOfficeService) {
        this.districtMonthlyOfficeService = districtMonthlyOfficeService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:districtmonthlyoffice:page")
    public Result<PageData<DistrictMonthlyOfficeDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<DistrictMonthlyOfficeDTO> page = districtMonthlyOfficeService.page(params);

        return new Result<PageData<DistrictMonthlyOfficeDTO>>().ok(page);
    }


    @GetMapping
    @ApiOperation("查询")
//    @RequiresPermissions("cockpit:districtmonthlyoffice:page")
    public Result query(@ApiIgnore @RequestParam Map<String, Object> params){
//        PageData<DistrictMonthlyOfficeDTO> page = districtMonthlyOfficeService.page(params);
        List<DistrictMonthlyOfficeDTO> list = districtMonthlyOfficeService.list(params);
        return new Result().ok(list);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("cockpit:districtmonthlyoffice:info")
    public Result<DistrictMonthlyOfficeDTO> get(@PathVariable("id") Long id){
        DistrictMonthlyOfficeDTO data = districtMonthlyOfficeService.get(id);

        return new Result<DistrictMonthlyOfficeDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
//    @RequiresPermissions("cockpit:districtmonthlyoffice:save")
    public Result<Object> save(@RequestBody DistrictMonthlyOfficeDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        districtMonthlyOfficeService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("cockpit:districtmonthlyoffice:update")
    public Result<Object> update(@RequestBody DistrictMonthlyOfficeDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        districtMonthlyOfficeService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
//    @RequiresPermissions("cockpit:districtmonthlyoffice:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        districtMonthlyOfficeService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
//    @RequiresPermissions("cockpit:districtmonthlyoffice:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<DistrictMonthlyOfficeDTO> list = districtMonthlyOfficeService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, DistrictMonthlyOfficeExcel.class);
    }

}