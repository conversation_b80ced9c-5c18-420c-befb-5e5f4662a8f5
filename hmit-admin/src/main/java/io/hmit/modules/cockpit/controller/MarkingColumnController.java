package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.MarkingColumnDTO;
import io.hmit.modules.cockpit.excel.MarkingColumnExcel;
import io.hmit.modules.cockpit.service.MarkingColumnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>各时段取号柱状图</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-25
 */
@RestController
@RequestMapping("cockpit/markingcolumn")
@Api(tags="各时段取号柱状图")
public class MarkingColumnController {

    private final MarkingColumnService markingColumnService;

    public MarkingColumnController(MarkingColumnService markingColumnService) {
        this.markingColumnService = markingColumnService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:markingcolumn:page")
    public Result page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<MarkingColumnDTO> page = markingColumnService.page(params);

        return new Result().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:markingcolumn:info")
    public Result<MarkingColumnDTO> get(@PathVariable("id") Long id){
        MarkingColumnDTO data = markingColumnService.get(id);

        return new Result<MarkingColumnDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:markingcolumn:save")
    public Result<Object> save(@RequestBody MarkingColumnDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        markingColumnService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:markingcolumn:update")
    public Result<Object> update(@RequestBody MarkingColumnDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        markingColumnService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:markingcolumn:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        markingColumnService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:markingcolumn:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<MarkingColumnDTO> list = markingColumnService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, MarkingColumnExcel.class);
    }

}