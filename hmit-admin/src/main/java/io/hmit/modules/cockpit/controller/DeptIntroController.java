package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.DeptIntroDTO;
import io.hmit.modules.cockpit.excel.DeptIntroExcel;
import io.hmit.modules.cockpit.service.DeptIntroService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>部门简介</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-25
 */
@RestController
@RequestMapping("cockpit/deptintro")
@Api(tags="部门简介")
public class DeptIntroController {

    private final DeptIntroService deptIntroService;

    public DeptIntroController(DeptIntroService deptIntroService) {
        this.deptIntroService = deptIntroService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:deptintro:page")
    public Result page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<DeptIntroDTO> page = deptIntroService.page(params);

        return new Result().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:deptintro:info")
    public Result<DeptIntroDTO> get(@PathVariable("id") Long id){
        DeptIntroDTO data = deptIntroService.get(id);

        return new Result<DeptIntroDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:deptintro:save")
    public Result<Object> save(@RequestBody DeptIntroDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        deptIntroService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:deptintro:update")
    public Result<Object> update(@RequestBody DeptIntroDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        deptIntroService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:deptintro:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        deptIntroService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:deptintro:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<DeptIntroDTO> list = deptIntroService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, DeptIntroExcel.class);
    }

}