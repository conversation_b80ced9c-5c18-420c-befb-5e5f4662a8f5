package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.NumberProcessedDao;
import io.hmit.modules.cockpit.dto.NumberProcessedDTO;
import io.hmit.modules.cockpit.entity.NumberProcessedEntity;
import io.hmit.modules.cockpit.service.NumberProcessedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>办件量 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class NumberProcessedServiceImpl extends CrudServiceImpl<NumberProcessedDao, NumberProcessedEntity, NumberProcessedDTO> implements NumberProcessedService {

    @Override
    public QueryWrapper<NumberProcessedEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<NumberProcessedEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}