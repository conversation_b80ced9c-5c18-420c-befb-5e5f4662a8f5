package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.InnovativePracticeDTO;
import io.hmit.modules.cockpit.excel.InnovativePracticeExcel;
import io.hmit.modules.cockpit.service.InnovativePracticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>创新实践</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/innovativepractice")
@Api(tags="创新实践")
public class InnovativePracticeController {

    private final InnovativePracticeService innovativePracticeService;

    public InnovativePracticeController(InnovativePracticeService innovativePracticeService) {
        this.innovativePracticeService = innovativePracticeService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:innovativepractice:page")
    public Result<PageData<InnovativePracticeDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<InnovativePracticeDTO> page = innovativePracticeService.page(params);

        return new Result<PageData<InnovativePracticeDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:innovativepractice:info")
    public Result<InnovativePracticeDTO> get(@PathVariable("id") Long id){
        InnovativePracticeDTO data = innovativePracticeService.get(id);

        return new Result<InnovativePracticeDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:innovativepractice:save")
    public Result<Object> save(@RequestBody InnovativePracticeDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        innovativePracticeService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:innovativepractice:update")
    public Result<Object> update(@RequestBody InnovativePracticeDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        innovativePracticeService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:innovativepractice:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        innovativePracticeService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:innovativepractice:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<InnovativePracticeDTO> list = innovativePracticeService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, InnovativePracticeExcel.class);
    }

}