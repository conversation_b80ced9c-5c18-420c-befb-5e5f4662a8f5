package io.hmit.modules.cockpit.service;

import io.hmit.common.page.PageData;
import io.hmit.common.service.CrudService;
import io.hmit.modules.cockpit.dto.BusinessLeaderboardDTO;
import io.hmit.modules.cockpit.entity.BusinessLeaderboardEntity;

import java.util.List;
import java.util.Map;

/**
 * <h1>政务2.0事项排行（新） Service</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-16
 */
public interface BusinessLeaderboardService extends CrudService<BusinessLeaderboardEntity, BusinessLeaderboardDTO> {

    @Override
    PageData<BusinessLeaderboardDTO> page(Map<String, Object> params);

    public List<BusinessLeaderboardDTO> getTopN(int n);
}