package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.ChannelAnalysisDTO;
import io.hmit.modules.cockpit.excel.ChannelAnalysisExcel;
import io.hmit.modules.cockpit.service.ChannelAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>年度收件渠道分析</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-07
 */
@RestController
@RequestMapping("cockpit/channelanalysis")
@Api(tags="年度收件渠道分析")
public class ChannelAnalysisController {

    private final ChannelAnalysisService channelAnalysisService;

    public ChannelAnalysisController(ChannelAnalysisService channelAnalysisService) {
        this.channelAnalysisService = channelAnalysisService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:channelanalysis:page")
    public Result<PageData<ChannelAnalysisDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<ChannelAnalysisDTO> page = channelAnalysisService.page(params);

        return new Result<PageData<ChannelAnalysisDTO>>().ok(page);
    }


    @GetMapping()
    @ApiOperation("查询")
//    @RequiresPermissions("cockpit:channelanalysis:page")
    public Result query(@ApiIgnore @RequestParam Map<String, Object> params){
        List<ChannelAnalysisDTO> list = channelAnalysisService.list(params);

        return new Result().ok(list.get(0));
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("cockpit:channelanalysis:info")
    public Result<ChannelAnalysisDTO> get(@PathVariable("id") Long id){
        ChannelAnalysisDTO data = channelAnalysisService.get(id);

        return new Result<ChannelAnalysisDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
//    @RequiresPermissions("cockpit:channelanalysis:save")
    public Result<Object> save(@RequestBody ChannelAnalysisDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        channelAnalysisService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("cockpit:channelanalysis:update")
    public Result<Object> update(@RequestBody ChannelAnalysisDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        channelAnalysisService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:channelanalysis:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        channelAnalysisService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:channelanalysis:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<ChannelAnalysisDTO> list = channelAnalysisService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, ChannelAnalysisExcel.class);
    }

}