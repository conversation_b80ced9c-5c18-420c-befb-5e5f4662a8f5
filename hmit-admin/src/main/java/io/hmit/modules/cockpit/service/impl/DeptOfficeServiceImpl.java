package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.DeptOfficeDao;
import io.hmit.modules.cockpit.dto.DeptOfficeDTO;
import io.hmit.modules.cockpit.entity.DeptOfficeEntity;
import io.hmit.modules.cockpit.service.DeptOfficeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>部门办件 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-25
 */
@Slf4j
@Service
public class DeptOfficeServiceImpl extends CrudServiceImpl<DeptOfficeDao, DeptOfficeEntity, DeptOfficeDTO> implements DeptOfficeService {

    @Override
    public QueryWrapper<DeptOfficeEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<DeptOfficeEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}