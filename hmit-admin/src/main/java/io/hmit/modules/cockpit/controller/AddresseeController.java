package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.AddresseeDTO;
import io.hmit.modules.cockpit.excel.AddresseeExcel;
import io.hmit.modules.cockpit.service.AddresseeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>2.0收件数排名</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-31
 */
@RestController
@RequestMapping("cockpit/addressee")
@Api(tags="2.0收件数排名")
public class AddresseeController {

    private final AddresseeService addresseeService;

    public AddresseeController(AddresseeService addresseeService) {
        this.addresseeService = addresseeService;
    }

    @GetMapping
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:addressee:page")
    public Result<PageData<AddresseeDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        params.put("page","1");
        params.put("limit","5");
        params.put("orderField","number_received_packages");
        params.put("order","desc");
        PageData<AddresseeDTO> page = addresseeService.page(params);

        return new Result<PageData<AddresseeDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:addressee:info")
    public Result<AddresseeDTO> get(@PathVariable("id") Long id){
        AddresseeDTO data = addresseeService.get(id);

        return new Result<AddresseeDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:addressee:save")
    public Result<Object> save(@RequestBody AddresseeDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        addresseeService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:addressee:update")
    public Result<Object> update(@RequestBody AddresseeDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        addresseeService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:addressee:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        addresseeService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:addressee:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<AddresseeDTO> list = addresseeService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, AddresseeExcel.class);
    }

}