package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.hmit.common.exception.HmitException;
import io.hmit.common.page.PageData;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.common.utils.ConvertUtils;
import io.hmit.modules.cockpit.dao.BusinessLeaderboardDao;
import io.hmit.modules.cockpit.dto.BusinessLeaderboardDTO;
import io.hmit.modules.cockpit.entity.BusinessLeaderboardEntity;
import io.hmit.modules.cockpit.service.BusinessLeaderboardService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <h1>政务2.0事项排行（新） Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-16
 */
@Slf4j
@Service
public class BusinessLeaderboardServiceImpl extends CrudServiceImpl<BusinessLeaderboardDao, BusinessLeaderboardEntity, BusinessLeaderboardDTO> implements BusinessLeaderboardService {

    @Override
    public PageData<BusinessLeaderboardDTO> page(Map<String, Object> params) {
        IPage<BusinessLeaderboardEntity> page = baseDao.selectPage(
                getPage(params, null, false),
                new LambdaQueryWrapper<BusinessLeaderboardEntity>()
                        .orderByDesc(BusinessLeaderboardEntity::getTotal)
        );

        return getPageData(page, BusinessLeaderboardDTO.class);
    }

    public List<BusinessLeaderboardDTO> getTopN(int n) {
        if (n < 0) {
            throw new HmitException("n必须大于0");
        }
        List<BusinessLeaderboardEntity> entityList = baseDao.selectList(new LambdaQueryWrapper<BusinessLeaderboardEntity>()
                .orderByDesc(BusinessLeaderboardEntity::getTotal)
                .last("LIMIT " + n));
        return ConvertUtils.sourceToTarget(entityList, BusinessLeaderboardDTO.class);
    }

    @Override
    public QueryWrapper<BusinessLeaderboardEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<BusinessLeaderboardEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}