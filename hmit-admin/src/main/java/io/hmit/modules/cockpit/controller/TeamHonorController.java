package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.TeamHonorDTO;
import io.hmit.modules.cockpit.excel.TeamHonorExcel;
import io.hmit.modules.cockpit.service.TeamHonorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>团队荣誉</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/teamhonor")
@Api(tags="团队荣誉")
public class TeamHonorController {

    private final TeamHonorService teamHonorService;

    public TeamHonorController(TeamHonorService teamHonorService) {
        this.teamHonorService = teamHonorService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:teamhonor:page")
    public Result page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<TeamHonorDTO> page = teamHonorService.page(params);

        return new Result().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:teamhonor:info")
    public Result<TeamHonorDTO> get(@PathVariable("id") Long id){
        TeamHonorDTO data = teamHonorService.get(id);

        return new Result<TeamHonorDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:teamhonor:save")
    public Result<Object> save(@RequestBody TeamHonorDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        teamHonorService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:teamhonor:update")
    public Result<Object> update(@RequestBody TeamHonorDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        teamHonorService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:teamhonor:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        teamHonorService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:teamhonor:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TeamHonorDTO> list = teamHonorService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, TeamHonorExcel.class);
    }

}