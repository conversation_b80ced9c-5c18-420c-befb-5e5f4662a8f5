package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.FeedbackAnalysisDao;
import io.hmit.modules.cockpit.dto.FeedbackAnalysisDTO;
import io.hmit.modules.cockpit.entity.FeedbackAnalysisEntity;
import io.hmit.modules.cockpit.service.FeedbackAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>${comments} Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-19
 */
@Slf4j
@Service
public class FeedbackAnalysisServiceImpl extends CrudServiceImpl<FeedbackAnalysisDao, FeedbackAnalysisEntity, FeedbackAnalysisDTO> implements FeedbackAnalysisService {

    @Override
    public QueryWrapper<FeedbackAnalysisEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<FeedbackAnalysisEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}