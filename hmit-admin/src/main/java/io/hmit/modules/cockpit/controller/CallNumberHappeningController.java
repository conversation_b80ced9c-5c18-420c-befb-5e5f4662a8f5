package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.CallNumberHappeningDTO;
import io.hmit.modules.cockpit.dto.NumberDto;
import io.hmit.modules.cockpit.dto.TodayCallNumberDataDto;
import io.hmit.modules.cockpit.excel.CallNumberHappeningExcel;
import io.hmit.modules.cockpit.service.CallNumberHappeningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <h1>取叫号实况</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/callnumberhappening")
@Api(tags="取叫号实况")
public class CallNumberHappeningController {

    private final CallNumberHappeningService callNumberHappeningService;

    public CallNumberHappeningController(CallNumberHappeningService callNumberHappeningService) {
        this.callNumberHappeningService = callNumberHappeningService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:callnumberhappening:page")
    public Result page(@RequestParam(required = false) String date, @RequestParam(defaultValue = "1",required = false) Long page, @RequestParam(defaultValue = "10",required = false) Long limit){
        PageData<CallNumberHappeningDTO> pageData = callNumberHappeningService.pageDate(date,page,limit);
        List<CallNumberHappeningDTO> list = pageData.getList();
        int todayNumber = 0;
        int todayWait = 0;
        if (list!=null && list.size()>0){
            for (int i = 0; i < list.size(); i++) {
                todayNumber +=  list.get(i).getNumber();
                todayWait +=  list.get(i).getWaitPeople();
            }
        }
        HashMap<Object, Object> map = new HashMap<>();
        map.put("todayNumber",todayNumber);
        map.put("todayWait",todayWait);
        map.put("pageData",pageData);
        return new Result().ok(map);

    }


    @GetMapping("analyze")
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:callnumberhappening:page")
    public Result analyze(@RequestParam(required = false) String date, @RequestParam(defaultValue = "1",required = false) Long page, @RequestParam(defaultValue = "10",required = false) Long limit){
        PageData<CallNumberHappeningDTO> pageData = callNumberHappeningService.pageDate(date,page,limit);
        List<CallNumberHappeningDTO> list = pageData.getList();
        int todayNumber = 0;
        int todayWait = 0;
        if (list!=null && list.size()>0){
            for (int i = 0; i < list.size(); i++) {
                todayNumber +=  list.get(i).getNumber();
                todayWait +=  list.get(i).getWaitPeople();
            }
        }
       NumberDto numberDto =  callNumberHappeningService.getData();
        HashMap<Object, Object> map = new HashMap<>();
        map.put("todayNumber",todayNumber);
        map.put("thisYearNumber",numberDto.getThisYearNumber());
        map.put("maxDayNumber",numberDto.getMaxDayNumber());
        map.put("avgNumber",numberDto.getAvgNumber());
        map.put("pageData",pageData);
        return new Result().ok(map);

    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:callnumberhappening:info")
    public Result<CallNumberHappeningDTO> get(@PathVariable("id") Long id){
        CallNumberHappeningDTO data = callNumberHappeningService.get(id);

        return new Result<CallNumberHappeningDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:callnumberhappening:save")
    public Result<Object> save(@RequestBody CallNumberHappeningDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        callNumberHappeningService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:callnumberhappening:update")
    public Result<Object> update(@RequestBody CallNumberHappeningDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        callNumberHappeningService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:callnumberhappening:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        callNumberHappeningService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:callnumberhappening:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<CallNumberHappeningDTO> list = callNumberHappeningService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, CallNumberHappeningExcel.class);
    }

}