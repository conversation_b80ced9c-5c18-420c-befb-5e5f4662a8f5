package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.WindowWorkloadDTO;
import io.hmit.modules.cockpit.excel.WindowWorkloadExcel;
import io.hmit.modules.cockpit.service.WindowWorkloadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>窗口工作量</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/windowworkload")
@Api(tags="窗口工作量")
public class WindowWorkloadController {

    private final WindowWorkloadService windowWorkloadService;

    public WindowWorkloadController(WindowWorkloadService windowWorkloadService) {
        this.windowWorkloadService = windowWorkloadService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:windowworkload:page")
    public Result<PageData<WindowWorkloadDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        params.put("orderField","number");
        params.put("order","desc");
        PageData<WindowWorkloadDTO> page = windowWorkloadService.page(params);

        return new Result<PageData<WindowWorkloadDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:windowworkload:info")
    public Result<WindowWorkloadDTO> get(@PathVariable("id") Long id){
        WindowWorkloadDTO data = windowWorkloadService.get(id);

        return new Result<WindowWorkloadDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:windowworkload:save")
    public Result<Object> save(@RequestBody WindowWorkloadDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        windowWorkloadService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:windowworkload:update")
    public Result<Object> update(@RequestBody WindowWorkloadDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        windowWorkloadService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:windowworkload:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        windowWorkloadService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:windowworkload:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<WindowWorkloadDTO> list = windowWorkloadService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, WindowWorkloadExcel.class);
    }

}