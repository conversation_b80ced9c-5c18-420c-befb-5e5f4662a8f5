package io.hmit.modules.cockpit.service;

import io.hmit.common.page.PageData;
import io.hmit.common.service.CrudService;
import io.hmit.modules.cockpit.dto.OffPeakPeriodStatsDTO;
import io.hmit.modules.cockpit.entity.OffPeakPeriodStatsEntity;

import java.util.Map;

/**
 * <h1>非高峰时段统计 Service</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-07
 */
public interface OffPeakPeriodStatsService extends CrudService<OffPeakPeriodStatsEntity, OffPeakPeriodStatsDTO> {
    @Override
    PageData<OffPeakPeriodStatsDTO> page(Map<String, Object> params);
}