package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.hmit.common.page.PageData;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.common.utils.Result;
import io.hmit.modules.cockpit.dao.CallNumberHappeningDao;
import io.hmit.modules.cockpit.dto.CallNumberHappeningDTO;
import io.hmit.modules.cockpit.dto.NumberDto;
import io.hmit.modules.cockpit.dto.TodayCallNumberDataDto;
import io.hmit.modules.cockpit.entity.CallNumberHappeningEntity;
import io.hmit.modules.cockpit.service.CallNumberHappeningService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <h1>取叫号实况 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class CallNumberHappeningServiceImpl extends CrudServiceImpl<CallNumberHappeningDao, CallNumberHappeningEntity, CallNumberHappeningDTO> implements CallNumberHappeningService {

    @Override
    public QueryWrapper<CallNumberHappeningEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<CallNumberHappeningEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public PageData<CallNumberHappeningDTO> pageDate(String date, Long page, Long limit) {
        if (date==null){
            LocalDate currentDate = LocalDate.now();

            // 获取年份
            int year = currentDate.getYear();

            // 获取月份
            int month = currentDate.getMonthValue();

            // 获取日期
            int day = currentDate.getDayOfMonth();
            date = year + "-" + month + "-" + day;
        }
        QueryWrapper<CallNumberHappeningEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("data_date",date);
        IPage<CallNumberHappeningEntity> callNumberHappeningEntityIPage = baseDao.selectPage(new Page<>(page, limit), wrapper);
        return getPageData(callNumberHappeningEntityIPage, CallNumberHappeningDTO.class);
    }

    @Override
    public NumberDto getData() {
        return baseDao.getData();
    }
}