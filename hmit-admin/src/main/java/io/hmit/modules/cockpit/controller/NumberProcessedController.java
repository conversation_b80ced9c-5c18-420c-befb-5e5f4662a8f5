package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.NumberProcessedDTO;
import io.hmit.modules.cockpit.excel.NumberProcessedExcel;
import io.hmit.modules.cockpit.service.NumberProcessedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>办件量</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/numberprocessed")
@Api(tags="办件量")
public class NumberProcessedController {

    private final NumberProcessedService numberProcessedService;

    public NumberProcessedController(NumberProcessedService numberProcessedService) {
        this.numberProcessedService = numberProcessedService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:numberprocessed:page")
    public Result<PageData<NumberProcessedDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<NumberProcessedDTO> page = numberProcessedService.page(params);

        return new Result<PageData<NumberProcessedDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:numberprocessed:info")
    public Result<NumberProcessedDTO> get(@PathVariable("id") Long id){
        NumberProcessedDTO data = numberProcessedService.get(id);

        return new Result<NumberProcessedDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:numberprocessed:save")
    public Result<Object> save(@RequestBody NumberProcessedDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        numberProcessedService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:numberprocessed:update")
    public Result<Object> update(@RequestBody NumberProcessedDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        numberProcessedService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:numberprocessed:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        numberProcessedService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:numberprocessed:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<NumberProcessedDTO> list = numberProcessedService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, NumberProcessedExcel.class);
    }

}