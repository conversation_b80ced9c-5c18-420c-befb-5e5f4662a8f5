package io.hmit.modules.cockpit.service;

import io.hmit.common.page.PageData;
import io.hmit.common.service.CrudService;
import io.hmit.modules.cockpit.dto.ReceiptLeaderboardDTO;
import io.hmit.modules.cockpit.entity.ReceiptLeaderboardEntity;

import java.util.List;
import java.util.Map;

/**
 * <h1>部门收件数排行 Service</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-16
 */
public interface ReceiptLeaderboardService extends CrudService<ReceiptLeaderboardEntity, ReceiptLeaderboardDTO> {
    @Override
    PageData<ReceiptLeaderboardDTO> page(Map<String, Object> params);
    List<ReceiptLeaderboardDTO> getTop5();
}