package io.hmit.modules.cockpit.service.impl;

import io.hmit.common.exception.HmitException;
import io.hmit.modules.cockpit.dto.*;
import io.hmit.modules.cockpit.service.ExternalApiService;
import io.hmit.modules.job.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class ExternalApiServiceImpl implements ExternalApiService {

    @Value("${HZZH.STATISTIC_IP}")
    private String statisticIP;
    
    @Value("${HZZH.IP}")
    private String apiIp;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public DepartmentListResponse getAllDepartments() throws HmitException {
        String url = statisticIP + "/smartqueue/other/deps";
        ResponseEntity<DepartmentListResponse> response = restTemplate.getForEntity(url, DepartmentListResponse.class);
        DepartmentListResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            throw new HmitException("获取所有部门名称列表接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public QueueListResponse getAllQueues() throws HmitException {
        String url = statisticIP + "/smartqueue/other/queues";
        ResponseEntity<QueueListResponse> response = restTemplate.getForEntity(url, QueueListResponse.class);
        QueueListResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            throw new HmitException("获取所有队列名称列表接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public WindowListResponse getAllWindows() throws HmitException {
        String url = statisticIP + "/smartqueue/other/wins";
        ResponseEntity<WindowListResponse> response = restTemplate.getForEntity(url, WindowListResponse.class);
        WindowListResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            throw new HmitException("获取所有窗口名称列表接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public DeptStatisticsResponse getDepartmentStatistics(String startDate, String endDate) throws HmitException {
        String url = statisticIP + String.format("/smartqueue/report/dep?sStart=%s&sEnd=%s", startDate, endDate);
        ResponseEntity<DeptStatisticsResponse> response = restTemplate.getForEntity(url, DeptStatisticsResponse.class);
        DeptStatisticsResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            throw new HmitException("获取部门收件数Top5接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public QueueStatisticsResponse getQueueStatistics(String startDate, String endDate) throws HmitException {
        String url = statisticIP + String.format("/smartqueue/report/queue?sStart=%s&sEnd=%s", startDate, endDate);
        ResponseEntity<QueueStatisticsResponse> response = restTemplate.getForEntity(url, QueueStatisticsResponse.class);
        QueueStatisticsResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            throw new HmitException("获取队列统计信息接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public HallStatisticsResponse getHallStatistics(String startDate, String endDate) throws HmitException {
        String url = statisticIP + String.format("/smartqueue/report/hall?sStart=%s&sEnd=%s", startDate, endDate);
        ResponseEntity<HallStatisticsResponse> response = restTemplate.getForEntity(url, HallStatisticsResponse.class);
        HallStatisticsResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            throw new HmitException("大厅统计接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public TodayCallStatusResponse getTodayCallStatus() throws HmitException {
        String url = statisticIP + "/smartqueue/other/hallgetcall";
        ResponseEntity<TodayCallStatusResponse> response = restTemplate.getForEntity(url, TodayCallStatusResponse.class);
        TodayCallStatusResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            throw new HmitException("获取今日叫号状态接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public DepartmentQuantityResponse getDepartmentQuantity() throws HmitException {
        String url = statisticIP + "/smartqueue/other/halldepgetcall";
        ResponseEntity<DepartmentQuantityResponse> response = restTemplate.getForEntity(url, DepartmentQuantityResponse.class);
        DepartmentQuantityResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            throw new HmitException("获取各部门数量接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public QueueInfoResponse getQueueWaitInfo() throws HmitException {
        String url = apiIp + "/other/GetQueueWait";
        ResponseEntity<QueueInfoResponse> response = restTemplate.getForEntity(url, QueueInfoResponse.class);
        QueueInfoResponse body = response.getBody();
        if (body == null || body.getIRet() != 0) {
            throw new HmitException("获取队列等候信息接口调用失败：" + (body != null ? body.getSMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public BadReviewResponse getBadReviews() throws HmitException {
        String url = apiIp + "/other/GetBad";
        ResponseEntity<BadReviewResponse> response = restTemplate.getForEntity(url, BadReviewResponse.class);
        BadReviewResponse body = response.getBody();
        if (body == null || body.getIRet() != 0) {
            throw new HmitException("获取差评信息接口调用失败：" + (body != null ? body.getSMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public WinMonitorResponse getWindowMonitorInfo() throws HmitException {
        String url = apiIp + "/other/GetWinMonitor";
        ResponseEntity<WinMonitorResponse> response = restTemplate.getForEntity(url, WinMonitorResponse.class);
        WinMonitorResponse body = response.getBody();
        if (body == null || body.getIRet() != 0) {
            throw new HmitException("获取窗口监控信息接口调用失败：" + (body != null ? body.getSMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public UserListResponse getUserList() throws HmitException {
        String url = statisticIP + "/smartqueue/other/users";
        ResponseEntity<UserListResponse> response = restTemplate.getForEntity(url, UserListResponse.class);
        UserListResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            throw new HmitException("获取用户列表接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }
        return body;
    }

    @Override
    public HallStatResponse getHallStatistics2(String startDate, String endDate) throws HmitException {
        String url = statisticIP + String.format("/smartqueue/report/hall?sStart=%s&sEnd=%s", startDate, endDate);
        ResponseEntity<HallStatResponse> response = restTemplate.getForEntity(url, HallStatResponse.class);
        HallStatResponse body = response.getBody();
        if (body == null || body.getCode() != 0 || body.getData() == null) {
            throw new HmitException("获取大厅统计信息接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
        }
        return body;
    }
}