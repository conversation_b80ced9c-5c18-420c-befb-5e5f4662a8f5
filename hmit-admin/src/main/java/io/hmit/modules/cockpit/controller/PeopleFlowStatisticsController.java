package io.hmit.modules.cockpit.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.exception.HmitException;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.PeopleFlowStatisticsDTO;
import io.hmit.modules.cockpit.excel.PeopleFlowStatisticsExcel;
import io.hmit.modules.cockpit.service.PeopleFlowStatisticsService;
import io.hmit.modules.sys.service.SysParamsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import springfox.documentation.annotations.ApiIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;


import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * <h1>今日人流量统计</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-07
 */
@RestController
@RequestMapping("cockpit/peopleflowstatistics")
@Api(tags="今日人流量统计")
@AllArgsConstructor
public class PeopleFlowStatisticsController {

    private final PeopleFlowStatisticsService peopleFlowStatisticsService;
    private final RestTemplate restTemplate;
    private final SysParamsService sysParamsService;


    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:peopleflowstatistics:page")
    public Result<PageData<PeopleFlowStatisticsDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<PeopleFlowStatisticsDTO> page = peopleFlowStatisticsService.page(params);

        return new Result<PageData<PeopleFlowStatisticsDTO>>().ok(page);
    }


    @GetMapping
    @ApiOperation("查询")
//    @RequiresPermissions("cockpit:peopleflowstatistics:page")
    public Result query(@ApiIgnore @RequestParam Map<String, Object> params){
        List<PeopleFlowStatisticsDTO> list = peopleFlowStatisticsService.list(params);
        return new Result().ok(list.get(0));
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("cockpit:peopleflowstatistics:info")
    public Result<PeopleFlowStatisticsDTO> get(@PathVariable("id") Long id){
        PeopleFlowStatisticsDTO data = peopleFlowStatisticsService.get(id);

        return new Result<PeopleFlowStatisticsDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
//    @RequiresPermissions("cockpit:peopleflowstatistics:save")
    public Result<Object> save(@RequestBody PeopleFlowStatisticsDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        peopleFlowStatisticsService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("cockpit:peopleflowstatistics:update")
    public Result<Object> update(@RequestBody PeopleFlowStatisticsDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        peopleFlowStatisticsService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:peopleflowstatistics:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        peopleFlowStatisticsService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:peopleflowstatistics:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<PeopleFlowStatisticsDTO> list = peopleFlowStatisticsService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, PeopleFlowStatisticsExcel.class);
    }

    @ApiOperation(value = "人流量统计接口")
    @GetMapping("/passengerFlow")
    public Result<Object> passengerFlow() {
        Integer todayEntryCountBase = sysParamsService.getValueObject("todayEntryCountBase", Integer.class);

        try {
            String url = "http://172.19.191.107/CardSolution/statistics/regionConfig/getRegionInfo";
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Object> response = restTemplate.getForEntity(url, Object.class);
            Object body = response.getBody();

            if (body == null) {
                throw new HmitException("接口调用失败：未知错误");
            }

            // 用 ObjectMapper 动态操作 JSON
            ObjectMapper objectMapper = new ObjectMapper();
            // 先把 body 转成字符串再转 JsonNode
            ObjectNode root = (ObjectNode) objectMapper.readTree(objectMapper.writeValueAsString(body));
            ArrayNode dataArray = (ArrayNode) root.get("data");

            if (dataArray != null && dataArray.size() > 0) {
                ObjectNode firstObj = (ObjectNode) dataArray.get(0);
                int enterNumber = firstObj.get("enterNumber").asInt();
                firstObj.put("enterNumber", enterNumber + todayEntryCountBase);
            }

            return new Result<>().ok(objectMapper.convertValue(root, Object.class));
        } catch (Exception e) {
            return new Result<>().error("接口调用失败:" + e.getMessage());
        }

    }
}