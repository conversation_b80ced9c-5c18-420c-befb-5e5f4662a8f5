package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.FeedbackAnalysisDTO;
import io.hmit.modules.cockpit.excel.FeedbackAnalysisExcel;
import io.hmit.modules.cockpit.service.FeedbackAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>${comments}</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-19
 */
@RestController
@RequestMapping("cockpit/feedbackanalysis")
@Api(tags="好差评分析")
public class FeedbackAnalysisController {

    private final FeedbackAnalysisService feedbackAnalysisService;

    public FeedbackAnalysisController(FeedbackAnalysisService feedbackAnalysisService) {
        this.feedbackAnalysisService = feedbackAnalysisService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:feedbackanalysis:page")
    public Result<PageData<FeedbackAnalysisDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<FeedbackAnalysisDTO> page = feedbackAnalysisService.page(params);

        return new Result<PageData<FeedbackAnalysisDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("cockpit:feedbackanalysis:info")
    public Result<FeedbackAnalysisDTO> get(@PathVariable("id") Long id){
        FeedbackAnalysisDTO data = feedbackAnalysisService.get(id);

        return new Result<FeedbackAnalysisDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:feedbackanalysis:save")
    public Result<Object> save(@RequestBody FeedbackAnalysisDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto);

        feedbackAnalysisService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("cockpit:feedbackanalysis:update")
    public Result<Object> update(@RequestBody FeedbackAnalysisDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto);

        feedbackAnalysisService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:feedbackanalysis:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        feedbackAnalysisService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:feedbackanalysis:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<FeedbackAnalysisDTO> list = feedbackAnalysisService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, FeedbackAnalysisExcel.class);
    }

}