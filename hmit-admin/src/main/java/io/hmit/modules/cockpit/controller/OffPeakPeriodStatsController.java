package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.OffPeakPeriodStatsDTO;
import io.hmit.modules.cockpit.excel.OffPeakPeriodStatsExcel;
import io.hmit.modules.cockpit.service.OffPeakPeriodStatsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Comparator;
import java.util.List;
import java.util.Map;


/**
 * <h1>非高峰时段统计</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-07
 */
@RestController
@RequestMapping("cockpit/offpeakperiodstats")
@Api(tags="非高峰时段统计")
public class OffPeakPeriodStatsController {

    private final OffPeakPeriodStatsService offPeakPeriodStatsService;

    public OffPeakPeriodStatsController(OffPeakPeriodStatsService offPeakPeriodStatsService) {
        this.offPeakPeriodStatsService = offPeakPeriodStatsService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:offpeakperiodstats:page")
    public Result<PageData<OffPeakPeriodStatsDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<OffPeakPeriodStatsDTO> page = offPeakPeriodStatsService.page(params);

        return new Result<PageData<OffPeakPeriodStatsDTO>>().ok(page);
    }


    @GetMapping()
    @ApiOperation("查询")
//    @RequiresPermissions("cockpit:offpeakperiodstats:page")
    public Result<List<OffPeakPeriodStatsDTO>> query(@ApiIgnore @RequestParam Map<String, Object> params){
        List<OffPeakPeriodStatsDTO> list = offPeakPeriodStatsService.list(params);
        list.sort(Comparator.comparingInt(OffPeakPeriodStatsDTO::getSort));
        return new Result().ok(list);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("cockpit:offpeakperiodstats:info")
    public Result<OffPeakPeriodStatsDTO> get(@PathVariable("id") Long id){
        OffPeakPeriodStatsDTO data = offPeakPeriodStatsService.get(id);

        return new Result<OffPeakPeriodStatsDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
//    @RequiresPermissions("cockpit:offpeakperiodstats:save")
    public Result<Object> save(@RequestBody OffPeakPeriodStatsDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        offPeakPeriodStatsService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("cockpit:offpeakperiodstats:update")
    public Result<Object> update(@RequestBody OffPeakPeriodStatsDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto);

        offPeakPeriodStatsService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
//    @RequiresPermissions("cockpit:offpeakperiodstats:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        offPeakPeriodStatsService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:offpeakperiodstats:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<OffPeakPeriodStatsDTO> list = offPeakPeriodStatsService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, OffPeakPeriodStatsExcel.class);
    }

}