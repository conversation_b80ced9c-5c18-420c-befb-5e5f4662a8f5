package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.AnnualJobSatisfactionDTO;
import io.hmit.modules.cockpit.excel.AnnualJobSatisfactionExcel;
import io.hmit.modules.cockpit.service.AnnualJobSatisfactionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>全年办件满意度分析</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-30
 */
@RestController
@RequestMapping("cockpit/annualjobsatisfaction")
@Api(tags="全年办件满意度分析")
public class AnnualJobSatisfactionController {

    private final AnnualJobSatisfactionService annualJobSatisfactionService;

    public AnnualJobSatisfactionController(AnnualJobSatisfactionService annualJobSatisfactionService) {
        this.annualJobSatisfactionService = annualJobSatisfactionService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:annualjobsatisfaction:page")
    public Result<PageData<AnnualJobSatisfactionDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<AnnualJobSatisfactionDTO> page = annualJobSatisfactionService.page(params);
        AnnualJobSatisfactionDTO annualJobSatisfactionDTO = annualJobSatisfactionService.get(1L);
        return new Result().ok(annualJobSatisfactionDTO);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:annualjobsatisfaction:info")
    public Result<AnnualJobSatisfactionDTO> get(@PathVariable("id") Long id){
        AnnualJobSatisfactionDTO data = annualJobSatisfactionService.get(id);

        return new Result<AnnualJobSatisfactionDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:annualjobsatisfaction:save")
    public Result<Object> save(@RequestBody AnnualJobSatisfactionDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        annualJobSatisfactionService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:annualjobsatisfaction:update")
    public Result<Object> update(@RequestBody AnnualJobSatisfactionDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        annualJobSatisfactionService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:annualjobsatisfaction:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        annualJobSatisfactionService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:annualjobsatisfaction:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<AnnualJobSatisfactionDTO> list = annualJobSatisfactionService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, AnnualJobSatisfactionExcel.class);
    }

}