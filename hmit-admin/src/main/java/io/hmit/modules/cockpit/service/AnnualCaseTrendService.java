package io.hmit.modules.cockpit.service;

import io.hmit.common.service.CrudService;
import io.hmit.modules.cockpit.dto.AnnualCaseTrendDTO;
import io.hmit.modules.cockpit.entity.AnnualCaseTrendEntity;

import java.util.List;

/**
 * <h1>${comments} Service</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-19
 */
public interface AnnualCaseTrendService extends CrudService<AnnualCaseTrendEntity, AnnualCaseTrendDTO> {

    List<AnnualCaseTrendDTO> getRecentNMonthsTrend(int n,String endMonthStr);

    @Override
    void save(AnnualCaseTrendDTO dto);

    @Override
    void update(AnnualCaseTrendDTO dto);
}