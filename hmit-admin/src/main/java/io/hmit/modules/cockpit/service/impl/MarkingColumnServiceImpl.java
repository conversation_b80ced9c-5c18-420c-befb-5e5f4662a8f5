package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.MarkingColumnDao;
import io.hmit.modules.cockpit.dto.MarkingColumnDTO;
import io.hmit.modules.cockpit.entity.MarkingColumnEntity;
import io.hmit.modules.cockpit.service.MarkingColumnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>各时段取号柱状图 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-25
 */
@Slf4j
@Service
public class MarkingColumnServiceImpl extends CrudServiceImpl<MarkingColumnDao, MarkingColumnEntity, MarkingColumnDTO> implements MarkingColumnService {

    @Override
    public QueryWrapper<MarkingColumnEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<MarkingColumnEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}