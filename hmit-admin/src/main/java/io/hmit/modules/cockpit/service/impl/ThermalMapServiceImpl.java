package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.ThermalMapDao;
import io.hmit.modules.cockpit.dto.ThermalMapDTO;
import io.hmit.modules.cockpit.entity.ThermalMapEntity;
import io.hmit.modules.cockpit.service.ThermalMapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>政务大厅热流图 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class ThermalMapServiceImpl extends CrudServiceImpl<ThermalMapDao, ThermalMapEntity, ThermalMapDTO> implements ThermalMapService {

    @Override
    public QueryWrapper<ThermalMapEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<ThermalMapEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}