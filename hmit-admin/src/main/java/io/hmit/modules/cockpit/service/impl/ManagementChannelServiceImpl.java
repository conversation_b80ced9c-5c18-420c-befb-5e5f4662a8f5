package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.ManagementChannelDao;
import io.hmit.modules.cockpit.dto.ManagementChannelDTO;
import io.hmit.modules.cockpit.entity.ManagementChannelEntity;
import io.hmit.modules.cockpit.service.ManagementChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>办理渠道 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class ManagementChannelServiceImpl extends CrudServiceImpl<ManagementChannelDao, ManagementChannelEntity, ManagementChannelDTO> implements ManagementChannelService {

    @Override
    public QueryWrapper<ManagementChannelEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<ManagementChannelEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}