package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.TotalDataDao;
import io.hmit.modules.cockpit.dto.TotalDataDTO;
import io.hmit.modules.cockpit.entity.TotalDataEntity;
import io.hmit.modules.cockpit.service.TotalDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>办件总数据 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class TotalDataServiceImpl extends CrudServiceImpl<TotalDataDao, TotalDataEntity, TotalDataDTO> implements TotalDataService {

    @Override
    public QueryWrapper<TotalDataEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<TotalDataEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}