package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.InnovativePracticeDao;
import io.hmit.modules.cockpit.dto.InnovativePracticeDTO;
import io.hmit.modules.cockpit.entity.InnovativePracticeEntity;
import io.hmit.modules.cockpit.service.InnovativePracticeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>创新实践 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class InnovativePracticeServiceImpl extends CrudServiceImpl<InnovativePracticeDao, InnovativePracticeEntity, InnovativePracticeDTO> implements InnovativePracticeService {

    @Override
    public QueryWrapper<InnovativePracticeEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<InnovativePracticeEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}