package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.OffPeakPeriodStatsDao;
import io.hmit.modules.cockpit.dto.OffPeakPeriodStatsDTO;
import io.hmit.modules.cockpit.entity.OffPeakPeriodStatsEntity;
import io.hmit.modules.cockpit.service.OffPeakPeriodStatsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>非高峰时段统计 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-07
 */
@Slf4j
@Service
public class OffPeakPeriodStatsServiceImpl extends CrudServiceImpl<OffPeakPeriodStatsDao, OffPeakPeriodStatsEntity, OffPeakPeriodStatsDTO> implements OffPeakPeriodStatsService {

    @Override
    public PageData<OffPeakPeriodStatsDTO> page(Map<String, Object> params) {
        IPage<OffPeakPeriodStatsEntity> page = getPage(params, Constant.ID, true);
        LambdaQueryWrapper<OffPeakPeriodStatsEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(OffPeakPeriodStatsEntity::getSort);
        IPage<OffPeakPeriodStatsEntity> entityPage = baseDao.selectPage(page, wrapper);
        return getPageData(entityPage.getRecords(), entityPage.getTotal(), OffPeakPeriodStatsDTO.class);
    }

    @Override
    public QueryWrapper<OffPeakPeriodStatsEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<OffPeakPeriodStatsEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}