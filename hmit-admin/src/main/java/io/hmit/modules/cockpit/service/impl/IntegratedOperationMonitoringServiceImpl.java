package io.hmit.modules.cockpit.service.impl;


import io.hmit.common.exception.HmitException;
import io.hmit.modules.cockpit.dto.*;
import io.hmit.modules.cockpit.service.ExternalApiService;
import io.hmit.modules.cockpit.service.IntegratedOperationMonitoringService;
import io.hmit.modules.job.dto.DepartmentListResponse;
import io.hmit.modules.job.dto.QueueListResponse;
import io.hmit.modules.job.dto.WindowListResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 一体化运行监测
 */
@Service
public class IntegratedOperationMonitoringServiceImpl implements IntegratedOperationMonitoringService {

    @Autowired
    private ExternalApiService externalApiService;

    private static final List<String> DEPT_ORDER = Arrays.asList(
           "1",     //"人力社保服务区",
           "6",     //"医疗保障服务区",
           "2",     //"商事登记服务区",
           "7",     //"社会事务服务区",
           "3",     //"公安服务区",
           "4",     //"不动产服务区",
           "5"     //"公积金服务区"
    );

    @Override
    public GovHallOverviewDTO getOverview() {
        GovHallOverviewDTO govHallOverviewDTO = new GovHallOverviewDTO();

        // 获取部门数量
        DepartmentListResponse allDepartments = externalApiService.getAllDepartments();
        govHallOverviewDTO.setDeptCount(allDepartments.getData().size());

        // 获取事项数量
        QueueListResponse queues = externalApiService.getAllQueues();
        govHallOverviewDTO.setBusinessCount(queues.getData().size());

        // 获取窗口数量
        WindowListResponse windows = externalApiService.getAllWindows();
        govHallOverviewDTO.setWindowCount(windows.getData().size());

        return govHallOverviewDTO;

    }

    @Override
    public List<DeptRankingDTO> getDeptRanking() {
        // 方法实现
        return null;
    }

    @Override
    public List<QueueStatisticsResponse.QueueStatisticsData> getQueueRanking() {
        // 方法实现
        return null;
    }

    @Override
    public ReviewStatisticsDTO getReviewStatistics() {
        // 方法实现
        return null;
    }

    @Override
    public Map<String, Integer> getAnnualCaseClosureTrend() {
        // 方法实现
        return null;
    }

    @Override
    public List<Map<String, Object>> getQueueStatistics() {
        // 方法实现
        return null;
    }

    @Override
    public TodayCallStatusDTO getTodayCallStatus() {
        TodayCallStatusResponse todayCallStatus = externalApiService.getTodayCallStatus();
        DepartmentQuantityResponse departmentQuantity = externalApiService.getDepartmentQuantity();

//        System.out.println(todayCallStatus);

        TodayCallStatusDTO retDto = new TodayCallStatusDTO();
        retDto.setGettotal(todayCallStatus.getData().getGettotal());
        retDto.setWaittotal(todayCallStatus.getData().getWaittotal());

        final Map<String, Integer> deptGetCntMap = departmentQuantity.getData() != null ? 
            departmentQuantity.getData().stream()
                    .collect(Collectors.toMap(
                            DepartmentQuantityResponse.DepartmentQuantityData::getSDepId,
                            dept -> dept.getGetcnt() != null ? dept.getGetcnt() : 0
                    )) : new HashMap<>();

        List<TodayCallStatusResponse.QueueStatus> queues = todayCallStatus.getData().getQueues();
        if (queues == null) {
            throw new HmitException("外部接口 http://IP:7086/smartqueue/other/hallgetcall 返回queues为null");
        }
        Map<String, DeptCallInfoDTO> groupByDeptName = queues.stream()
                .collect(Collectors.groupingBy(TodayCallStatusResponse.QueueStatus::getSDepName,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> {
                                    DeptCallInfoDTO deptCallInfo = new DeptCallInfoDTO();
                                    deptCallInfo.setSDepId(list.get(0).getSDepId());
                                    deptCallInfo.setSDepName(list.get(0).getSDepName());

                                    BigDecimal avgWaitTime = BigDecimal.ZERO;
                                    int totalWaitTime = list.stream()
                                            .mapToInt(queue -> queue.getWaittime() != null ? queue.getWaittime() : 0)
                                            .sum();
                                    avgWaitTime = new BigDecimal(totalWaitTime)
                                            .divide(new BigDecimal(list.size()), 10, RoundingMode.HALF_UP);
                                    if (avgWaitTime.compareTo(BigDecimal.ZERO) > 0 && avgWaitTime.compareTo(BigDecimal.ONE) < 0) {
                                        //大于0小于1，取1
                                        avgWaitTime = BigDecimal.ONE;
                                    } else if (avgWaitTime.compareTo(BigDecimal.ZERO) == 0) {
                                        //等于0，取0
                                        avgWaitTime = BigDecimal.ZERO;
                                    } else {
                                        //否则向上取整
                                        avgWaitTime = avgWaitTime.setScale(0, RoundingMode.CEILING);
                                    }

                                    deptCallInfo.setWaittime(avgWaitTime.toString());
                                    deptCallInfo.setWaitcnt(list.stream()
                                            .mapToInt(queue -> queue.getWaitcnt() != null ? queue.getWaitcnt() : 0)
                                            .sum());
                                    deptCallInfo.setGetcnt(deptGetCntMap.getOrDefault(list.get(0).getSDepId(), 0));
                                    return deptCallInfo;
                                })));

        Comparator<DeptCallInfoDTO> deptComparator = Comparator.comparingInt(
                dept -> DEPT_ORDER.contains(dept.getSDepId()) ? DEPT_ORDER.indexOf(dept.getSDepId()) : Integer.MAX_VALUE
        );

        List<DeptCallInfoDTO> deptCallInfoDTOList = groupByDeptName.values().stream()
                .sorted(deptComparator)
                .collect(Collectors.toList());

        retDto.setDeptCallInfoList(deptCallInfoDTOList);

        return retDto;
    }
}

