package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.BadReviewDao;
import io.hmit.modules.cockpit.dto.BadReviewDTO;
import io.hmit.modules.cockpit.entity.BadReviewEntity;
import io.hmit.modules.cockpit.service.BadReviewService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>好差评 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class BadReviewServiceImpl extends CrudServiceImpl<BadReviewDao, BadReviewEntity, BadReviewDTO> implements BadReviewService {

    @Override
    public QueryWrapper<BadReviewEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<BadReviewEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}