package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.NetworkManagementSituationDao;
import io.hmit.modules.cockpit.dto.NetworkManagementSituationDTO;
import io.hmit.modules.cockpit.entity.NetworkManagementSituationEntity;
import io.hmit.modules.cockpit.service.NetworkManagementSituationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>网办情况 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-31
 */
@Slf4j
@Service
public class NetworkManagementSituationServiceImpl extends CrudServiceImpl<NetworkManagementSituationDao, NetworkManagementSituationEntity, NetworkManagementSituationDTO> implements NetworkManagementSituationService {

    @Override
    public QueryWrapper<NetworkManagementSituationEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<NetworkManagementSituationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}