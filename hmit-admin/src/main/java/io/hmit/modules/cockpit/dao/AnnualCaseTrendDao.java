package io.hmit.modules.cockpit.dao;

import io.hmit.common.dao.BaseDao;
import io.hmit.modules.cockpit.entity.AnnualCaseTrendEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <h1>${comments}</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-19
 */
@Mapper
public interface AnnualCaseTrendDao extends BaseDao<AnnualCaseTrendEntity> {

    //获取最近N个月的数据，并按月份升序排序
	public List<AnnualCaseTrendEntity> selectRecentNMonthsAsc(@Param("n") int n,@Param("endMonthStr") String endMonthStr);
}