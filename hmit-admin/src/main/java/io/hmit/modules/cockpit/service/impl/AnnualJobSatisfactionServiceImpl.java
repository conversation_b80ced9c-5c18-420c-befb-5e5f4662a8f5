package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.AnnualJobSatisfactionDao;
import io.hmit.modules.cockpit.dto.AnnualJobSatisfactionDTO;
import io.hmit.modules.cockpit.entity.AnnualJobSatisfactionEntity;
import io.hmit.modules.cockpit.service.AnnualJobSatisfactionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>全年办件满意度分析 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-30
 */
@Slf4j
@Service
public class AnnualJobSatisfactionServiceImpl extends CrudServiceImpl<AnnualJobSatisfactionDao, AnnualJobSatisfactionEntity, AnnualJobSatisfactionDTO> implements AnnualJobSatisfactionService {

    @Override
    public QueryWrapper<AnnualJobSatisfactionEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<AnnualJobSatisfactionEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}