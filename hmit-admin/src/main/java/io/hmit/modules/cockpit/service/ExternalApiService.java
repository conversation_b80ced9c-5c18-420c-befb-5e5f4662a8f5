package io.hmit.modules.cockpit.service;

import io.hmit.common.exception.HmitException;
import io.hmit.modules.cockpit.dto.*;
import io.hmit.modules.job.dto.*;

public interface ExternalApiService {
    //7086端口
    DepartmentListResponse getAllDepartments() throws HmitException;
    QueueListResponse getAllQueues() throws HmitException;
    WindowListResponse getAllWindows() throws HmitException;
    DeptStatisticsResponse getDepartmentStatistics(String startDate, String endDate) throws HmitException;
    QueueStatisticsResponse getQueueStatistics(String startDate, String endDate) throws HmitException;
    HallStatisticsResponse getHallStatistics(String startDate, String endDate) throws HmitException;
    TodayCallStatusResponse getTodayCallStatus() throws HmitException;
    DepartmentQuantityResponse getDepartmentQuantity() throws HmitException;
    
    // 7087端口
    QueueInfoResponse getQueueWaitInfo() throws HmitException;
    BadReviewResponse getBadReviews() throws HmitException; 
    WinMonitorResponse getWindowMonitorInfo() throws HmitException;
    UserListResponse getUserList() throws HmitException;
    HallStatResponse getHallStatistics2(String startDate, String endDate) throws HmitException;
}

