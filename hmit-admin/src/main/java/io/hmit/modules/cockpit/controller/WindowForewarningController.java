package io.hmit.modules.cockpit.controller;

import io.hmit.common.utils.Result;
import io.hmit.modules.cockpit.dto.WindowData;
import io.hmit.modules.cockpit.dto.WindowDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("cockpit")
@Api("窗口承载能力监测")
public class WindowForewarningController {

    @ApiOperation("窗口承载能力监测列表")
    @GetMapping("getWindowForewarning")
    public Result getWindowForewarning() {
        ArrayList<WindowDto> list = new ArrayList<>();

        WindowDto windowDto = new WindowDto("人力社保",null);
        WindowDto windowDto1 = new WindowDto("公安",null);
        WindowData windowData = new WindowData("A101", "王天", "在岗", null);
        WindowData windowData1 = new WindowData("A102", "张一项", "在岗", "5");
        WindowData windowData2 = new WindowData("A103", "李章为", "在岗", null);
        WindowData windowData3 = new WindowData("A104", "彭先知", "在岗", null);
        WindowData windowData4 = new WindowData("A105", "王丹按", "在岗", "5");
        WindowData windowData5 = new WindowData("A106", "李白", "在岗", null);
        WindowData windowData6 = new WindowData("A107", "王为", "离岗", null);
        WindowData windowData7 = new WindowData("A108", "张一拿", "在岗", "5");
        WindowData windowData8 = new WindowData("A109", "呼伦贝", "在岗", null);
        ArrayList<WindowData> windowDataList = new ArrayList<>();
        windowDataList.add(windowData);
        windowDataList.add(windowData1);
        windowDataList.add(windowData2);
        windowDataList.add(windowData3);
        windowDataList.add(windowData4);
        windowDataList.add(windowData5);
        windowDataList.add(windowData6);
        windowDataList.add(windowData7);
        windowDataList.add(windowData8);
        windowDto.setList(windowDataList);
        list.add(windowDto);
        list.add(windowDto1);
        return new Result<List<WindowDto>>().ok(list);
    }

    /**
     * 获取预警阈值
     * @return
     */
    @ApiOperation("获取预警阈值")
    @GetMapping("getoverwarning")
    public Result getoverwarning() {
        HashMap<Object, Object> map = new HashMap<>();
        map.put("deptName","人力资源");
        map.put("window","窗口A102");
        map.put("serviceName","基本养老保险");
        map.put("number",25);
        return new Result().ok(map);
    }
}
