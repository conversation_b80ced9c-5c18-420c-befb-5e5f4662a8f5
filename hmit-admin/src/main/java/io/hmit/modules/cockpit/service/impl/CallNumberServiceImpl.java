package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.CallNumberDao;
import io.hmit.modules.cockpit.dto.CallNumberDTO;
import io.hmit.modules.cockpit.entity.CallNumberEntity;
import io.hmit.modules.cockpit.service.CallNumberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>取叫号 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class CallNumberServiceImpl extends CrudServiceImpl<CallNumberDao, CallNumberEntity, CallNumberDTO> implements CallNumberService {

    @Override
    public QueryWrapper<CallNumberEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<CallNumberEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}