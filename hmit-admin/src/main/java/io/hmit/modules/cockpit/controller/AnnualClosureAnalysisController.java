package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.AnnualClosureAnalysisDTO;
import io.hmit.modules.cockpit.excel.AnnualClosureAnalysisExcel;
import io.hmit.modules.cockpit.service.AnnualClosureAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>年度办结分析</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-07
 */
@RestController
@RequestMapping("cockpit/annualclosureanalysis")
@Api(tags="年度办结分析")
public class AnnualClosureAnalysisController {

    private final AnnualClosureAnalysisService annualClosureAnalysisService;

    public AnnualClosureAnalysisController(AnnualClosureAnalysisService annualClosureAnalysisService) {
        this.annualClosureAnalysisService = annualClosureAnalysisService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:annualclosureanalysis:page")
    public Result<PageData<AnnualClosureAnalysisDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<AnnualClosureAnalysisDTO> page = annualClosureAnalysisService.page(params);

        return new Result<PageData<AnnualClosureAnalysisDTO>>().ok(page);
    }


    @GetMapping()
    @ApiOperation("查询")
//    @RequiresPermissions("cockpit:annualclosureanalysis:page")
    public Result query(@ApiIgnore @RequestParam Map<String, Object> params){
        List<AnnualClosureAnalysisDTO> list = annualClosureAnalysisService.list(params);
        return new Result().ok(list.get(0));
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("cockpit:annualclosureanalysis:info")
    public Result<AnnualClosureAnalysisDTO> get(@PathVariable("id") Long id){
        AnnualClosureAnalysisDTO data = annualClosureAnalysisService.get(id);

        return new Result<AnnualClosureAnalysisDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
//    @RequiresPermissions("cockpit:annualclosureanalysis:save")
    public Result<Object> save(@RequestBody AnnualClosureAnalysisDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        annualClosureAnalysisService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("cockpit:annualclosureanalysis:update")
    public Result<Object> update(@RequestBody AnnualClosureAnalysisDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        annualClosureAnalysisService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:annualclosureanalysis:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        annualClosureAnalysisService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:annualclosureanalysis:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<AnnualClosureAnalysisDTO> list = annualClosureAnalysisService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, AnnualClosureAnalysisExcel.class);
    }

}