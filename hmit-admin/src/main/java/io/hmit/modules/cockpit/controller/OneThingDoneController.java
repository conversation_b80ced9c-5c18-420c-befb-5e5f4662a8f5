package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.OneThingDoneDTO;
import io.hmit.modules.cockpit.excel.OneThingDoneExcel;
import io.hmit.modules.cockpit.service.OneThingDoneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>${comments}</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-16
 */
@RestController
@RequestMapping("cockpit/onethingdone")
@Api(tags="高效办成一件事")
public class OneThingDoneController {

    private final OneThingDoneService oneThingDoneService;

    public OneThingDoneController(OneThingDoneService oneThingDoneService) {
        this.oneThingDoneService = oneThingDoneService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:onethingdone:page")
    public Result<PageData<OneThingDoneDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<OneThingDoneDTO> page = oneThingDoneService.page(params);

        return new Result<PageData<OneThingDoneDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("cockpit:onethingdone:info")
    public Result<OneThingDoneDTO> get(@PathVariable("id") Long id){
        OneThingDoneDTO data = oneThingDoneService.get(id);

        return new Result<OneThingDoneDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
//    @RequiresPermissions("cockpit:onethingdone:save")
    public Result<Object> save(@RequestBody OneThingDoneDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        oneThingDoneService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("cockpit:onethingdone:update")
    public Result<Object> update(@RequestBody OneThingDoneDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        oneThingDoneService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
//    @RequiresPermissions("cockpit:onethingdone:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        oneThingDoneService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:onethingdone:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<OneThingDoneDTO> list = oneThingDoneService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, OneThingDoneExcel.class);
    }

}