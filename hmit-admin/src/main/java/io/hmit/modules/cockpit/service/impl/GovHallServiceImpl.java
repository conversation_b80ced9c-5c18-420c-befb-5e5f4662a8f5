package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.GovHallDao;
import io.hmit.modules.cockpit.dto.GovHallDTO;
import io.hmit.modules.cockpit.entity.GovHallEntity;
import io.hmit.modules.cockpit.service.GovHallService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>政务大厅概况 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class GovHallServiceImpl extends CrudServiceImpl<GovHallDao, GovHallEntity, GovHallDTO> implements GovHallService {

    @Override
    public QueryWrapper<GovHallEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<GovHallEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}