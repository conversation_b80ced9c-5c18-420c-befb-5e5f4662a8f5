package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.GovernmentServiceTwoDao;
import io.hmit.modules.cockpit.dto.GovernmentServiceTwoDTO;
import io.hmit.modules.cockpit.entity.GovernmentServiceTwoEntity;
import io.hmit.modules.cockpit.service.GovernmentServiceTwoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>政务服务2.0 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-07
 */
@Slf4j
@Service
public class GovernmentServiceTwoServiceImpl extends CrudServiceImpl<GovernmentServiceTwoDao, GovernmentServiceTwoEntity, GovernmentServiceTwoDTO> implements GovernmentServiceTwoService {

    @Override
    public QueryWrapper<GovernmentServiceTwoEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<GovernmentServiceTwoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}