package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.ChannelAnalysisDao;
import io.hmit.modules.cockpit.dto.ChannelAnalysisDTO;
import io.hmit.modules.cockpit.entity.ChannelAnalysisEntity;
import io.hmit.modules.cockpit.service.ChannelAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>年度收件渠道分析 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-07
 */
@Slf4j
@Service
public class ChannelAnalysisServiceImpl extends CrudServiceImpl<ChannelAnalysisDao, ChannelAnalysisEntity, ChannelAnalysisDTO> implements ChannelAnalysisService {

    @Override
    public QueryWrapper<ChannelAnalysisEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<ChannelAnalysisEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}