package io.hmit.modules.cockpit.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import io.hmit.common.utils.Result;
import io.hmit.modules.cockpit.dto.*;
import io.hmit.modules.cockpit.dto.DeptRankingDTO;
import io.hmit.modules.cockpit.dto.ReviewStatisticsDTO;
import io.hmit.modules.cockpit.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("integratedOperationMonitoring")
@Api(tags = "一体化运行监测")
@Slf4j
public class IntegratedOperationMonitoringController {


    @Value("${HZZH.IP}")
    private String apiIp;

    @Value("${HZZH.STATISTIC_IP}")
    private String statisticIP;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private OneThingDoneService oneThingDoneService;

    @Autowired
    private ReceiptLeaderboardService receiptLeaderboardService;

    @Autowired
    private BusinessLeaderboardService businessLeaderboardService;

    @Autowired
    private AnnualCaseTrendService annualCaseTrendService;

    @Autowired
    private FeedbackAnalysisService feedbackAnalysisService;

    @Autowired
    private HallOverviewService hallOverviewService;
    @Autowired
    private IntegratedOperationMonitoringService integratedOperationMonitoringService;


    @GetMapping("overview")
    @ApiOperation("政务大厅概况")
    public Result<GovHallOverviewDTO> overview() {
        List<HallOverviewDTO> list = hallOverviewService.list(new HashMap<>());
        if(CollectionUtils.isEmpty(list)) {
            return new Result<GovHallOverviewDTO>().error("政务大厅概况无数据，请确认是否已配置");
        }
        HallOverviewDTO inDb =list.get(0);
        GovHallOverviewDTO retDTO = new GovHallOverviewDTO();
        BeanUtils.copyProperties(inDb, retDTO);

        String deptRange = inDb.getDeptRange();
        Map<String, String> deptRangeMap = new HashMap<>();
        if (StringUtils.isNotBlank(deptRange)) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                deptRangeMap = objectMapper.readValue(deptRange, new TypeReference<LinkedHashMap<String, String>>() {
                });
            } catch (Exception e) {
                log.error("deptRange解析JSON失败: {}", deptRange, e);
            }
        }
        retDTO.setDeptRange(deptRangeMap); // 设置解析后的部门范围信息

        return new Result<GovHallOverviewDTO>().ok(retDTO);
    }

    @GetMapping("deptRanking")
    @ApiOperation("部门收件数Top5")
    public Result<List<DeptRankingDTO>> deptRanking() {

//        String url = statisticIP + "/smartqueue/report/dep?sStart=2024-01-01&sEnd=2056-01-01";
//        ResponseEntity<DeptStatisticsResponse> response = restTemplate.getForEntity(url, DeptStatisticsResponse.class);
//        DeptStatisticsResponse body = response.getBody();
//        if (body == null || body.getCode() != 0 || body.getData() == null) {
//            throw new HmitException("获取部门收件数Top5接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
//        }
//        List<DeptStatisticsResponse.DeptStatisticsData> dataList = body.getData();
//        dataList = dataList.stream().sorted((data1, data2) -> data2.getNTotal() - data1.getNTotal()).limit(Math.min(dataList.size(), 5)).collect(Collectors.toList());
//
//        List<DeptRankingDTO> retList = dataList.stream().map(e -> new DeptRankingDTO(e.getSDepName(), e.getNTotal())).collect(Collectors.toList());

//        return new Result().ok(retList);

//        List<DeptRankingDTO> deptStatsList2 = new ArrayList<>();
//
//        deptStatsList2.add(new DeptRankingDTO("余姚市税务局", 712887));
//        deptStatsList2.add(new DeptRankingDTO("余姚市人力资源和社会保障局", 277489));
//        deptStatsList2.add(new DeptRankingDTO("余姚市医疗保障局", 123550));
//        deptStatsList2.add(new DeptRankingDTO("余姚市公安局", 118256));
//        deptStatsList2.add(new DeptRankingDTO("余姚市住房公积金中心", 48977));

        List<ReceiptLeaderboardDTO> top5 = receiptLeaderboardService.getTop5();
        List<DeptRankingDTO>retList=new ArrayList<>();
        for (ReceiptLeaderboardDTO dto : top5) {
            DeptRankingDTO deptRankingDTO = new DeptRankingDTO();
            deptRankingDTO.setSDepName(dto.getSDepName());
            deptRankingDTO.setNTotal(dto.getNTotal());
            retList.add(deptRankingDTO);
        }
        return new Result<List<DeptRankingDTO>>().ok(retList);

    }

    @GetMapping("queueRanking")
    @ApiOperation("政务事项Top10")
    public Result<List<QueueStatisticsResponse.QueueStatisticsData>> queueRanking() {
//        // 获取今天的日期
//        DateTime today = DateUtil.date();
//
//        // 获取当前年份的第一天
//        DateTime firstDayOfYear = DateUtil.beginOfYear(today);
//
//        // 格式化为字符串（默认格式 yyyy-MM-dd）
//        String todayStr = DateUtil.formatDate(today);
//        String firstDayOfYearStr = DateUtil.formatDate(firstDayOfYear);
//
//        String url = statisticIP + String.format("/smartqueue/report/queue?sStart=%s&sEnd=%s",firstDayOfYearStr, todayStr);
//        ResponseEntity<QueueStatisticsResponse> response = restTemplate.getForEntity(url, QueueStatisticsResponse.class);
//        QueueStatisticsResponse body = response.getBody();
//        if (body == null || body.getCode() != 0 || body.getData() == null) {
//            throw new HmitException("获取队列统计信息接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
//        }
//
//        List<QueueStatisticsResponse.QueueStatisticsData> dataList = body.getData();
//        List<QueueStatisticsResponse.QueueStatisticsData> sortedList = dataList.stream().sorted((data1, data2) -> data2.getNTotal() - data1.getNTotal()).limit(Math.min(dataList.size(), 10)).collect(Collectors.toList());

//        return new Result().ok(sortedList);

        List<BusinessLeaderboardDTO> topN = businessLeaderboardService.getTopN(10);
        List<QueueStatisticsResponse.QueueStatisticsData>retList=new ArrayList<>();
        for (BusinessLeaderboardDTO each : topN) {
            QueueStatisticsResponse.QueueStatisticsData queueStatisticsData = new QueueStatisticsResponse.QueueStatisticsData();
            queueStatisticsData.setSDepName(each.getDeptName());
            queueStatisticsData.setSWinQueueName(each.getBusinessName());
            queueStatisticsData.setNTotal(each.getTotal());
            retList.add(queueStatisticsData);
        }

        return new Result<List<QueueStatisticsResponse.QueueStatisticsData>>().ok(retList);
    }

    @GetMapping("reviewAnalysis")
    @ApiOperation("好差评分析")
    public Result<ReviewStatisticsDTO> reviewStatistics() {
        List<FeedbackAnalysisDTO> list = feedbackAnalysisService.list(new HashMap<>());
        FeedbackAnalysisDTO feedback = list.get(0);
        ReviewStatisticsDTO retDto = new ReviewStatisticsDTO();
        retDto.setPositiveRate(feedback.getSatisfactionRate());
        retDto.setReviewCount(feedback.getTotalReviews());
        retDto.setNApp(Arrays.asList(
                feedback.getVerySatisfiedCount(),
                feedback.getSatisfiedCount(),
                feedback.getBasicallySatisfiedCount(),
                feedback.getDissatisfiedCount(),
                feedback.getVeryDissatisfiedCount()
        ));

        return new Result<ReviewStatisticsDTO>().ok(retDto);

//        String url = statisticIP + "/smartqueue/report/hall?sStart=2024-01-01&sEnd=2050-01-01";
//        ResponseEntity<HallStatisticsResponse> response = restTemplate.getForEntity(url, HallStatisticsResponse.class);
//        HallStatisticsResponse body = response.getBody();
//        if (body == null || body.getCode() != 0 || body.getData() == null) {
//            throw new HmitException("大厅统计接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
//        }
//        HallStatisticsResponse.HallStatisticsData data = body.getData();
//        ReviewStatisticsDTO reviewStatisticsDTO = new ReviewStatisticsDTO();
//        reviewStatisticsDTO.setNApp(data.getNApp());
//
//        Integer total = 0, positive = 0, negative = 0;
//        for (int i = 0; i < data.getNApp().size(); i++) {
//            Integer cur = data.getNApp().get(i);
//
//            if (i == 5) {//差评
//                negative += cur;
//            } else {//好评
//                positive += cur;
//            }
//
//            total += cur;
//        }
//
//        reviewStatisticsDTO.setReviewCount(total);
//        reviewStatisticsDTO.setPositiveRate(total == 0 ? 0.0 : (double) positive / total);
//        reviewStatisticsDTO.setVerySatisfiedRate(total == 0 ? 0.0 : (double) data.getNApp().get(1) / total);

    }

    @GetMapping("annualCaseClosureTrend")
    @ApiOperation("年度办件趋势")
    public Result<Map<String, Integer>> annualCaseClosureTrend() {

        //获取上个月的yyyy-MM字符串
        String lastMonth = DateUtil.format(DateUtil.beginOfMonth(DateUtil.offsetMonth(DateUtil.date(), -1)), "yyyy-MM");

        List<AnnualCaseTrendDTO> recent12MonthsTrend = annualCaseTrendService.getRecentNMonthsTrend(12, lastMonth);
        Map<String, Integer> map = recent12MonthsTrend.stream()
                .collect(Collectors.toMap(
                        AnnualCaseTrendDTO::getMonth,
                        AnnualCaseTrendDTO::getCaseCount,
                        (existing, replacement) -> replacement,
                        LinkedHashMap::new));
        return new Result<Map<String, Integer>>().ok(map);

//        Map<String, Integer> map = new LinkedHashMap<>();
//        for (int i = 11; i >= 0; i--) {
//            DateTime now = DateTime.now();
//            // 计算第 i 个月的起始和结束时间
//            DateTime monthStart = DateUtil.beginOfMonth(DateUtil.offsetMonth(now, -i));
//            DateTime monthEnd = DateUtil.endOfMonth(monthStart);
//
//            String sStart = monthStart.toDateStr();
//            String sEnd = monthEnd.toDateStr();
//
//            String url = statisticIP + String.format("/smartqueue/report/hall?sStart=%s&sEnd=%s", sStart, sEnd);
//            ResponseEntity<HallStatisticsResponse> response = restTemplate.getForEntity(url, HallStatisticsResponse.class);
//            HallStatisticsResponse body = response.getBody();
//            if (body == null || body.getCode() != 0 || body.getData() == null) {
//                throw new HmitException("大厅统计接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
//            }
//
//            HallStatisticsResponse.HallStatisticsData data = body.getData();
//            map.put(monthStart.toString("yyyy-MM"), data.getNTotal());
//        }
//        return new Result().ok(map2);

    }

    @GetMapping("queueStatistics")
    @ApiOperation("高效办成一件事")
    public Result<Object> queueStatistics() {

        List<OneThingDoneDTO> list = oneThingDoneService.list(new HashMap<>());
        return new Result<>().ok(list);

//        List<IntegratedServicesDTO> list = integratedServicesService.list(new HashMap<>());
//        Map<String, IntegratedServicesDTO> map = list.stream().collect(Collectors.toMap(IntegratedServicesDTO::getItemName, Function.identity(), (existing, replacement) -> replacement));
//
//        // 获取当前日期
//        DateTime now = DateUtil.date();
//
//        // 获取上个月的第一天
//        String firstDayOfPreviousMonth = DateUtil.beginOfMonth(DateUtil.offsetMonth(now, -1)).toDateStr();
//
//        // 获取上个月的最后一天
//        String lastDayOfPreviousMonth = DateUtil.endOfMonth(DateUtil.offsetMonth(now, -1)).toDateStr();
//
//        String url = statisticIP + String.format("/smartqueue/report/queue?sStart=%s&sEnd=%s", firstDayOfPreviousMonth, lastDayOfPreviousMonth);
//        ResponseEntity<QueueStatisticsResponse> response = restTemplate.getForEntity(url, QueueStatisticsResponse.class);
//        QueueStatisticsResponse body = response.getBody();
//        if (body == null || body.getCode() != 0 || body.getData() == null) {
//            throw new HmitException("获取队列统计信息接口调用失败：" + (body != null ? body.getMsg() : "未知错误"));
//        }
//
//        List<QueueStatisticsResponse.QueueStatisticsData> dataList = body.getData();
//        System.out.println(dataList);
//
//        List<QueueStatisticsDTO> retList = new ArrayList<>();
//        for (QueueStatisticsResponse.QueueStatisticsData data : dataList) {
//            QueueStatisticsDTO dto = new QueueStatisticsDTO();
//            dto.setSWinQueueName(data.getSWinQueueName());
//            dto.setNTotal(data.getNTotal());
//            IntegratedServicesDTO integratedServicesDTO = map.get(data.getSWinQueueName());
//            if (integratedServicesDTO != null) {
//                dto.setProcessingDays(integratedServicesDTO.getOriginalProcessingDays() - integratedServicesDTO.getTimeSavedDays());
//                dto.setBusinessCount(integratedServicesDTO.getOriginalSteps() - integratedServicesDTO.getStepsReduced());
//            } else {
//                dto.setProcessingDays(0);
//                dto.setBusinessCount(0);
//            }
//            retList.add(dto);
//        }
//        retList = retList.stream().sorted(Comparator.comparingInt(QueueStatisticsDTO::getNTotal).reversed()).collect(Collectors.toList());
//
//        return new Result<>().ok(retList);

    }

    @GetMapping("todayCallStatus")
    @ApiOperation("今日取叫号实况")
    public Result<TodayCallStatusDTO> getTodayCallStatus() {
        return new Result<TodayCallStatusDTO>().ok(integratedOperationMonitoringService.getTodayCallStatus());
    }

}
