package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.AddresseeDao;
import io.hmit.modules.cockpit.dto.AddresseeDTO;
import io.hmit.modules.cockpit.entity.AddresseeEntity;
import io.hmit.modules.cockpit.service.AddresseeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>2.0收件数排名 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-31
 */
@Slf4j
@Service
public class AddresseeServiceImpl extends CrudServiceImpl<AddresseeDao, AddresseeEntity, AddresseeDTO> implements AddresseeService {

    @Override
    public QueryWrapper<AddresseeEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<AddresseeEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}