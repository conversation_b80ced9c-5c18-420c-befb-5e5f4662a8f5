package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.TeamHonorDao;
import io.hmit.modules.cockpit.dto.TeamHonorDTO;
import io.hmit.modules.cockpit.entity.TeamHonorEntity;
import io.hmit.modules.cockpit.service.TeamHonorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>团队荣誉 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class TeamHonorServiceImpl extends CrudServiceImpl<TeamHonorDao, TeamHonorEntity, TeamHonorDTO> implements TeamHonorService {

    @Override
    public QueryWrapper<TeamHonorEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<TeamHonorEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}