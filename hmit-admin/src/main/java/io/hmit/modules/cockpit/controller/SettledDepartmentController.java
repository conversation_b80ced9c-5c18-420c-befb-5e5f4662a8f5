package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.SettledDepartmentDTO;
import io.hmit.modules.cockpit.excel.SettledDepartmentExcel;
import io.hmit.modules.cockpit.service.SettledDepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>入驻部门</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-31
 */
@RestController
@RequestMapping("cockpit/settleddepartment")
@Api(tags="入驻部门")
public class SettledDepartmentController {

    private final SettledDepartmentService settledDepartmentService;

    public SettledDepartmentController(SettledDepartmentService settledDepartmentService) {
        this.settledDepartmentService = settledDepartmentService;
    }

    @GetMapping
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:settleddepartment:page")
    public Result<PageData<SettledDepartmentDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        params.put("page","1");
        params.put("limit","3");
        PageData<SettledDepartmentDTO> page = settledDepartmentService.page(params);

        return new Result<PageData<SettledDepartmentDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:settleddepartment:info")
    public Result<SettledDepartmentDTO> get(@PathVariable("id") Long id){
        SettledDepartmentDTO data = settledDepartmentService.get(id);

        return new Result<SettledDepartmentDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:settleddepartment:save")
    public Result<Object> save(@RequestBody SettledDepartmentDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        settledDepartmentService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:settleddepartment:update")
    public Result<Object> update(@RequestBody SettledDepartmentDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        settledDepartmentService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:settleddepartment:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        settledDepartmentService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:settleddepartment:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<SettledDepartmentDTO> list = settledDepartmentService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, SettledDepartmentExcel.class);
    }

}