package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.ServiceStarDTO;
import io.hmit.modules.cockpit.excel.ServiceStarExcel;
import io.hmit.modules.cockpit.service.ServiceStarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>服务明星</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/servicestar")
@Api(tags="服务明星")
public class ServiceStarController {

    private final ServiceStarService serviceStarService;

    public ServiceStarController(ServiceStarService serviceStarService) {
        this.serviceStarService = serviceStarService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:servicestar:page")
    public Result<PageData<ServiceStarDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<ServiceStarDTO> page = serviceStarService.page(params);

        return new Result<PageData<ServiceStarDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:servicestar:info")
    public Result<ServiceStarDTO> get(@PathVariable("id") Long id){
        ServiceStarDTO data = serviceStarService.get(id);

        return new Result<ServiceStarDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:servicestar:save")
    public Result<Object> save(@RequestBody ServiceStarDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        serviceStarService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:servicestar:update")
    public Result<Object> update(@RequestBody ServiceStarDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        serviceStarService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:servicestar:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        serviceStarService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:servicestar:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<ServiceStarDTO> list = serviceStarService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, ServiceStarExcel.class);
    }

}