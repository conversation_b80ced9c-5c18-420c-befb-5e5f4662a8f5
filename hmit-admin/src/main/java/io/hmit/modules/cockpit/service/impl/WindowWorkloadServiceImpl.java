package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.WindowWorkloadDao;
import io.hmit.modules.cockpit.dto.WindowWorkloadDTO;
import io.hmit.modules.cockpit.entity.WindowWorkloadEntity;
import io.hmit.modules.cockpit.service.WindowWorkloadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>窗口工作量 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class WindowWorkloadServiceImpl extends CrudServiceImpl<WindowWorkloadDao, WindowWorkloadEntity, WindowWorkloadDTO> implements WindowWorkloadService {

    @Override
    public QueryWrapper<WindowWorkloadEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<WindowWorkloadEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}