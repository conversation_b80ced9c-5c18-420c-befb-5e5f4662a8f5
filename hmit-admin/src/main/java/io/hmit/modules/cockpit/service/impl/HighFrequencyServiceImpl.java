package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.HighFrequencyDao;
import io.hmit.modules.cockpit.dto.HighFrequencyDTO;
import io.hmit.modules.cockpit.entity.HighFrequencyEntity;
import io.hmit.modules.cockpit.service.HighFrequencyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>高频事项 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class HighFrequencyServiceImpl extends CrudServiceImpl<HighFrequencyDao, HighFrequencyEntity, HighFrequencyDTO> implements HighFrequencyService {

    @Override
    public QueryWrapper<HighFrequencyEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<HighFrequencyEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}