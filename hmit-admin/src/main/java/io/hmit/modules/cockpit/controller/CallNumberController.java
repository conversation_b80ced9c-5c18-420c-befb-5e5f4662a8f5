package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.CallNumberDTO;
import io.hmit.modules.cockpit.excel.CallNumberExcel;
import io.hmit.modules.cockpit.service.CallNumberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>取叫号</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/callnumber")
@Api(tags="取叫号")
public class CallNumberController {

    private final CallNumberService callNumberService;

    public CallNumberController(CallNumberService callNumberService) {
        this.callNumberService = callNumberService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:callnumber:page")
    public Result page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<CallNumberDTO> page = callNumberService.page(params);
        CallNumberDTO callNumberDTO = callNumberService.get(1L);

        return new Result().ok(callNumberDTO);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:callnumber:info")
    public Result<CallNumberDTO> get(@PathVariable("id") Long id){
        CallNumberDTO data = callNumberService.get(id);

        return new Result<CallNumberDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:callnumber:save")
    public Result<Object> save(@RequestBody CallNumberDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        callNumberService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:callnumber:update")
    public Result<Object> update(@RequestBody CallNumberDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        callNumberService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:callnumber:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        callNumberService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:callnumber:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<CallNumberDTO> list = callNumberService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, CallNumberExcel.class);
    }

}