package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.HallOverviewDao;
import io.hmit.modules.cockpit.dto.HallOverviewDTO;
import io.hmit.modules.cockpit.entity.HallOverviewEntity;
import io.hmit.modules.cockpit.service.HallOverviewService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>${comments} Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-16
 */
@Slf4j
@Service
public class HallOverviewServiceImpl extends CrudServiceImpl<HallOverviewDao, HallOverviewEntity, HallOverviewDTO> implements HallOverviewService {

    @Override
    public QueryWrapper<HallOverviewEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<HallOverviewEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}