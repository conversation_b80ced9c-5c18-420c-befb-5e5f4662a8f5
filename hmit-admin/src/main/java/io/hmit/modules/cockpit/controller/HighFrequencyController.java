package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.HighFrequencyDTO;
import io.hmit.modules.cockpit.excel.HighFrequencyExcel;
import io.hmit.modules.cockpit.service.HighFrequencyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <h1>高频事项</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/highfrequency")
@Api(tags="高频事项")
public class HighFrequencyController {

    private final HighFrequencyService highFrequencyService;

    public HighFrequencyController(HighFrequencyService highFrequencyService) {
        this.highFrequencyService = highFrequencyService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:highfrequency:page")
    public Result<PageData<HighFrequencyDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        params.put("orderField", "number");
        params.put("order", "desc");
        params.put("limit", "10");
        params.put("page", "1");
        PageData<HighFrequencyDTO> page = highFrequencyService.page(params);

        return new Result<PageData<HighFrequencyDTO>>().ok(page);
    }

    @GetMapping("statistics")
    @ApiOperation("统计")
//    @RequiresPermissions("cockpit:highfrequency:page")
    public Result statistics(@ApiIgnore @RequestParam Map<String, Object> params){
        params.put("orderField", "number");
        params.put("order", "desc");
        params.put("limit", "6");
        params.put("page", "1");
        PageData<HighFrequencyDTO> page = highFrequencyService.page(params);
        List<HighFrequencyDTO> list = page.getList();
        HashMap<Object, Object> map = new HashMap<>();
        int total = 0;
        for (HighFrequencyDTO highFrequencyDTO : list) {
            total += highFrequencyDTO.getNumber();
        }
        for (HighFrequencyDTO highFrequencyDTO : list) {
            map.put(highFrequencyDTO.getTransactionName(), highFrequencyDTO.getNumber()*100/total);
        }
        return new Result().ok(map);
    }



    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:highfrequency:info")
    public Result<HighFrequencyDTO> get(@PathVariable("id") Long id){
        HighFrequencyDTO data = highFrequencyService.get(id);

        return new Result<HighFrequencyDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:highfrequency:save")
    public Result<Object> save(@RequestBody HighFrequencyDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        highFrequencyService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:highfrequency:update")
    public Result<Object> update(@RequestBody HighFrequencyDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        highFrequencyService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:highfrequency:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        highFrequencyService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:highfrequency:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<HighFrequencyDTO> list = highFrequencyService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, HighFrequencyExcel.class);
    }

}