package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.ServiceStarDao;
import io.hmit.modules.cockpit.dto.ServiceStarDTO;
import io.hmit.modules.cockpit.entity.ServiceStarEntity;
import io.hmit.modules.cockpit.service.ServiceStarService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>服务明星 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Slf4j
@Service
public class ServiceStarServiceImpl extends CrudServiceImpl<ServiceStarDao, ServiceStarEntity, ServiceStarDTO> implements ServiceStarService {

    @Override
    public QueryWrapper<ServiceStarEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<ServiceStarEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}