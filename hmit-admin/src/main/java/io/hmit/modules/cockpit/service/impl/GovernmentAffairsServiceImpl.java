package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.GovernmentAffairsDao;
import io.hmit.modules.cockpit.dto.GovernmentAffairsDTO;
import io.hmit.modules.cockpit.entity.GovernmentAffairsEntity;
import io.hmit.modules.cockpit.service.GovernmentAffairsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>政务2.0事项部门排名 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-31
 */
@Slf4j
@Service
public class GovernmentAffairsServiceImpl extends CrudServiceImpl<GovernmentAffairsDao, GovernmentAffairsEntity, GovernmentAffairsDTO> implements GovernmentAffairsService {

    @Override
    public QueryWrapper<GovernmentAffairsEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<GovernmentAffairsEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}