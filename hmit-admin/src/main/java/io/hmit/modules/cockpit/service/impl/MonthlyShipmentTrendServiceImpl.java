package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.MonthlyShipmentTrendDao;
import io.hmit.modules.cockpit.dto.MonthlyShipmentTrendDTO;
import io.hmit.modules.cockpit.entity.MonthlyShipmentTrendEntity;
import io.hmit.modules.cockpit.service.MonthlyShipmentTrendService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>每月办件趋势 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-31
 */
@Slf4j
@Service
public class MonthlyShipmentTrendServiceImpl extends CrudServiceImpl<MonthlyShipmentTrendDao, MonthlyShipmentTrendEntity, MonthlyShipmentTrendDTO> implements MonthlyShipmentTrendService {

    @Override
    public QueryWrapper<MonthlyShipmentTrendEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<MonthlyShipmentTrendEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}