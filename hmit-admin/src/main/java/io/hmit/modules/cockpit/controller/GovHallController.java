package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.exception.HmitException;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.GovHallDTO;
import io.hmit.modules.cockpit.dto.GovHallOverviewDTO;
import io.hmit.modules.cockpit.excel.GovHallExcel;
import io.hmit.modules.cockpit.service.GovHallService;
import io.hmit.modules.job.dto.DepartmentListResponse;
import io.hmit.modules.job.dto.HallStatResponse;
import io.hmit.modules.job.dto.QueueListResponse;
import io.hmit.modules.job.dto.WindowListResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>政务大厅概况</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/govhall")
@Api(tags="政务大厅概况")
public class GovHallController {


    private final GovHallService govHallService;

    public GovHallController(GovHallService govHallService,RestTemplate restTemplate) {
        this.govHallService = govHallService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:govhall:page")
    public Result<PageData<GovHallDTO>> page(){
        GovHallDTO govHallDTO = govHallService.get(1L);
        return new Result().ok(govHallDTO);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:govhall:info")
    public Result<GovHallDTO> get(@PathVariable("id") Long id){
        GovHallDTO data = govHallService.get(id);

        return new Result<GovHallDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:govhall:save")
    public Result<Object> save(@RequestBody GovHallDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        govHallService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:govhall:update")
    public Result<Object> update(@RequestBody GovHallDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        govHallService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:govhall:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        govHallService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:govhall:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<GovHallDTO> list = govHallService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, GovHallExcel.class);
    }

}