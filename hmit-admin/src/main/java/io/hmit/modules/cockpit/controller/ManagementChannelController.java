package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.ManagementChannelDTO;
import io.hmit.modules.cockpit.excel.ManagementChannelExcel;
import io.hmit.modules.cockpit.service.ManagementChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>办理渠道</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/managementchannel")
@Api(tags="办理渠道")
public class ManagementChannelController {

    private final ManagementChannelService managementChannelService;

    public ManagementChannelController(ManagementChannelService managementChannelService) {
        this.managementChannelService = managementChannelService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:managementchannel:page")
    public Result page(@ApiIgnore @RequestParam Map<String, Object> params){
//        PageData<ManagementChannelDTO> page = managementChannelService.page(params);
        ManagementChannelDTO managementChannelDTO = managementChannelService.get(1L);
        return new Result().ok(managementChannelDTO);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:managementchannel:info")
    public Result<ManagementChannelDTO> get(@PathVariable("id") Long id){
        ManagementChannelDTO data = managementChannelService.get(id);

        return new Result<ManagementChannelDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:managementchannel:save")
    public Result<Object> save(@RequestBody ManagementChannelDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        managementChannelService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:managementchannel:update")
    public Result<Object> update(@RequestBody ManagementChannelDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        managementChannelService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:managementchannel:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        managementChannelService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:managementchannel:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<ManagementChannelDTO> list = managementChannelService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, ManagementChannelExcel.class);
    }

}