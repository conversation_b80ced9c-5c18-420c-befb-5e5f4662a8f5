package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.hmit.common.page.PageData;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.common.utils.ConvertUtils;
import io.hmit.modules.cockpit.dao.ReceiptLeaderboardDao;
import io.hmit.modules.cockpit.dto.ReceiptLeaderboardDTO;
import io.hmit.modules.cockpit.entity.ReceiptLeaderboardEntity;
import io.hmit.modules.cockpit.service.ReceiptLeaderboardService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <h1>部门收件数排行 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-16
 */
@Slf4j
@Service
public class ReceiptLeaderboardServiceImpl extends CrudServiceImpl<ReceiptLeaderboardDao, ReceiptLeaderboardEntity, ReceiptLeaderboardDTO> implements ReceiptLeaderboardService {

    @Override
    public PageData<ReceiptLeaderboardDTO> page(Map<String, Object> params) {

        IPage<ReceiptLeaderboardEntity> entityPage = baseDao.selectPage(
                getPage(params, null, false),
                new LambdaQueryWrapper<ReceiptLeaderboardEntity>()
                        .orderByDesc(ReceiptLeaderboardEntity::getNTotal));

        PageData<ReceiptLeaderboardDTO> page = getPageData(entityPage, ReceiptLeaderboardDTO.class);
        return page;
    }

    @Override
    public List<ReceiptLeaderboardDTO> getTop5() {
        List<ReceiptLeaderboardEntity> entityList = baseDao.selectList(
                new LambdaQueryWrapper<ReceiptLeaderboardEntity>()
                        .orderByDesc(ReceiptLeaderboardEntity::getNTotal)
                        .last("LIMIT 5"));
        return ConvertUtils.sourceToTarget(entityList, ReceiptLeaderboardDTO.class);
    }

    @Override
    public QueryWrapper<ReceiptLeaderboardEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<ReceiptLeaderboardEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}