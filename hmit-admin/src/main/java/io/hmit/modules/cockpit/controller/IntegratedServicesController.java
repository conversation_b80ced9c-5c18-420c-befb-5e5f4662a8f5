package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.IntegratedServicesDTO;
import io.hmit.modules.cockpit.excel.IntegratedServicesExcel;
import io.hmit.modules.cockpit.service.IntegratedServicesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>“一件事”成效</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-06
 */
@RestController
@RequestMapping("cockpit/integratedservices")
@Api(tags="“一件事”成效")
public class IntegratedServicesController {

    private final IntegratedServicesService integratedServicesService;

    public IntegratedServicesController(IntegratedServicesService integratedServicesService) {
        this.integratedServicesService = integratedServicesService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:integratedservices:page")
    public Result<PageData<IntegratedServicesDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<IntegratedServicesDTO> page = integratedServicesService.page(params);

        return new Result<PageData<IntegratedServicesDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("cockpit:integratedservices:info")
    public Result<IntegratedServicesDTO> get(@PathVariable("id") Long id){
        IntegratedServicesDTO data = integratedServicesService.get(id);

        return new Result<IntegratedServicesDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:integratedservices:save")
    public Result<Object> save(@RequestBody IntegratedServicesDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        integratedServicesService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:integratedservices:update")
    public Result<Object> update(@RequestBody IntegratedServicesDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        integratedServicesService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:integratedservices:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        integratedServicesService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
//    @RequiresPermissions("cockpit:integratedservices:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<IntegratedServicesDTO> list = integratedServicesService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, IntegratedServicesExcel.class);
    }

}