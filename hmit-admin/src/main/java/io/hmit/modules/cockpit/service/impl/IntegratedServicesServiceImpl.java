package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.IntegratedServicesDao;
import io.hmit.modules.cockpit.dto.IntegratedServicesDTO;
import io.hmit.modules.cockpit.entity.IntegratedServicesEntity;
import io.hmit.modules.cockpit.service.IntegratedServicesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>“一件事”成效 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-06
 */
@Slf4j
@Service
public class IntegratedServicesServiceImpl extends CrudServiceImpl<IntegratedServicesDao, IntegratedServicesEntity, IntegratedServicesDTO> implements IntegratedServicesService {

    @Override
    public QueryWrapper<IntegratedServicesEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<IntegratedServicesEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}