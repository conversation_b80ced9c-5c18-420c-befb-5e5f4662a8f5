package io.hmit.modules.cockpit.service;

import io.hmit.modules.cockpit.dto.*;


import java.util.List;
import java.util.Map;

/**
 * 一体化运行监测
 */
public interface IntegratedOperationMonitoringService {
    
    /**
     * 获取政务大厅概况
     */
    GovHallOverviewDTO getOverview();
    
    /**
     * 获取部门收件数Top5
     */
    List<DeptRankingDTO> getDeptRanking();
    
    /**
     * 获取政务事项Top10
     */
    List<QueueStatisticsResponse.QueueStatisticsData> getQueueRanking();
    
    /**
     * 获取好差评分析
     */
    ReviewStatisticsDTO getReviewStatistics();
    
    /**
     * 获取年度办结趋势
     */
    Map<String, Integer> getAnnualCaseClosureTrend();
    
    /**
     * 获取高效办成一件事
     */
    List<Map<String, Object>> getQueueStatistics();

    TodayCallStatusDTO getTodayCallStatus();
}