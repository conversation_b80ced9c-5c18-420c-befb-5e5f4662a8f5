package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.AnnualClosureAnalysisDao;
import io.hmit.modules.cockpit.dto.AnnualClosureAnalysisDTO;
import io.hmit.modules.cockpit.entity.AnnualClosureAnalysisEntity;
import io.hmit.modules.cockpit.service.AnnualClosureAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>年度办结分析 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-07
 */
@Slf4j
@Service
public class AnnualClosureAnalysisServiceImpl extends CrudServiceImpl<AnnualClosureAnalysisDao, AnnualClosureAnalysisEntity, AnnualClosureAnalysisDTO> implements AnnualClosureAnalysisService {

    @Override
    public QueryWrapper<AnnualClosureAnalysisEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<AnnualClosureAnalysisEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}