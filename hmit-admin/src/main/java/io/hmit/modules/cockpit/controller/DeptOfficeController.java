package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.DeptOfficeDTO;
import io.hmit.modules.cockpit.excel.DeptOfficeExcel;
import io.hmit.modules.cockpit.service.DeptOfficeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>部门办件</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-25
 */
@RestController
@RequestMapping("cockpit/deptoffice")
@Api(tags="部门办件")
public class DeptOfficeController {

    private final DeptOfficeService deptOfficeService;

    public DeptOfficeController(DeptOfficeService deptOfficeService) {
        this.deptOfficeService = deptOfficeService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:deptoffice:page")
    public Result page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<DeptOfficeDTO> page = deptOfficeService.page(params);

        return new Result().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:deptoffice:info")
    public Result<DeptOfficeDTO> get(@PathVariable("id") Long id){
        DeptOfficeDTO data = deptOfficeService.get(id);

        return new Result<DeptOfficeDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:deptoffice:save")
    public Result<Object> save(@RequestBody DeptOfficeDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        deptOfficeService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:deptoffice:update")
    public Result<Object> update(@RequestBody DeptOfficeDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        deptOfficeService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:deptoffice:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        deptOfficeService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:deptoffice:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<DeptOfficeDTO> list = deptOfficeService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, DeptOfficeExcel.class);
    }

}