package io.hmit.modules.cockpit.service;

import io.hmit.common.page.PageData;
import io.hmit.common.service.CrudService;
import io.hmit.modules.cockpit.dto.DistrictMonthlyOfficeDTO;
import io.hmit.modules.cockpit.entity.DistrictMonthlyOfficeEntity;

import java.util.Map;

/**
 * <h1>地址月办件量 Service</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
public interface DistrictMonthlyOfficeService extends CrudService<DistrictMonthlyOfficeEntity, DistrictMonthlyOfficeDTO> {
    @Override
    PageData<DistrictMonthlyOfficeDTO> page(Map<String, Object> params);
}