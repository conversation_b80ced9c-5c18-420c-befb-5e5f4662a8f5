package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.ThermalMapDTO;
import io.hmit.modules.cockpit.excel.ThermalMapExcel;
import io.hmit.modules.cockpit.service.ThermalMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>政务大厅热流图</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/thermalmap")
@Api(tags="政务大厅热流图")
public class ThermalMapController {

    private final ThermalMapService thermalMapService;

    public ThermalMapController(ThermalMapService thermalMapService) {
        this.thermalMapService = thermalMapService;
    }

    @GetMapping
    @ApiOperation("分页")

//    @RequiresPermissions("cockpit:thermalmap:page")
    public Result<PageData<ThermalMapDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<ThermalMapDTO> page = thermalMapService.page(params);

        return new Result<PageData<ThermalMapDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:thermalmap:info")
    public Result<ThermalMapDTO> get(@PathVariable("id") Long id){
        ThermalMapDTO data = thermalMapService.get(id);

        return new Result<ThermalMapDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:thermalmap:save")
    public Result<Object> save(@RequestBody ThermalMapDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        thermalMapService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:thermalmap:update")
    public Result<Object> update(@RequestBody ThermalMapDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        thermalMapService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:thermalmap:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        thermalMapService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:thermalmap:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<ThermalMapDTO> list = thermalMapService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, ThermalMapExcel.class);
    }

}