package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.HallOverviewDTO;
import io.hmit.modules.cockpit.excel.HallOverviewExcel;
import io.hmit.modules.cockpit.service.HallOverviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import com.google.gson.Gson;

/**
 * <h1>${comments}</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-16
 */
@RestController
@RequestMapping("cockpit/halloverview")
@Api(tags="政务大厅概况 新")
public class HallOverviewController {

    private final HallOverviewService hallOverviewService;

    public HallOverviewController(HallOverviewService hallOverviewService) {
        this.hallOverviewService = hallOverviewService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:halloverview:page")
    public Result<PageData<HallOverviewDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<HallOverviewDTO> page = hallOverviewService.page(params);

        return new Result<PageData<HallOverviewDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("cockpit:halloverview:info")
    public Result<HallOverviewDTO> get(@PathVariable("id") Long id){
        HallOverviewDTO data = hallOverviewService.get(id);

        return new Result<HallOverviewDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
//    @RequiresPermissions("cockpit:halloverview:save")
    public Result<Object> save(@RequestBody HallOverviewDTO dto){
        // 校验deptRange是否是格式正确的JSON
        if (dto.getDeptRange() != null && !dto.getDeptRange().isEmpty()) {
            try {
                new Gson().fromJson(dto.getDeptRange(), Object.class);
            } catch (Exception e) {
                return new Result<>().error("deptRange不是有效的JSON格式");
            }
        }

        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        hallOverviewService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("cockpit:halloverview:update")
    public Result<Object> update(@RequestBody HallOverviewDTO dto){
        // 校验deptRange是否是格式正确的JSON
        if (dto.getDeptRange() != null && !dto.getDeptRange().isEmpty()) {
            try {
                new Gson().fromJson(dto.getDeptRange(), Object.class);
            } catch (Exception e) {
                return new Result<>().error("deptRange不是有效的JSON格式");
            }
        }

        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);


        hallOverviewService.update(dto);
        
        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
//    @RequiresPermissions("cockpit:halloverview:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        hallOverviewService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:halloverview:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<HallOverviewDTO> list = hallOverviewService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, HallOverviewExcel.class);
    }

}