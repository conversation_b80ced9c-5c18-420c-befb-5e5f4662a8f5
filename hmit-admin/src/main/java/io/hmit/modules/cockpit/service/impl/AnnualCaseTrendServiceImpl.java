package io.hmit.modules.cockpit.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.hmit.common.constant.Constant;
import io.hmit.common.exception.HmitException;
import io.hmit.common.page.PageData;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.common.utils.ConvertUtils;
import io.hmit.modules.cockpit.dao.AnnualCaseTrendDao;
import io.hmit.modules.cockpit.dto.AnnualCaseTrendDTO;
import io.hmit.modules.cockpit.entity.AnnualCaseTrendEntity;
import io.hmit.modules.cockpit.service.AnnualCaseTrendService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <h1>${comments} Service Impl</h1>
 *
 * <AUTHOR>
 * @since 1.0.0 2025-05-19
 */
@Slf4j
@Service
public class AnnualCaseTrendServiceImpl extends CrudServiceImpl<AnnualCaseTrendDao, AnnualCaseTrendEntity, AnnualCaseTrendDTO> implements AnnualCaseTrendService {

    //yyyy-MM格式的正则表达式
    private static final String YEAR_MONTH_REGEX = "^\\d{4}-(0[1-9]|1[0-2])$";

    @Override
    public List<AnnualCaseTrendDTO> getRecentNMonthsTrend(int n,String endMonthStr) {
        if (n < 1) {
            throw new HmitException("n必须大于0");
        }
        if (StringUtils.isBlank(endMonthStr) || !endMonthStr.matches(YEAR_MONTH_REGEX)) {
            throw new HmitException("lastMonth格式不正确，必须为yyyy-MM格式");
        }
        List<AnnualCaseTrendEntity> entityList =baseDao.selectRecentNMonthsAsc(n, endMonthStr);
        return ConvertUtils.sourceToTarget(entityList, AnnualCaseTrendDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(AnnualCaseTrendDTO dto) {
        Integer count = baseDao.selectCount(Wrappers.<AnnualCaseTrendEntity>lambdaQuery()
                .eq(AnnualCaseTrendEntity::getMonth, dto.getMonth())
                .ne(AnnualCaseTrendEntity::getId, dto.getId())
        );
        if (count > 0) {
            throw new HmitException("月份已存在");
        }
        super.update(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AnnualCaseTrendDTO dto) {
        Integer count = baseDao.selectCount(Wrappers.<AnnualCaseTrendEntity>lambdaQuery()
                .eq(AnnualCaseTrendEntity::getMonth, dto.getMonth())
        );
        if (count > 0) {
            throw new HmitException("月份已存在");
        }
        AnnualCaseTrendEntity entity = ConvertUtils.sourceToTarget(dto, AnnualCaseTrendEntity.class);
        insert(entity);
    }

    @Override
    public QueryWrapper<AnnualCaseTrendEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<AnnualCaseTrendEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}
