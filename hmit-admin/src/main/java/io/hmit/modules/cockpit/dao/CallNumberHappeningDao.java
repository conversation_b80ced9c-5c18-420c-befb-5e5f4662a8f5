package io.hmit.modules.cockpit.dao;

import io.hmit.common.dao.BaseDao;
import io.hmit.modules.cockpit.dto.NumberDto;
import io.hmit.modules.cockpit.entity.CallNumberHappeningEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <h1>取叫号实况</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@Mapper
public interface CallNumberHappeningDao extends BaseDao<CallNumberHappeningEntity> {

    List<CallNumberHappeningEntity> selectListByMaxUpdateNumber(@Param("date") String date);

    NumberDto getData();

}