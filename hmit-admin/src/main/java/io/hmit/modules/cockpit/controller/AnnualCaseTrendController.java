package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.AnnualCaseTrendDTO;
import io.hmit.modules.cockpit.excel.AnnualCaseTrendExcel;
import io.hmit.modules.cockpit.service.AnnualCaseTrendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>年度办件趋势（新）</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-19
 */
@RestController
@RequestMapping("cockpit/annualcasetrend")
@Api(tags="年度办件趋势（新）")
public class AnnualCaseTrendController {

    private final AnnualCaseTrendService annualCaseTrendService;

    public AnnualCaseTrendController(AnnualCaseTrendService annualCaseTrendService) {
        this.annualCaseTrendService = annualCaseTrendService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:annualcasetrend:page")
    public Result<PageData<AnnualCaseTrendDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<AnnualCaseTrendDTO> page = annualCaseTrendService.page(params);

        return new Result<PageData<AnnualCaseTrendDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("cockpit:annualcasetrend:info")
    public Result<AnnualCaseTrendDTO> get(@PathVariable("id") Long id){
        AnnualCaseTrendDTO data = annualCaseTrendService.get(id);

        return new Result<AnnualCaseTrendDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
//    @RequiresPermissions("cockpit:annualcasetrend:save")
    public Result<Object> save(@RequestBody AnnualCaseTrendDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto);

        annualCaseTrendService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("cockpit:annualcasetrend:update")
    public Result<Object> update(@RequestBody AnnualCaseTrendDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto);

        annualCaseTrendService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
//    @RequiresPermissions("cockpit:annualcasetrend:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        annualCaseTrendService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:annualcasetrend:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<AnnualCaseTrendDTO> list = annualCaseTrendService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, AnnualCaseTrendExcel.class);
    }

}