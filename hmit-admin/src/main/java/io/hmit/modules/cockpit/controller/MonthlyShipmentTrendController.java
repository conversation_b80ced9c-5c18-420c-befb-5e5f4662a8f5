package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.MonthlyShipmentTrendDTO;
import io.hmit.modules.cockpit.excel.MonthlyShipmentTrendExcel;
import io.hmit.modules.cockpit.service.MonthlyShipmentTrendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>每月办件趋势</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-31
 */
@RestController
@RequestMapping("cockpit/monthlyshipmenttrend")
@Api(tags="每月办件趋势")
public class MonthlyShipmentTrendController {

    private final MonthlyShipmentTrendService monthlyShipmentTrendService;

    public MonthlyShipmentTrendController(MonthlyShipmentTrendService monthlyShipmentTrendService) {
        this.monthlyShipmentTrendService = monthlyShipmentTrendService;
    }

    @GetMapping
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("cockpit:monthlyshipmenttrend:page")
    public Result<PageData<MonthlyShipmentTrendDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        params.put("page","1");
        params.put("limit","10");
        params.put("orderField","month");
        params.put("order","desc");
        PageData<MonthlyShipmentTrendDTO> page = monthlyShipmentTrendService.page(params);

        return new Result<PageData<MonthlyShipmentTrendDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:monthlyshipmenttrend:info")
    public Result<MonthlyShipmentTrendDTO> get(@PathVariable("id") Long id){
        MonthlyShipmentTrendDTO data = monthlyShipmentTrendService.get(id);

        return new Result<MonthlyShipmentTrendDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:monthlyshipmenttrend:save")
    public Result<Object> save(@RequestBody MonthlyShipmentTrendDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        monthlyShipmentTrendService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:monthlyshipmenttrend:update")
    public Result<Object> update(@RequestBody MonthlyShipmentTrendDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        monthlyShipmentTrendService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:monthlyshipmenttrend:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        monthlyShipmentTrendService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:monthlyshipmenttrend:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<MonthlyShipmentTrendDTO> list = monthlyShipmentTrendService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, MonthlyShipmentTrendExcel.class);
    }

}