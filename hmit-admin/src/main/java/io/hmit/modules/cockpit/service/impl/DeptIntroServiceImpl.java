package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.DeptIntroDao;
import io.hmit.modules.cockpit.dto.DeptIntroDTO;
import io.hmit.modules.cockpit.entity.DeptIntroEntity;
import io.hmit.modules.cockpit.service.DeptIntroService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>部门简介 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-25
 */
@Slf4j
@Service
public class DeptIntroServiceImpl extends CrudServiceImpl<DeptIntroDao, DeptIntroEntity, DeptIntroDTO> implements DeptIntroService {

    @Override
    public QueryWrapper<DeptIntroEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<DeptIntroEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}