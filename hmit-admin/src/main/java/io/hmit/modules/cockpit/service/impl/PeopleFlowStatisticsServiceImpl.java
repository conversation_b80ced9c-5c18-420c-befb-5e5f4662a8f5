package io.hmit.modules.cockpit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.hmit.common.service.impl.CrudServiceImpl;
import io.hmit.modules.cockpit.dao.PeopleFlowStatisticsDao;
import io.hmit.modules.cockpit.dto.PeopleFlowStatisticsDTO;
import io.hmit.modules.cockpit.entity.PeopleFlowStatisticsEntity;
import io.hmit.modules.cockpit.service.PeopleFlowStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <h1>今日人流量统计 Service Impl</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-07
 */
@Slf4j
@Service
public class PeopleFlowStatisticsServiceImpl extends CrudServiceImpl<PeopleFlowStatisticsDao, PeopleFlowStatisticsEntity, PeopleFlowStatisticsDTO> implements PeopleFlowStatisticsService {

    @Override
    public QueryWrapper<PeopleFlowStatisticsEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<PeopleFlowStatisticsEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}