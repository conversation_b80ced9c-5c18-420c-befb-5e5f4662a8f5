package io.hmit.modules.cockpit.controller;

import io.hmit.common.annotation.LogOperation;
import io.hmit.common.constant.Constant;
import io.hmit.common.page.PageData;
import io.hmit.common.utils.ExcelUtils;
import io.hmit.common.utils.Result;
import io.hmit.common.validator.AssertUtils;
import io.hmit.common.validator.ValidatorUtils;
import io.hmit.common.validator.group.AddGroup;
import io.hmit.common.validator.group.DefaultGroup;
import io.hmit.common.validator.group.UpdateGroup;
import io.hmit.modules.cockpit.dto.TotalDataDTO;
import io.hmit.modules.cockpit.excel.TotalDataExcel;
import io.hmit.modules.cockpit.service.TotalDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <h1>办件总数据</h1>
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-23
 */
@RestController
@RequestMapping("cockpit/totaldata")
@Api(tags="办件总数据")
public class TotalDataController {

    private final TotalDataService totalDataService;

    public TotalDataController(TotalDataService totalDataService) {
        this.totalDataService = totalDataService;
    }

    @GetMapping
    @ApiOperation("分页")
//    @RequiresPermissions("cockpit:totaldata:page")
    public Result page(){

        TotalDataDTO totalDataDTO = totalDataService.get(1L);
        return new Result().ok(totalDataDTO);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("cockpit:totaldata:info")
    public Result<TotalDataDTO> get(@PathVariable("id") Long id){
        TotalDataDTO data = totalDataService.get(id);

        return new Result<TotalDataDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("cockpit:totaldata:save")
    public Result<Object> save(@RequestBody TotalDataDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        totalDataService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("cockpit:totaldata:update")
    public Result<Object> update(@RequestBody TotalDataDTO dto){
        //校验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        totalDataService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("cockpit:totaldata:delete")
    public Result<Object> delete(@RequestBody Long[] ids){
        //校验数据
        AssertUtils.isArrayEmpty(ids, "id");

        totalDataService.delete(ids);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("cockpit:totaldata:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TotalDataDTO> list = totalDataService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, TotalDataExcel.class);
    }

}