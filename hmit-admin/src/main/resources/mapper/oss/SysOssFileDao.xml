<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.oss.dao.SysOssFileDao">

    <resultMap type="io.hmit.modules.oss.eneity.SysOssFileEntity" id="sysOssFileMap">
        <result property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileActName" column="file_act_name"/>
        <result property="filePath" column="file_path"/>
        <result property="fileUrl" column="file_url"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileSuffix" column="file_suffix"/>
        <result property="ossId" column="oss_id"/>
        <result property="shardIndex" column="shard_index"/>
        <result property="shardSize" column="shard_size"/>
        <result property="shardTotal" column="shard_total"/>
        <result property="fileKey" column="file_key"/>
        <result property="fileInfo" column="file_info"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>