<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.OneThingDoneDao">

    <resultMap type="io.hmit.modules.cockpit.entity.OneThingDoneEntity" id="oneThingDoneMap">
        <result property="id" column="id"/>
        <result property="swinQueueName" column="swin_queue_name"/>
        <result property="processingDays" column="processing_days"/>
        <result property="businessCount" column="business_count"/>
        <result property="ntotal" column="ntotal"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>