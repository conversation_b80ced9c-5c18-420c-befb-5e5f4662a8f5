<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.ChannelAnalysisDao">

    <resultMap type="io.hmit.modules.cockpit.entity.ChannelAnalysisEntity" id="channelAnalysisMap">
        <result property="id" column="id"/>
        <result property="onlineApplicationCount" column="online_application_count"/>
        <result property="mobileApplicationCount" column="mobile_application_count"/>
        <result property="windowApplicationCount" column="window_application_count"/>
        <result property="selfServiceCount" column="self_service_count"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>