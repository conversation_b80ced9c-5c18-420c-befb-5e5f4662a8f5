<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.OffPeakPeriodStatsDao">

    <resultMap type="io.hmit.modules.cockpit.entity.OffPeakPeriodStatsEntity" id="offPeakPeriodStatsMap">
        <result property="id" column="id"/>
        <result property="deptName" column="dept_name"/>
        <result property="nonPeakDate" column="non_peak_date"/>
        <result property="nonPeakTime" column="non_peak_time"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>