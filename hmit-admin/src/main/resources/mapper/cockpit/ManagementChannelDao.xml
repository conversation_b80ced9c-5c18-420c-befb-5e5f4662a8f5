<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.ManagementChannelDao">

    <resultMap type="io.hmit.modules.cockpit.entity.ManagementChannelEntity" id="managementChannelMap">
        <result property="id" column="id"/>
        <result property="appNumber" column="app_number"/>
        <result property="wxMiniProgram" column="wx_mini_program"/>
        <result property="aliMiniProgram" column="ali_mini_program"/>
        <result property="lobbyNumber" column="lobby_number"/>
        <result property="thirdChannelNumber" column="third_channel_number"/>
        <result property="pcNumber" column="pc_number"/>
        <result property="takeChargeNumber" column="take_charge_number"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>