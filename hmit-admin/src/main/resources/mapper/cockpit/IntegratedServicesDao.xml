<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.IntegratedServicesDao">

    <resultMap type="io.hmit.modules.cockpit.entity.IntegratedServicesEntity" id="integratedServicesMap">
        <result property="id" column="id"/>
        <result property="itemName" column="item_name"/>
        <result property="timeSavedDays" column="time_saved_days"/>
        <result property="originalProcessingDays" column="original_processing_days"/>
        <result property="tripsReduced" column="trips_reduced"/>
        <result property="originalTrips" column="original_trips"/>
        <result property="stepsReduced" column="steps_reduced"/>
        <result property="originalSteps" column="original_steps"/>
        <result property="docsReduced" column="docs_reduced"/>
        <result property="originalDocs" column="original_docs"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>