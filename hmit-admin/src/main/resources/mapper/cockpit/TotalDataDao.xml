<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.TotalDataDao">

    <resultMap type="io.hmit.modules.cockpit.entity.TotalDataEntity" id="totalDataMap">
        <result property="id" column="id"/>
        <result property="totalNumberApplications" column="total_number_applications"/>
        <result property="oneNetcomOfficeRate" column="one_netcom_office_rate"/>
        <result property="evaluetionNumber" column="evaluetion_number"/>
        <result property="activeEvaluetionNumber" column="active_evaluetion_number"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>