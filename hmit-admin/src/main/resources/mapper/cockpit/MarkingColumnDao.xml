<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.MarkingColumnDao">

    <resultMap type="io.hmit.modules.cockpit.entity.MarkingColumnEntity" id="markingColumnMap">
        <result property="id" column="id"/>
        <result property="timeFrame" column="time_frame"/>
        <result property="accessNumber" column="access_number"/>
        <result property="dataDate" column="data_date"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>