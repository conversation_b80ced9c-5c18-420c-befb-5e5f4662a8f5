<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.CallNumberHappeningDao">

    <resultMap type="io.hmit.modules.cockpit.entity.CallNumberHappeningEntity" id="callNumberHappeningMap">
        <result property="id" column="id"/>
        <result property="businessName" column="business_name"/>
        <result property="number" column="number"/>
        <result property="awaitTime" column="await_time"/>
        <result property="dataDate" column="data_date"/>
        <result property="waitPeople" column="wait_people"/>
        <result property="updateNumber" column="update_number"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <select id="selectListByMaxUpdateNumber"
            resultType="io.hmit.modules.cockpit.entity.CallNumberHappeningEntity">
        select * from cockpit_call_number_happening where update_number = (select max(update_number) from cockpit_call_number_happening)
        and data_date = #{date}
    </select>
    <select id="getData" resultType="io.hmit.modules.cockpit.dto.NumberDto">
        SELECT sum(number) as thisYearNumber,max(number) as maxDayNumber,avg(number) as avgNumber from cockpit_call_number_happening
    </select>


</mapper>