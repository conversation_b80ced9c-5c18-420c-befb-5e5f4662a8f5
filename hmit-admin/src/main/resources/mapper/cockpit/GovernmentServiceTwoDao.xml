<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.GovernmentServiceTwoDao">

    <resultMap type="io.hmit.modules.cockpit.entity.GovernmentServiceTwoEntity" id="governmentServiceTwoMap">
        <result property="id" column="id"/>
        <result property="totalItemsCount" column="total_items_count"/>
        <result property="immediateProcessingRatio" column="immediate_processing_ratio"/>
        <result property="happenedItemsCount" column="happened_items_count"/>
        <result property="promisedItemsRatio" column="promised_items_ratio"/>
        <result property="onlineProcessingRate" column="online_processing_rate"/>
        <result property="mobileOnlineProcessingRate" column="mobile_online_processing_rate"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
        <result property="updater" column="updater"/>
    </resultMap>


</mapper>