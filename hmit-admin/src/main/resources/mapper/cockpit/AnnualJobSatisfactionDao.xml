<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.AnnualJobSatisfactionDao">

    <resultMap type="io.hmit.modules.cockpit.entity.AnnualJobSatisfactionEntity" id="annualJobSatisfactionMap">
        <result property="id" column="id"/>
        <result property="comprehensiveSatisfaction" column="comprehensive_satisfaction"/>
        <result property="comprehensiveSatisfactionPer" column="comprehensive_satisfaction_per"/>
        <result property="verySatisfied" column="very_satisfied"/>
        <result property="verySatisfiedPer" column="very_satisfied_per"/>
        <result property="satisfaction" column="satisfaction"/>
        <result property="satisfactionPer" column="satisfaction_per"/>
        <result property="basicSatisfaction" column="basic_satisfaction"/>
        <result property="basicSatisfactionPer" column="basic_satisfaction_per"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>