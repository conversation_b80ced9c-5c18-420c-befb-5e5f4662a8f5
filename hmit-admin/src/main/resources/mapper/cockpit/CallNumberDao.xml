<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.CallNumberDao">

    <resultMap type="io.hmit.modules.cockpit.entity.CallNumberEntity" id="callNumberMap">
        <result property="id" column="id"/>
        <result property="takeNumber" column="take_number"/>
        <result property="callLettersNumber" column="call_letters_number"/>
        <result property="awaitNumber" column="await_number"/>
        <result property="waitingTime" column="waiting_time"/>
        <result property="dataDate" column="data_date"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>