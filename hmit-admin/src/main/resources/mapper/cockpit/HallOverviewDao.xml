<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.HallOverviewDao">

    <resultMap type="io.hmit.modules.cockpit.entity.HallOverviewEntity" id="hallOverviewMap">
        <result property="id" column="id"/>
        <result property="deptCount" column="dept_count"/>
        <result property="businessCount" column="business_count"/>
        <result property="windowCount" column="window_count"/>
        <result property="deptRange" column="dept_range"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>