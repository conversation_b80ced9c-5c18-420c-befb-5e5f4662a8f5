<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.BadReviewDao">

    <resultMap type="io.hmit.modules.cockpit.entity.BadReviewEntity" id="badReviewMap">
        <result property="id" column="id"/>
        <result property="activeEvaluationNumber" column="active_evaluation_number"/>
        <result property="favorableRate" column="favorable_rate"/>
        <result property="verySatisfied" column="very_satisfied"/>
        <result property="satisfaction" column="satisfaction"/>
        <result property="basicSatisfaction" column="basic_satisfaction"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>