<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.DeptIntroDao">

    <resultMap type="io.hmit.modules.cockpit.entity.DeptIntroEntity" id="deptIntroMap">
        <result property="id" column="id"/>
        <result property="region" column="region"/>
        <result property="currentPersonFlow" column="current_person_flow"/>
        <result property="freeWindow" column="free_window"/>
        <result property="busyWindow" column="busy_window"/>
        <result property="localArea" column="local_area"/>
        <result property="floorSapce" column="floor_sapce"/>
        <result property="numberWindows" column="number_windows"/>
        <result property="intro" column="intro"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
        <result property="floor" column="floor"/>
    </resultMap>


</mapper>