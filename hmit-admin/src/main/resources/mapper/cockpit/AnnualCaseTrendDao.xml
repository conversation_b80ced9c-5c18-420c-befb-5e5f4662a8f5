<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.AnnualCaseTrendDao">

    <resultMap type="io.hmit.modules.cockpit.entity.AnnualCaseTrendEntity" id="annualCaseTrendMap">
        <result property="id" column="id"/>
        <result property="month" column="month"/>
        <result property="caseCount" column="case_count"/>
    </resultMap>
    <select id="selectRecentNMonthsAsc" resultType="io.hmit.modules.cockpit.entity.AnnualCaseTrendEntity">
        SELECT *
        FROM
            (SELECT id, month, case_count
            FROM
                cockpit_annual_case_trend
            <where>
                <if test="endMonthStr != null">
                    month &lt;= #{endMonthStr}
                </if>
            </where>
            ORDER BY
                month DESC
             LIMIT #{n})
        ORDER BY month ASC
    </select>


</mapper>