<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.cockpit.dao.FeedbackAnalysisDao">

    <resultMap type="io.hmit.modules.cockpit.entity.FeedbackAnalysisEntity" id="feedbackAnalysisMap">
        <result property="id" column="id"/>
        <result property="totalReviews" column="total_reviews"/>
        <result property="satisfactionRate" column="satisfaction_rate"/>
        <result property="verySatisfiedCount" column="very_satisfied_count"/>
        <result property="satisfiedCount" column="satisfied_count"/>
        <result property="basicallySatisfiedCount" column="basically_satisfied_count"/>
        <result property="dissatisfiedCount" column="dissatisfied_count"/>
        <result property="veryDissatisfiedCount" column="very_dissatisfied_count"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>