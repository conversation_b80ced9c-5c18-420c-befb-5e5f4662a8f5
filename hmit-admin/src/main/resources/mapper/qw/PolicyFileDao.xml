<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qw.dao.PolicyFileDao">

    <resultMap type="io.hmit.modules.qw.entity.PolicyFileEntity" id="policyFileMap">
        <result property="id" column="id"/>
        <result property="policyId" column="policy_id"/>
        <result property="fileName" column="file_name"/>
        <result property="url" column="url"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>