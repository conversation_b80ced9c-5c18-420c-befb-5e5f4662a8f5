<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qw.dao.NewsDao">

    <resultMap type="io.hmit.modules.qw.entity.NewsEntity" id="newsMap">
        <result property="id" column="id"/>
        <result property="newsTitle" column="news_title"/>
        <result property="newsSource" column="news_source"/>
        <result property="newsContent" column="news_content"/>
        <result property="newsCreatedate" column="news_createdate"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>