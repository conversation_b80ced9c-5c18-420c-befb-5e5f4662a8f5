<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qw.dao.MessageDao">

    <resultMap type="io.hmit.modules.qw.entity.MessageEntity" id="messageMap">
        <result property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="name" column="name"/>
        <result property="mobile" column="mobile"/>
        <result property="replyContent" column="reply_content"/>
        <result property="replyDatetime" column="reply_datetime"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

    <select id="getList" resultType="io.hmit.modules.qw.entity.MessageEntity">
        SELECT * FROM `qw_message` where 1=1
        <if test="keyword != null and keyword.trim() != ''">
            and (name like concat( "%",#{keyword},"%") or content like concat( "%",#{keyword},"%") or replyContent like concat( "%",#{keyword},"%") or mobile like concat( "%",#{keyword},"%"))
        </if>
    </select>


</mapper>