<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qw.dao.PolicyDao">

    <resultMap type="io.hmit.modules.qw.entity.PolicyEntity" id="policyMap">
        <result property="id" column="id"/>
        <result property="policyTitle" column="policy_title"/>
        <result property="policySource" column="policy_source"/>
        <result property="policyContent" column="policy_content"/>
        <result property="policyCreatedate" column="policy_createdate"/>
        <result property="policyType" column="policy_type"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

    <select id="getList" resultType="io.hmit.modules.qw.entity.PolicyEntity">
        SELECT * FROM `qw_policy` where 1=1
        <if test="keyword != null and keyword.trim() != ''">
            and policyTitle like concat( '%',#{keyword},'%')
        </if>
        <if test="policyTitle != null and policyTitle.trim() != ''">
            and policy_title like concat( '%',#{policyTitle},'%')
        </if>
        <if test="policySource != null and policySource.trim() != ''">
            and policy_source like concat( '%',#{policySource},'%')
        </if>

        <if test="policyType != null and policyType.trim() != ''">
            and policy_type = #{policyType}
        </if>

    </select>


</mapper>