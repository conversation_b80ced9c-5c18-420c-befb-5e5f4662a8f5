<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.sys.dao.SysDictDataDao">

    <select id="getDictDataList" resultType="io.hmit.modules.sys.entity.DictData">
        select dict_type_id, dict_label, dict_value from sys_dict_data order by dict_type_id, sort
    </select>

</mapper>