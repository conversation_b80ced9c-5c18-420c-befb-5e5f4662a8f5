<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.sys.dao.SysUserDao">

	<select id="getList" resultType="io.hmit.modules.sys.entity.SysUserEntity">
		select t1.*, (select t2.name from sys_dept t2 where t2.id=t1.dept_id) deptName from sys_user t1
		where t1.super_admin = 0
		<if test="username != null and username.trim() != ''">
			and t1.username like #{username}
		</if>
		<if test="deptId != null and deptId.trim() != ''">
			and t1.dept_id = #{deptId}
		</if>
		<if test="gender != null and gender.trim() != ''">
			and t1.gender = #{gender}
		</if>
		<if test="deptIdList != null">
			and t1.dept_id in
			<foreach item="id" collection="deptIdList" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="duties != null and duties.trim() != ''">
			and t1.duties = #{duties}
		</if>
		<if test="workNo != null and workNo.trim() != ''">
			and t1.work_no = #{workNo}
		</if>
		<if test="realName != null and realName.trim() != ''">
			and t1.real_name like #{realName}
		</if>
		<if test="windowNo != null and windowNo.trim() != ''">
			and t1.window_no = #{windowNo}
		</if>
		<if test="queueStatus != null ">
			and t1.queue_status = #{queueStatus}
		</if>
		<if test="windowId != null ">
			and t1.window_id = #{windowId}
		</if>
		<if test="ids != null ">
			and t1.id in
			<foreach item="id" collection="ids" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
	</select>
	<select id="getUnbindUser" resultType="io.hmit.modules.sys.entity.SysUserEntity">
		select t1.*, (select t2.name from sys_dept t2 where t2.id=t1.dept_id) deptName from sys_user t1
		where t1.super_admin = 0 and (t1.account_id is null or t1.account_id = '' ) and (t1.mobile is not null  and t1.mobile != '')
		<if test="userId != null ">
			and t1.id = #{userId}
		</if>
	</select>

	<select id="getWindowUserInsert" resultType="io.hmit.modules.sys.entity.SysUserEntity">
		select t1.* from sys_user_copy2 t1
		where  t1.username  not in (select real_name from sys_user)

	</select>

	<select id="listForQueue" resultType="io.hmit.modules.sys.entity.SysUserEntity">
		select t1.work_no,t1.real_name,t1.window_no,t1.id from sys_user t1
		where t1.super_admin = 0
		<if test="username != null and username.trim() != ''">
			and t1.username like #{username}
		</if>
		<if test="deptId != null and deptId.trim() != ''">
			and t1.dept_id = #{deptId}
		</if>
		<if test="gender != null and gender.trim() != ''">
			and t1.gender = #{gender}
		</if>
		<if test="deptIdList != null">
			and t1.dept_id in
			<foreach item="id" collection="deptIdList" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="duties != null and duties.trim() != ''">
			and t1.duties = #{duties}
		</if>
		<if test="workNo != null and workNo.trim() != ''">
			and t1.work_no = #{workNo}
		</if>
		<if test="realName != null and realName.trim() != ''">
			and t1.real_name like #{realName}
		</if>
		<if test="windowNo != null and windowNo.trim() != ''">
			and t1.window_no = #{windowNo}
		</if>
		<if test="queueStatus != null ">
			and t1.queue_status = #{queueStatus}
		</if>
	</select>

	<select id="getById" resultType="io.hmit.modules.sys.entity.SysUserEntity">
		select t1.*, (select t2.name from sys_dept t2 where t2.id=t1.dept_id) deptName from sys_user t1
			where t1.id = #{value}
	</select>

	<select id="getByUsername" resultType="io.hmit.modules.sys.entity.SysUserEntity">
		select * from sys_user where username = #{value}
	</select>

	<update id="updatePassword">
		update sys_user set password = #{newPassword} where id = #{id}
	</update>

	<select id="getCountByDeptId" resultType="int">
		select count(*) from sys_user where dept_id = #{value}
	</select>

</mapper>
