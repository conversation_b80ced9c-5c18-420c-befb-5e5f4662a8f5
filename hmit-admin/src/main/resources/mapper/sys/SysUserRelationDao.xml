<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.sys.dao.SysUserRelationDao">

    <resultMap type="io.hmit.modules.sys.entity.SysUserRelationEntity" id="sysUserRelationMap">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="parentName" column="parent_name"/>
        <result property="parentRoleId" column="parent_role_id"/>
        <result property="parentRoleName" column="parent_role_name"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="userRoleId" column="user_role_id"/>
        <result property="userRoleName" column="user_role_name"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

    <select id="listForUserRole" resultType="map">
        SELECT u.id as userId, u.real_name as userName, GROUP_CONCAT(r.id) as roleId, GROUP_CONCAT(r.name) as roleName
        FROM
            `public`.sys_user u
        LEFT JOIN
            `public`.sys_role_user ur ON u.id = ur.user_id
        LEFT JOIN
            `public`.sys_role r ON ur.role_id = r.id
        <if test="userIds != null">
            WHERE u.id in
            <foreach item="id" collection="userIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY
            u.id, u.`real_name`
    </select>

    <insert id="insertBatch">
        INSERT INTO sys_user_relation
        (pid, parent_name, parent_role_id, parent_role_name,
        user_id, user_name, user_role_id, user_role_name,
        creator, create_date, updater, update_date)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.pid},
            #{item.parentName},
            #{item.parentRoleId},
            #{item.parentRoleName},
            #{item.userId},
            #{item.userName},
            #{item.userRoleId},
            #{item.userRoleName},
            #{item.creator},
            #{item.createDate},
            #{item.updater},
            #{item.updateDate}
            )
        </foreach>
    </insert>


</mapper>