<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.sys.dao.SysLanguageDao">

    <select id="getLanguage" resultType="io.hmit.modules.sys.entity.SysLanguageEntity">
        select * from sys_language
          where table_name=#{tableName} and table_id=#{tableId} and field_name=#{fieldName} and language=#{language}
    </select>

    <select id="updateLanguage">
        update sys_language set field_value=#{fieldValue}
          where table_name=#{tableName} and table_id=#{tableId} and field_name=#{fieldName} and language=#{language}
    </select>

    <delete id="deleteLanguage">
        delete from sys_language where table_name=#{tableName} and table_id=#{tableId}
    </delete>
</mapper>