<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.issue.dao.IssueDao">

    <resultMap type="io.hmit.modules.issue.entity.IssueEntity" id="issueMap">
        <result property="id" column="id"/>
        <result property="issueCode" column="issue_code"/>
        <result property="content" column="content"/>
        <result property="typeCode" column="type_code"/>
        <result property="typeName" column="type_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="windowsId" column="windows_id"/>
        <result property="windowsName" column="windows_name"/>
        <result property="windowsCode" column="windows_code"/>
        <result property="phone" column="phone"/>
        <result property="doneUserId" column="done_user_id"/>
        <result property="todoUserId" column="todo_user_id"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="resolved" column="resolved"/>
        <result property="fileInit" column="file_init"/>
        <result property="fileHandle" column="file_handle"/>
        <result property="fileHandle2" column="file_handle2"/>
        <result property="ccUsers" column="cc_users"/>
        <result property="delStatus" column="del_status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

    <select id="getList" resultType="io.hmit.modules.issue.entity.IssueEntity" >
        select * from yy_issue where del_status = 0
        <if test="issueCode != null and issueCode.trim() != '' ">
            and issue_code = #{issueCode}
        </if>
        <if test="content != null and content.trim() != '' ">
            and content like concat('%',#{content},'%')
        </if>
        <if test="deptId != null and deptId.trim() != '' ">
            and dept_id = #{deptId}
        </if>
        <if test="windowsId != null and windowsId.trim() != '' ">
            and windows_id = #{windowsId}
        </if>
        <if test="createDateStart != null and createDateStart.trim() != '' ">
            and create_date >= #{createDateStart}
        </if>
        <if test="createDateEnd != null and createDateEnd.trim() != '' ">
            and #{createDateEnd} >= create_date
        </if>
        <if test="typeCode != null and typeCode.trim() != '' ">
            and type_code = #{typeCode}
        </if>
        <if test="status != null and status.trim() != '' ">
            and status = #{status}
        </if>
        <if test="creator != null  ">
            and creator = #{creator}
        </if>
        <if test="todoUserId != null  ">
            and todo_user_id = #{todoUserId}
        </if>
        <if test="doneUserId != null  ">
            and done_user_id = #{doneUserId}
        </if>
        <choose>
            <when test="ids != null and !ids.isEmpty()">
                and id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </when>
            <when test="ids != null and ids.isEmpty()">
                and id in (0)
            </when>
        </choose>

    </select>

</mapper>