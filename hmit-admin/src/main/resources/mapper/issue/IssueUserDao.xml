<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.issue.dao.IssueUserDao">

    <resultMap type="io.hmit.modules.issue.entity.IssueUserEntity" id="issueUserMap">
        <result property="id" column="id"/>
        <result property="issueId" column="issue_id"/>
        <result property="userId" column="user_id"/>
        <result property="userRealName" column="user_real_name"/>
        <result property="roleName" column="role_name"/>
        <result property="handleRemark" column="handle_remark"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>