<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.demo.dao.SysSqlLogDao">

    <resultMap type="io.hmit.modules.datasource.entity.SysSqlLogEntity" id="sysSqlLogMap">
        <result property="id" column="id"/>
        <result property="sqlLogName" column="sql_log_name"/>
        <result property="sqlLogContent" column="sql_log_content"/>
        <result property="sqlLogResult" column="sql_log_result"/>
        <result property="sqlLogIp" column="sql_log_ip"/>
        <result property="sqlExecuteTime" column="sql_execute_time"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
    </resultMap>


</mapper>