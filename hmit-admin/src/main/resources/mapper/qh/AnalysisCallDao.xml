<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.AnalysisCallDao">

    <resultMap type="io.hmit.modules.qh.entity.AnalysisCallEntity" id="analysisCallMap">
        <result property="id" column="id"/>
        <result property="callNo" column="call_no"/>
        <result property="businessName" column="business_name"/>
        <result property="businessCode" column="business_code"/>
        <result property="windowName" column="window_name"/>
        <result property="windowCode" column="window_code"/>
        <result property="workerName" column="worker_name"/>
        <result property="workerNo" column="worker_no"/>
        <result property="workerTel" column="worker_tel"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerTel" column="customer_tel"/>
        <result property="takeNumberTime" column="take_number_time"/>
        <result property="callNoTime" column="call_no_time"/>
        <result property="callNoStatus" column="call_no_status"/>
        <result property="evaluativeStatus" column="evaluative_status"/>
        <result property="processingTime" column="processing_time"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="delStatus" column="del_status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <update id="deleteByStatus">
        update yy_analysis_call set del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getNewAnalysisCall" resultType="io.hmit.modules.qh.entity.AnalysisCallEntity">
        SELECT
            t1.call_no call_no,
            t2.business_code business_code,
            t2.business_name business_name,
            t1.window_id window_id,
            t1.user_id,
            t1.NAME customer_name,
            t1.tel customer_tel,
            t1.create_date take_number_time,
            t1.handle_date call_no_time,
            t1.handle_time processing_time,
            t3.stars evaluative_status,
            t1.status call_no_status,
            t4.mobile worker_tel,
            t4.real_name worker_name,
            t4.work_no worker_no,
            t5.window_name window_name
        FROM
                ( SELECT * FROM yy_call_no_info WHERE 1 = 1
                <if test="startTime != null and startTime.trim() != '' ">
                    and DATE(handle_date) >= #{startTime}
                </if>
                <if test="endTime != null and endTime.trim() != '' ">
                    and #{endTime} >= DATE(handle_date)
                </if>
                <if test="callNo != null and callNo.trim() != '' ">
                    and  (call_no like concat( '%',#{callNo},'%'))
                </if>
                <if test="businessId != null and businessId.trim() != '' ">
                    and business_id= #{businessId}
                </if>
                <if test="userId != null and userId.trim() != '' ">
                    and user_id= #{userId}
                </if>
                <if test="workerNo != null and workerNo.trim() != '' ">
                    and user_id in (select `id` from sys_user where work_no like concat( '%',#{workerNo},'%'))
                </if>
                <if test="workerName != null and workerName.trim() != '' ">
                    and user_id in (select `id` from sys_user where real_name like concat( '%',#{workerName},'%'))
                </if>
                <if test="windowId != null and windowId.trim() != '' ">
                    and window_id= #{windowId}
                </if>
                <if test="status != null and status.trim() != '' ">
                    and status= #{status}
                </if>
                <if test="tel != null and tel.trim() != '' ">
                    and (tel like concat( '%',#{tel},'%'))
                </if>
                    ) t1
                    LEFT JOIN sys_user t4 ON t1.user_id = t4.id
                    LEFT JOIN yy_windows t5 ON t1.window_id = t5.id
                    LEFT JOIN yy_business t2 ON t1.business_id = t2.id
                    LEFT JOIN yy_call_evaluative_info t3 ON t1.id = t3.call_no_id
                WHERE 1=1
                <if test="evaluativeStatus != null and evaluativeStatus.trim() != '' ">
                    and t3.stars = #{evaluativeStatus}
                </if>

    </select>

</mapper>