<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.BusinessDao">

    <resultMap type="io.hmit.modules.qh.entity.BusinessEntity" id="businessMap">
        <result property="id" column="id"/>
        <result property="businessName" column="business_name"/>
        <result property="businessCode" column="business_code"/>
        <result property="content" column="content"/>
        <result property="callPrefix" column="call_prefix"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="callMin" column="call_min"/>
        <result property="callMax" column="call_max"/>
        <result property="warningTime" column="warning_time"/>
        <result property="warningCount" column="warning_count"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="delStatus" column="del_status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <update id="deleteByStatus">
        update yy_business set del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="getList" resultType="io.hmit.modules.qh.entity.BusinessEntity">
        select * from yy_business where del_status = 0
        <if test="businessName!= null and businessName.trim() != ''">
            and (business_name like concat( '%',#{businessName},'%') )
        </if>
        <if test="deptId != null and deptId.trim() !='' ">
            and dept_id = #{deptId}
        </if>
        <if test="businessCode != null and businessCode.trim() != '' ">
            and business_code = #{businessCode}
        </if>
        <if test="windowId != null ">
            and id in (SELECT business_id FROM `yy_windows_business` where del_status = 0 and window_id = #{windowId})
        </if>

    </select>

    <select id="getListForSelect" resultType="io.hmit.modules.qh.entity.BusinessEntity">
        select * from yy_business where del_status = 0
        <if test="businessName!= null and businessName.trim() != ''">
            and (business_name like concat( '%',#{businessName},'%') )
        </if>
        <if test="deptId != null ">
            and dept_id = #{deptId}
        </if>

    </select>
    <select id="getBusinessByCode" resultType="io.hmit.modules.qh.entity.BusinessEntity">
        select * from yy_business where del_status = 0 and business_code = #{businessCode} limit 1
    </select>

</mapper>