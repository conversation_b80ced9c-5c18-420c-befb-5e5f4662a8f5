<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.AnalysisBusinessDao">

    <resultMap type="io.hmit.modules.qh.entity.AnalysisBusinessEntity" id="analysisBusinessMap">
        <result property="id" column="id"/>
        <result property="businessName" column="business_name"/>
        <result property="businessCode" column="business_code"/>
        <result property="deptName" column="dept_name"/>
        <result property="deptCode" column="dept_code"/>
        <result property="allCallNum" column="all_call_num"/>
        <result property="maxWaitingTime" column="max_waiting_time"/>
        <result property="averageWaitingTime" column="average_waiting_time"/>
        <result property="maxProcessingTime" column="max_processing_time"/>
        <result property="averageProcessingTime" column="average_processing_time"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="delStatus" column="del_status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <update id="deleteByStatus">
        update yy_analysis_business set del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getNewAnalysisBusiness" resultType="io.hmit.modules.qh.entity.AnalysisBusinessEntity">
        SELECT
            yb.id,
            yb.business_name,
            yb.business_code,
            yb.dept_name,
            IFNULL( bg.all_call_num, 0 ) all_call_num,
            IFNULL( bg.max_waiting_time, 0 ) max_waiting_time,
            IFNULL( bg.average_waiting_time, 0 ) average_waiting_time,
            IFNULL( bg.max_processing_time, 0 ) max_processing_time,
            IFNULL( bg.average_processing_time, 0 ) average_processing_time
        FROM
            yy_business yb
                LEFT JOIN (
                SELECT
                    business_id,
                    count(IF( handle_date IS NOT NULL, TRUE, NULL )) all_call_num,
                    max( waiting_time ) max_waiting_time,
                    avg( waiting_time ) average_waiting_time,
                    max( handle_time ) max_processing_time,
                    avg( handle_time ) average_processing_time
                FROM
                    `yy_call_no_info` where 1=1
                <if test="startTime != null and startTime.trim() != '' ">
                    and DATE(handle_date) >= #{startTime}
                </if>
                <if test="endTime != null and endTime.trim() != '' ">
                    and #{endTime} >= DATE(handle_date)
                </if>
                GROUP BY
                    business_id
            ) bg ON yb.id = bg.business_id where 1=1
        <if test="businessName != null and businessName.trim() != '' ">
            and  (yb.business_name like concat( '%',#{businessName},'%') )
        </if>
        <if test="businessCode != null and businessCode.trim() != '' ">
            and  (yb.business_code like concat( '%',#{businessCode},'%') )
        </if>
        <if test="deptId != null and deptId.trim() != '' ">
            and yb.dept_id =  #{deptId}
        </if>
    </select>

    <select id="getDayBusiness" resultType="io.hmit.modules.qh.dto.RtaBusinessDTO">
        SELECT count(*) take_num,count(`status`=1 or null) waiting_num FROM `yy_call_no_info` where 1=1
        <if test="date != null and date.trim() != '' ">
            and DATE(create_date) = #{date}
        </if>

    </select>

    <select id="getDayBusinessDetail" resultType="io.hmit.modules.qh.dto.RtaBusinessDetailDTO">
        SELECT
            yb.business_name,
            IFNULL( bg.waiting_num, 0 ) waiting_num,
            IFNULL( bg.average_waiting_time, 0 ) average_waiting_time
        FROM
            yy_business yb
                LEFT JOIN (
                SELECT
                    business_id,
                    count(IF(`status`=1, TRUE, NULL )) waiting_num,
                    avg( waiting_time ) average_waiting_time
                FROM
                    `yy_call_no_info` where 1=1
                    <if test="date != null and date.trim() != '' ">
                        and DATE(create_date) = #{date}
                    </if>
                GROUP BY
                    business_id
            ) bg ON yb.id = bg.business_id
    </select>

    <select id="getDaySatisfaction" resultType="io.hmit.modules.qh.dto.RtaSatisfactionDTO">
        SELECT
            count(IF( t4.stars = '1', TRUE, NULL )) satisfaction_one,
            count(IF( t4.stars = '2', TRUE, NULL )) satisfaction_two,
            count(IF( t4.stars = '3', TRUE, NULL )) satisfaction_three,
            count(IF( t4.stars = '4', TRUE, NULL )) satisfaction_four,
            count(IF( t4.stars = '5', TRUE, NULL )) satisfaction_five
        FROM
            (
                SELECT
                    t1.*,
                    t3.stars
                FROM
                    yy_call_no_info t1
                        LEFT JOIN yy_call_evaluative_info t3 ON t1.id = t3.call_no_id
            ) t4 where 1=1
        <if test="date != null and date.trim() != '' ">
            and DATE(t4.create_date) = #{date}
        </if>
    </select>

    <select id="getDayPeriodTime" resultType="io.hmit.modules.qh.dto.RtaPeriodTimeCallDTO">
        SELECT
            t1.period_Time,
            IFNULL( t2.take_num, 0 ) take_num
        FROM
            (
                SELECT 8 AS period_Time UNION ALL
                SELECT 9 AS period_Time UNION ALL
                SELECT 10 AS period_Time UNION ALL
                SELECT 11 AS period_Time UNION ALL
                SELECT 12 AS period_Time UNION ALL
                SELECT 13 AS period_Time UNION ALL
                SELECT 14 AS period_Time UNION ALL
                SELECT 15 AS period_Time UNION ALL
                SELECT 16 AS period_Time
            ) t1
                LEFT JOIN
                ( SELECT count(*) take_num, HOUR ( create_date ) period_Time FROM yy_call_no_info GROUP BY period_Time ) t2
                    ON t1.period_Time = t2.period_Time
    </select>




</mapper>