<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.DepartmentDao">

    <resultMap type="io.hmit.modules.qh.entity.DepartmentEntity" id="departmentMap">
        <result property="id" column="id"/>
        <result property="departmentName" column="department_name"/>
        <result property="departmentCode" column="department_code"/>
        <result property="content" column="content"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="delStatus" column="del_status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <update id="deleteByStatus">
        update entity set del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>