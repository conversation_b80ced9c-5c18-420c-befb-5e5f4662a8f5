<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.ListrecDao">

    <resultMap type="io.hmit.modules.qh.entity.ListrecEntity" id="listrecMap">
        <result property="id" column="id"/>
        <result property="depName" column="dep_name"/>
        <result property="userName" column="user_name"/>
        <result property="winCodename" column="win_codename"/>
        <result property="depId" column="dep_id"/>
        <result property="queueName" column="queue_name"/>
        <result property="queueId" column="queue_id"/>
        <result property="callNumber" column="call_number"/>
        <result property="sRegTime" column="s_reg_time"/>
        <result property="sCallTime" column="s_call_time"/>
        <result property="sEndTime" column="s_end_time"/>
        <result property="sAppTime" column="s_app_time"/>
        <result property="appValue" column="app_value"/>
        <result property="sTime" column="s_time"/>
        <result property="callerId" column="caller_id"/>
        <result property="userId" column="user_id"/>
        <result property="clientTel" column="client_tel"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <resultMap type="io.hmit.modules.qh.dto.ListrecDataDTO" id="listrecMap2">
        <result property="dep_name" column="dep_name"/>
        <result property="user_name" column="user_name"/>
        <result property="win_codename" column="win_codename"/>
        <result property="dep_id" column="dep_id"/>
        <result property="queue_name" column="queue_name"/>
        <result property="queue_id" column="queue_id"/>
        <result property="call_number" column="call_number"/>
        <result property="s_reg_time" column="s_reg_time"/>
        <result property="s_call_time" column="s_call_time"/>
        <result property="s_end_time" column="s_end_time"/>
        <result property="s_app_time" column="s_app_time"/>
        <result property="app_value" column="app_value"/>
        <result property="s_time" column="s_time"/>
        <result property="caller_id" column="caller_id"/>
        <result property="user_id" column="user_id"/>
        <result property="client_tel" column="client_tel"/>
        <result property="del_flag" column="del_flag"/>
    </resultMap>
    <update id="deleteByStatus">
        update yy_listrec set del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <delete id="deleteByDay">
        delete from yy_listrec where CAST (s_reg_time as DATE) = #{sDate}
    </delete>
    <select id="getByDatabase" resultMap="listrecMap2">
        select * from yy_listrec where 1=1
        <if test="startDate != null and startDate.trim() != '' ">
            and CAST (s_reg_time as DATE) > = #{startDate}
        </if>
        <if test="endDate != null and endDate.trim() != '' ">
            and  #{endDate} >= CAST (s_reg_time as DATE)
        </if>
        order by s_reg_time asc
    </select>


</mapper>