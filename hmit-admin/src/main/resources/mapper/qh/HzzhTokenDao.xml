<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.HzzhTokenDao">

    <resultMap type="io.hmit.modules.qh.entity.HzzhTokenEntity" id="hzzhTokenMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="token" column="token"/>
        <result property="expireDate" column="expire_date"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <update id="deleteByStatus">
        update yy_hzzh_token set del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getByUserId" resultType="io.hmit.modules.qh.entity.HzzhTokenEntity">
        select * from yy_hzzh_token where user_id = #{value}
    </select>

</mapper>