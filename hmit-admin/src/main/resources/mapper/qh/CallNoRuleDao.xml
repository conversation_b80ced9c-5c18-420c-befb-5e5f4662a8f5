<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.CallNoRuleDao">

    <resultMap type="io.hmit.modules.qh.entity.CallNoRuleEntity" id="callNoRuleMap">
        <result property="id" column="id"/>
        <result property="windowId" column="window_id"/>
        <result property="windowName" column="window_name"/>
        <result property="ordinaryMax" column="ordinary_max"/>
        <result property="priorityMax" column="priority_max"/>
        <result property="reservationMax" column="reservation_max"/>
        <result property="ordinaryRemainingMin" column="ordinary_remaining_min"/>
        <result property="reservationRemainingMin" column="reservation_remaining_min"/>
        <result property="priorityRemainingMin" column="priority_remaining_min"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="delStatus" column="del_status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <update id="deleteByStatus">
        update yy_call_no_rule set del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>