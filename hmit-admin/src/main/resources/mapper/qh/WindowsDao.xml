<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.WindowsDao">

    <resultMap type="io.hmit.modules.qh.entity.WindowsEntity" id="windowsMap">
        <result property="id" column="id"/>
        <result property="windowName" column="window_name"/>
        <result property="windowCode" column="window_code"/>
        <result property="windowLocation" column="window_location"/>
        <result property="content" column="content"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="delStatus" column="del_status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <update id="deleteByStatus">
        update entity yy_windows del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getListForSelect" resultType="io.hmit.modules.qh.entity.WindowsEntity" >
        select * from yy_windows where del_status = 0
        <if test="deptId != null and deptId.trim() != '' ">
            and dept_id = #{deptId}
        </if>
    </select>

    <select id="getList" resultType="io.hmit.modules.qh.entity.WindowsEntity" >
        select * from yy_windows where del_status = 0
        <if test="deptId != null and deptId.trim() != '' ">
            and dept_id = #{deptId}
        </if>
        <if test="deptIdList != null">
            and dept_id in
            <foreach item="id" collection="deptIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="windowCode != null and windowCode.trim() != '' ">
            and window_code like concat('%',#{windowCode},'%')
        </if>

        <if test="businessName != null and businessName.trim() != '' ">
            and id in (SELECT window_id FROM `yy_windows_business` where del_status = 0 and business_name like concat( '%',#{businessName},'%'))
        </if>

    </select>

    <select id="getWindowsByDept" resultType="io.hmit.modules.qh.dto.DeptWindowDTO">
        SELECT
            a.NAME deptName,
            t.windows windows
        FROM
            ( SELECT dept_id, GROUP_CONCAT( window_name ) windows FROM `yy_windows` where del_status = 0  GROUP BY dept_id ) t
                LEFT JOIN sys_dept a ON t.dept_id = a.id
    </select>


</mapper>