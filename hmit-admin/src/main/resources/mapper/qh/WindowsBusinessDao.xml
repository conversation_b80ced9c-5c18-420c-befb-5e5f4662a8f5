<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.WindowsBusinessDao">

    <resultMap type="io.hmit.modules.qh.entity.WindowsBusinessEntity" id="windowsBusinessMap">
        <result property="id" column="id"/>
        <result property="windowId" column="window_id"/>
        <result property="businessId" column="business_id"/>
        <result property="businessName" column="business_name"/>
        <result property="warningTime" column="warning_time"/>
        <result property="firstRank" column="first_rank"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="delStatus" column="del_status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <update id="deleteByStatus">
        update yy_windows_business set del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="getByWindowId" resultType="io.hmit.modules.qh.entity.WindowsBusinessEntity">
        select * from yy_windows_business where del_status = 0 and window_id = #{windowId}
    </select>
    <select id="getBusinessNames" resultType="string">
        select GROUP_CONCAT(business_Name) from yy_windows_business where del_status = 0 and window_id = #{windowId} group by window_id
    </select>
    <update id="updateBusinessNameById">
        update yy_windows_business set business_name = #{businessName} where business_id = #{businessId}
    </update>

</mapper>