<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.CallNoConfigDao">

    <resultMap type="io.hmit.modules.qh.entity.CallNoConfigEntity" id="callNoConfigMap">
        <result property="id" column="id"/>
        <result property="showContent" column="show_content"/>
        <result property="keyFont" column="key_font"/>
        <result property="keyColor" column="key_color"/>
        <result property="fontColor" column="font_color"/>
        <result property="backgroundPic" column="background_pic"/>
        <result property="keyWidth" column="key_width"/>
        <result property="keyHeight" column="key_height"/>
        <result property="showWaitStatus" column="show_wait_status"/>
        <result property="showWaitColor" column="show_wait_color"/>
        <result property="businessId" column="business_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="globalConfigStatus" column="global_config_status"/>
        <result property="morningStartTime" column="morning_start_time"/>
        <result property="morningEndTime" column="morning_end_time"/>
        <result property="morningMax" column="morning_max"/>
        <result property="afternoonStartTime" column="afternoon_start_time"/>
        <result property="afternoonEndTime" column="afternoon_end_time"/>
        <result property="afternoonMax" column="afternoon_max"/>
        <result property="weekdays" column="weekdays"/>
        <result property="extraPrintInfo" column="extra_print_info"/>
        <result property="idcardNum" column="idcard_num"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="delStatus" column="del_status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <update id="deleteByStatus">
        update yy_call_no_config set del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>