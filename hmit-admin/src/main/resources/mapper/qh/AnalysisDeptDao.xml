<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.qh.dao.AnalysisDeptDao">

    <resultMap type="io.hmit.modules.qh.entity.AnalysisDeptEntity" id="analysisDeptMap">
        <result property="id" column="id"/>
        <result property="deptName" column="dept_name"/>
        <result property="deptCode" column="dept_code"/>
        <result property="allCallNum" column="all_call_num"/>
        <result property="averageWaitingTime" column="average_waiting_time"/>
        <result property="satisfactionOne" column="satisfaction_one"/>
        <result property="satisfactionTwo" column="satisfaction_two"/>
        <result property="satisfactionThree" column="satisfaction_three"/>
        <result property="satisfactionFour" column="satisfaction_four"/>
        <result property="satisfactionFive" column="satisfaction_five"/>
        <result property="analysisDate" column="analysis_date"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="delStatus" column="del_status"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>
    <update id="deleteByStatus">
        update yy_analysis_dept set del_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="getNewAnalysisDept" resultType="io.hmit.modules.qh.entity.AnalysisDeptEntity">
        SELECT
            sys_dept.NAME dept_name,
            IFNULL( t5.all_call_num, 0 ) all_call_num,
            IFNULL( t5.average_waiting_time, 0 ) average_waiting_time,
            IFNULL( t5.satisfaction_one, 0 ) satisfaction_one,
            IFNULL( t5.satisfaction_two, 0 ) satisfaction_two,
            IFNULL( t5.satisfaction_three, 0 ) satisfaction_three,
            IFNULL( t5.satisfaction_four, 0 ) satisfaction_four,
            IFNULL( t5.satisfaction_five, 0 ) satisfaction_five
        FROM
            sys_dept
                LEFT JOIN (
                SELECT
                    t4.dept_id,
                    count(IF( t4.handle_date IS NOT NULL, TRUE, NULL )) all_call_num,
                    avg( t4.waiting_time ) average_waiting_time,
                    count(IF( t4.stars = '1', TRUE, NULL )) satisfaction_one,
                    count(IF( t4.stars = '2', TRUE, NULL )) satisfaction_two,
                    count(IF( t4.stars = '3', TRUE, NULL )) satisfaction_three,
                    count(IF( t4.stars = '4', TRUE, NULL )) satisfaction_four,
                    count(IF( t4.stars = '5', TRUE, NULL )) satisfaction_five
                FROM
                    (
                        SELECT
                            t1.*,
                            t2.dept_id,
                            t3.stars
                        FROM
                            (SELECT * from  yy_call_no_info where 1=1
                            <if test="startTime != null and startTime.trim() != '' ">
                                and DATE(handle_date) >= #{startTime}
                            </if>
                            <if test="endTime != null and endTime.trim() != '' ">
                                and #{date} >= DATE(handle_date)
                            </if>
                            ) t1
                                LEFT JOIN yy_business t2 ON t1.business_id = t2.id
                                LEFT JOIN yy_call_evaluative_info t3 ON t1.id = t3.call_no_id
                    ) t4
                GROUP BY
                    t4.dept_id
            ) t5 ON sys_dept.id = t5.dept_id where 1=1
        <if test="deptId != null and deptId.trim() != '' ">
            and sys_dept.`id` =  #{deptId}
        </if>
    </select>

    <select id="getDayDept" resultType="io.hmit.modules.qh.dto.RtaDeptDetailDTO">
        SELECT
            sys_dept.name dept_name,
            IFNULL( t5.take_num, 0 ) take_num,
            IFNULL( t5.handle_num, 0 ) handle_num,
            IFNULL( t5.waiting_num, 0 ) waiting_num
        FROM
            sys_dept
                LEFT JOIN (
                SELECT
                    t4.dept_id,
                    count(create_date) take_num,
                    count(IF( t4.`status` =2, TRUE, NULL )) handle_num,
                    count(IF( t4.`status` =1, TRUE, NULL )) waiting_num
                FROM
                    (
                        SELECT
                            t1.*,
                            t2.dept_id
                        FROM
                            yy_call_no_info t1
                                LEFT JOIN yy_business t2 ON t1.business_id = t2.id
                    ) t4
                GROUP BY
                    t4.dept_id
            ) t5 ON sys_dept.id = t5.dept_id
                order by take_num desc
    </select>

</mapper>