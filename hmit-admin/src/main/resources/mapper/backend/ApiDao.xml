<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.backend.dao.ApiDao">

    <resultMap type="io.hmit.modules.backend.entity.ApiEntity" id="apiMap">
        <result property="id" column="id"/>
        <result property="apiName" column="api_name"/>
        <result property="apiCname" column="api_cname"/>
        <result property="apiType" column="api_type"/>
        <result property="belongingSystem" column="belonging_system"/>
        <result property="personInCharge" column="person_in_charge"/>
        <result property="apiVersion" column="api_version"/>
        <result property="status" column="status"/>
        <result property="businessType" column="business_type"/>
        <result property="apiDesc" column="api_desc"/>
        <result property="messageProtocol" column="message_protocol"/>
        <result property="maxQps" column="max_qps"/>
        <result property="maxConcurrency" column="max_concurrency"/>
        <result property="authenticationMethod" column="authentication_method"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>


</mapper>