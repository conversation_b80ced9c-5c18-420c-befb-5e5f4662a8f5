<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.backend.dao.EarlyWarningDao">

    <resultMap type="io.hmit.modules.backend.entity.EarlyWarningEntity" id="earlyWarningMap">
        <result property="id" column="id"/>
        <result property="deptName" column="dept_name"/>
        <result property="windowNo" column="window_no"/>
        <result property="name" column="name"/>
        <result property="workNo" column="work_no"/>
        <result property="photoFile" column="photo_file"/>
        <result property="warningInfo" column="warning_info"/>
        <result property="warningGrade" column="warning_grade"/>
        <result property="warningType" column="warning_type"/>
        <result property="seTime" column="se_time"/>
        <result property="businessName" column="business_name"/>
        <result property="currentQueueSize" column="current_queue_size"/>
        <result property="standardNumber" column="standard_number"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
        <result property="duration" column="duration"/>
        <result property="callId" column="call_id"/>
        <result property="callNumbe" column="call_numbe"/>
        <result property="appTime" column="app_time"/>

    </resultMap>


</mapper>
