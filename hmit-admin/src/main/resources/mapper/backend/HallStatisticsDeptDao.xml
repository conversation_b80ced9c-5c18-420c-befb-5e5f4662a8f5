<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.backend.dao.HallStatisticsDeptDao">

    <resultMap type="io.hmit.modules.backend.entity.HallStatisticsDeptEntity" id="hallStatisticsDeptMap">
        <result property="id" column="id"/>
        <result property="windowNum" column="window_num"/>
        <result property="workerNum" column="worker_num"/>
        <result property="warningNum" column="warning_num"/>
        <result property="todayPickUp" column="today_pick_up"/>
        <result property="todayProcessed" column="today_processed"/>
        <result property="averageWaitingTime" column="average_waiting_time"/>
        <result property="todayTotalProcessingTime" column="today_total_processing_time"/>
        <result property="minWaitingTime" column="min_waiting_time"/>
        <result property="maxWaitingTime" column="max_waiting_time"/>
        <result property="todayCompletionRate" column="today_completion_rate"/>
        <result property="satisfactionRate" column="satisfaction_rate"/>
        <result property="applauseRate" column="applause_rate"/>
        <result property="cNum" column="c_num"/>
        <result property="pNum" column="p_num"/>
        <result property="lNum" column="l_num"/>
        <result property="hNum" column="h_num"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
        <result property="departmentId" column="department_id"/>
        <result property="departmentName" column="department_name"/>
    </resultMap>


</mapper>