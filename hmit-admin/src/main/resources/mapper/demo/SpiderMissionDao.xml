<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.demo.dao.SpiderMissionDao">

    <resultMap type="io.hmit.modules.demo.entity.SpiderMissionEntity" id="spiderMissionMap">
        <result property="id" column="id"/>
        <result property="missionName" column="mission_name"/>
        <result property="spiderConfigId" column="spider_config_id"/>
        <result property="entryUrls" column="entry_urls"/>
        <result property="status" column="status"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="timeCost" column="time_cost"/>
        <result property="exitWay" column="exit_way"/>
        <result property="exitWayCount" column="exit_way_count"/>
        <result property="successNum" column="success_num"/>
        <result property="cookieStr" column="cookie_str"/>
        <result property="headerStr" column="header_str"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="loopFlag" column="loop_flag"/>
        <result property="loopParam" column="loop_param"/>
        <result property="loopNum" column="loop_num"/>
    </resultMap>


</mapper>
