<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.demo.dao.ArticleDao">

    <resultMap type="io.hmit.modules.demo.entity.ArticleEntity" id="articleMap">
        <result property="id" column="id"/>
        <result property="articleHref" column="article_href"/>
        <result property="articleTitle" column="article_title"/>
        <result property="articleCreatedate" column="article_createdate"/>
        <result property="articleSource" column="article_source"/>
        <result property="content" column="content"/>
        <result property="articlehrefsId" column="articlehrefs_id"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
        <result property="showStatus" column="show_status"/>
    </resultMap>

    <select id="selectByHref" parameterType="string" resultType="io.hmit.modules.demo.entity.ArticleEntity">
        select id,article_href,article_title from tb_article where article_href = #{href}
    </select>

    <update id="offShelf" parameterType="java.util.List">
        UPDATE tb_article
        SET show_status = 0
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="upShelf" parameterType="java.util.List">
        UPDATE tb_article
        SET show_status = 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

</mapper>
