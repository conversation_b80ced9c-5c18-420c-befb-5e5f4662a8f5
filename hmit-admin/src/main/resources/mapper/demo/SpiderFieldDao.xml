<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.demo.dao.SpiderFieldDao">

    <resultMap type="io.hmit.modules.demo.entity.SpiderFieldEntity" id="spiderFieldMap">
        <result property="id" column="id"/>
        <result property="configId" column="config_id"/>
        <result property="field" column="field"/>
        <result property="fieldName" column="field_name"/>
        <result property="extractType" column="extract_type"/>
        <result property="extractBy" column="extract_by"/>
        <result property="extractBy2" column="extract_by2"/>
        <result property="constantValue" column="constant_value"/>
        <result property="extractIndex" column="extract_index"/>
        <result property="processRuleId" column="process_rule_id"/>
        <result property="extractAttrFlag" column="extract_attr_flag"/>
        <result property="extractAttr" column="extract_attr"/>
        <result property="spiderFieldHighSetting" column="spider_field_high_setting"/>
        <result property="spiderFieldHighSettingParams" column="spider_field_high_setting_params"/>
    </resultMap>


</mapper>
