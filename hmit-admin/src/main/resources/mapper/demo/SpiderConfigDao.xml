<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.demo.dao.SpiderConfigDao">

    <resultMap type="io.hmit.modules.demo.entity.SpiderConfigEntity" id="spiderConfigMap">
        <result property="id" column="id"/>
        <result property="spiderCode" column="spider_code"/>
        <result property="spiderName" column="spider_name"/>
        <result property="entryUrls" column="entry_urls"/>
        <result property="targetRegex" column="target_regex"/>
        <result property="cascadess" column="cascadess"/>
        <result property="tableName" column="table_name"/>
        <result property="domain" column="domain"/>
        <result property="charset" column="charset"/>
        <result property="sleepTime" column="sleep_time"/>
        <result property="retryTimes" column="retry_times"/>
        <result property="threadCount" column="thread_count"/>
        <result property="useProxy" column="use_proxy"/>
        <result property="showLog" column="show_log"/>
        <result property="saveDb" column="save_db"/>
        <result property="isJson" column="is_json"/>
        <result property="spiderHighSetting" column="spider_high_setting"/>
        <result property="userDefinePipeline" column="user_define_pipeline"/>
        <result property="isSelenium" column="is_selenium"/>
        <result property="missionFinish" column="mission_finish"/>
        <result property="listExtractBy" column="list_extract_by"/>
    </resultMap>


</mapper>
