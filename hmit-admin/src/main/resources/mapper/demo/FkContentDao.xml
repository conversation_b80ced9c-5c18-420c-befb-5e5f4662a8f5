<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.hmit.modules.demo.dao.FkContentDao">


    <select id="getDetailList" resultType="io.hmit.modules.demo.vo.FkContentVo">
        SELECT a.`id`, a.`node_id`, b.`title`, a.`content`, a.`creator`, a.`create_date`, a.`updater`, a.`update_date`
        FROM `tb_fk_content` a JOIN `tb_fk_nodes` b on a.`node_id` = b.`id`
    </select>
</mapper>