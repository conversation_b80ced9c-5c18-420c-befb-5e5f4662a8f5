server:
  port: 8084

hmit:
  file:
    path: /home/<USER>/financial-workshop/

ding:
  tenantId: 1
  appKey: appKey
  appSecret: appSecret
  platform: openplatform-pro.ding.zj.gov.cn

accessFile:
  resourceHandler: /static/**
  location: /home/<USER>/financial-workshop/


spring:
  datasource:
    druid:
      #MySQL
      driver-class-name: com.mysql.cj.jdbc.Driver
#      driver-class-name: com.mysql.jdbc.Driver
      url: ****************************************************************************************************************************************************
      username: hmit_user
      password: HmitUser@2022
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
