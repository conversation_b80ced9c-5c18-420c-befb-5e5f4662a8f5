server:
  port: 18000

hmit:
  upload:
    path: D:/tmp/springboot2/

ding:
  tenantId: 50117795
  appKey: GCHAMP-rhwd1pkeNeXGkXL3u7z2amm
  appSecret: 0ym46OXa2v9Q042U9grUj23gO54qTig3v9Uy5IX0
  platform: openplatform.dg-work.cn

spring:
  datasource:
    druid:
      #MySQL
      driver-class-name: com.mysql.cj.jdbc.Driver
#      driver-class-name: com.mysql.jdbc.Driver
      url: ***************************************************************************************************************************************************
      username: root
      password: 123456
#      #Oracle
#      driver-class-name: oracle.jdbc.OracleDriver
#      url: ***********************************
#      username: hmit_security
#      password: 123456
#      #SQLServer
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      url: ****************************************************************
#      username: sa
#      password: 123456
#      #postgresql
#      driver-class-name: org.postgresql.Driver
#      url: ****************************************************
#      username: postgres
#      password: 123456
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 6000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

##多数据源的配置，需要引用hmit-dynamic-datasource
#dynamic:
#  datasource:
#    slave1:
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      url: *************************************************************
#      username: sa
#      password: 123456
#    slave2:
#      driver-class-name: org.postgresql.Driver
#      url: *************************************************
#      username: postgres
#      password: 123456
