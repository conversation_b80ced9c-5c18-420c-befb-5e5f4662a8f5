<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>欢迎页</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="css/bootstrap.min.css">
</head>
<body>
<div class="panel panel-default">
    <div class="panel-heading">基本信息</div>
    <div style="padding: 10px 0 20px 10px;">
        <h3>&nbsp;&nbsp;&nbsp;模块配置文件</h3>
        <ul>
            <li>配置文件：generator.properties</li>
        </ul>

        <h3>&nbsp;&nbsp;&nbsp;数据库相关</h3>
        <ul>
            <li>配置文件：application.yml</li>
        </ul>

        <h3>&nbsp;&nbsp;&nbsp;生成目录结构</h3>
        <pre class="code highlight js-syntax-highlight plaintext white" lang="plaintext" v-pre="true">
        <code><span id="LC1" class="line" lang="plaintext">hmit-generator</span>
        <span id="LC2" class="line" lang="plaintext">├─ 目录SQL语句</span>
        <span id="LC3" class="line" lang="plaintext">│</span>
        <span id="LC4" class="line" lang="plaintext">├─admin 模块</span>
        <span id="LC5" class="line" lang="plaintext">│  ├─resources/mapper/模块 xml文件</span>
        <span id="LC6" class="line" lang="plaintext">│  └─main/java/io/hmit/modules/模块</span>
        <span id="LC7" class="line" lang="plaintext">│                             ├─controller</span>
        <span id="LC8" class="line" lang="plaintext">│                             ├─dao</span>
        <span id="LC9" class="line" lang="plaintext">│                             └─service</span>
        <span id="LC10" class="line" lang="plaintext">├─api 模块</span>
        <span id="LC11" class="line" lang="plaintext">│  ├─resources/mapper/模块 xml文件</span>
        <span id="LC12" class="line" lang="plaintext">│  └─main/java/io/hmit/api/modules/模块</span>
        <span id="LC13" class="line" lang="plaintext">│                             ├─controller</span>
        <span id="LC14" class="line" lang="plaintext">│                             ├─dao</span>
        <span id="LC15" class="line" lang="plaintext">│                             └─service</span>
        <span id="LC16" class="line" lang="plaintext">├─common 模块</span>
        <span id="LC17" class="line" lang="plaintext">│  └─main/java/io/hmit/modules/模块</span>
        <span id="LC18" class="line" lang="plaintext">│                         ├─dto</span>
        <span id="LC19" class="line" lang="plaintext">│                         ├─entity</span>
        <span id="LC20" class="line" lang="plaintext">│                         └─excel</span>
        <span id="LC21" class="line" lang="plaintext">│
        <span id="LC22" class="line" lang="plaintext">├─vue/views/modules/模块 vue页面代码</span></code>
        </pre>
    </div>
</div>
</body>
</html>