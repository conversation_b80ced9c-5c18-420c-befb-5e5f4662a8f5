server:
  port: 8082
  servlet:
    context-path: /hmit-generator

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    #MySQL配置
    driverClassName: org.postgresql.Driver
#    driverClassName: com.mysql.jdbc.Driver
    url: ******************************************************************************, sys_catalog
    username: hmit_user
    password: HmitUser@2022
    #oracle配置
#    driverClassName: oracle.jdbc.OracleDriver
#    url: ***************************************
#    username: hmit_security
#    password: 123456
    #SQLServer配置
#    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
#    url: ********************************************************************
#    username: sa
#    password: 123456
    #PostgreSQL配置
#    driverClassName: org.postgresql.Driver
#    url: ********************************************************
#    username: postgres
#    password: 123456
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  resources:
    static-locations: classpath:/static/,classpath:/views/


mybatis:
  mapperLocations: classpath:mapper/**/*.xml


pagehelper:
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql


#指定数据库，可选值有【mysql、oracle、sqlserver、postgresql】
hmit:
  database: postgresql
