package ${package}.api.modules.${moduleName}.service;

import ${package}.common.service.CrudService;
import ${package}.modules.${moduleName}.dto.${className}DTO;
import ${package}.modules.${moduleName}.entity.${className}Entity;

/**
 * <h1>${comments} Service</h1>
 *
 * <AUTHOR> ${email}
 * @since ${version} ${date}
 */
public interface ${className}Service extends CrudService<${className}Entity, ${className}DTO> {

}