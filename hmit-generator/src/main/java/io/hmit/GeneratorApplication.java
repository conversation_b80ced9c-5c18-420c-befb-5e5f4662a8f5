package io.hmit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.core.env.Environment;

/**
 * <AUTHOR>
 */
@SpringBootApplication
public class GeneratorApplication {

    private static final Logger LOG = LoggerFactory.getLogger(GeneratorApplication.class);

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(GeneratorApplication.class);
        Environment env = app.run(args).getEnvironment();
        LOG.info("启动成功！！");
        LOG.info("Generator地址: \thttp://127.0.0.1:{}/hmit-generator/", env.getProperty("server.port"));
    }
}
