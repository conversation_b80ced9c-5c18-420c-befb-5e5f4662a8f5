# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Spring Boot-based enterprise application system built with a modular architecture. The project consists of multiple Maven modules providing admin backend functionality, API services, and shared common utilities.

### Module Structure
- **hmit-admin**: Main administrative backend application (port 8080)
- **hmit-api**: API service application (port 8081) 
- **hmit-common**: Shared utilities, entities, and base services
- **hmit-dynamic-datasource**: Dynamic datasource configuration
- **hmit-generator**: Code generation tools and templates

### Key Technologies
- Spring Boot 2.1.18
- MyBatis Plus 3.2.0 for database operations
- Apache Shiro for security
- Redis for caching
- Druid for database connection pooling
- Multiple database support (MySQL, Oracle, PostgreSQL, SQL Server)
- Swagger for API documentation
- Docker containerization support

## Common Development Commands

### Building the Project
```bash
# Build all modules
mvn clean install

# Build specific module
mvn clean install -pl hmit-admin
mvn clean install -pl hmit-api

# Skip tests during build
mvn clean install -DskipTests
```

### Running Applications
```bash
# Run admin application
cd hmit-admin
mvn spring-boot:run

# Run API application  
cd hmit-api
mvn spring-boot:run

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### Docker Operations
```bash
# Build and run using Docker Compose
docker-compose up -d

# Build individual images
docker build -t hmit/hmit-admin hmit-admin/
docker build -t hmit/hmit-api hmit-api/
```

### Code Generation
The project includes a code generator module for creating CRUD operations:
1. Modify `hmit-generator/src/main/resources/generator.properties`
2. Update `moduleName` and `tablePrefix` values
3. Run generator to create controller, service, dao, and entity classes

## Application Architecture

### Modular Business Organization
The application is organized into functional modules under `hmit-admin/src/main/java/io/hmit/modules/`:

- **backend**: Backend management and statistics
- **cockpit**: Dashboard and monitoring components  
- **datasource**: Database source management and SQL logging
- **demo**: Demo/example functionality including file handling and spider crawling
- **issue**: Issue tracking system
- **job**: Scheduled job management using Quartz
- **log**: System logging (error, login, operation logs)
- **message**: Email and SMS messaging services
- **oss**: Object storage services (Aliyun, Qcloud, Qiniu, Local, FastDFS)
- **qh**: Queue management and hall operations
- **qw**: Content management (news, policies, honors)
- **security**: Authentication and authorization (Shiro, OAuth2)
- **sys**: Core system management (users, roles, departments, menus)

### Standard Layer Architecture
Each module follows consistent layering:
- **controller**: REST API endpoints
- **service**: Business logic layer with interface and implementation
- **dao**: Data access layer using MyBatis Plus
- **entity**: Database entities
- **dto**: Data transfer objects

### Database Configuration
- Supports multiple databases via profiles (dev, test, prod)
- Uses Druid connection pooling
- MyBatis Plus for ORM with automatic CRUD generation
- Dynamic datasource support for multi-database scenarios

### Security Features
- Shiro-based authentication and authorization
- OAuth2 token-based API access
- Login attempt locking (configurable via `hmit.login.lock`)
- SM2/SM4 national cryptographic algorithms integrated

### External Integrations
- 浙政钉 (ZheZhengDing) integration for employee authentication
- Multiple cloud storage providers
- Email and SMS services
- Redis caching layer

## Configuration

### Profiles
- `dev`: Development environment (default)
- `test`: Testing environment  
- `prod`: Production environment
- `vb`: Special variant environment

### Key Configuration Files
- `application.yml`: Main configuration
- `application-{profile}.yml`: Environment-specific settings
- `logback-spring.xml`: Logging configuration
- `docker-compose.yml`: Container orchestration

### Important Settings
- Redis configuration for caching (`hmit.redis.open`)
- Login lock settings (`hmit.login.lock`)
- Database connections per environment
- File upload limits (100MB default)

## Development Notes

### Common Patterns
- All services extend `BaseService<T>` for standard CRUD operations
- Controllers use standard REST patterns with consistent response format
- Entities extend `BaseEntity` for common fields (create/update timestamps)
- DAO interfaces extend MyBatis Plus `BaseMapper<T>`

### Code Generation Workflow
1. Ensure database table exists
2. Configure `generator.properties` with correct module and table prefix
3. Use hmit-generator to create complete CRUD scaffold
4. Customize generated code as needed

### Testing
- Test classes located in `src/test/java`
- Integration tests configured with test application.yml
- Use appropriate profiles for testing environments

## Related Projects

### Frontend Application
- **Location**: `/mnt/d/projects/yuyao-workbenches-vue`
- **Deployment**: Docker containerized
- **Description**: Vue.js frontend application for the HMIT management system

## Claude Guidance

### Git Operations
- 当我让你进行git提交时，仅提交本次修改的文件，提交信息写一行就够了
- 遵循简洁的提交信息格式，如: "feat: 功能描述" 或 "fix: 修复内容"

### Environment
- 前端代码的位置是 ../yuyao-workbenches-vue
- 运行在WSL环境中，有时候可能需要转换路径

## 外部系统集成和ID映射关系

### 杭州智慧系统集成
本系统与杭州智慧外部系统深度集成，存在内外部ID映射关系，开发时必须注意转换逻辑：

#### 部门ID映射
- **内部系统**: `sys_dept.id` (Long类型，自增主键)
- **杭州智慧**: `sys_dept.code` (String类型，杭州智慧系统的部门标识)
- **转换方法**: `sysDeptService.get(deptId).getCode()` 获取杭州智慧部门ID
- **应用场景**: 
  - 用户管理：创建/更新用户时需要传递 `code` 给杭州智慧
  - 统计数据：所有统计表(hall_statistics_dept等)存储的是杭州智慧的部门ID
  - 窗口管理：创建窗口时需要转换部门ID

#### 用户ID映射
- **内部系统**: `sys_user.id` (Long类型，自增主键)
- **杭州智慧**: `sys_user.work_no` (String类型，工号)
- **转换方法**: 直接使用 `workNo` 字段与杭州智慧交互
- **应用场景**: 用户CRUD操作时与杭州智慧系统同步

#### 窗口ID映射
- **内部系统**: `yy_windows.id` (Long类型，自增主键)
- **杭州智慧**: `yy_windows.window_unique_num` (Integer类型，杭州智慧窗口ID)
- **转换方法**: `windowsEntity.getWindowUniqueNum().toString()` 获取杭州智慧窗口ID
- **应用场景**: 统计数据查询时需要转换窗口ID

#### 业务ID映射
- **内部系统**: `yy_business.id` (Long类型，自增主键)
- **杭州智慧**: `yy_business.business_code` (String类型，杭州智慧业务编码)
- **转换方法**: `businessEntity.getBusinessCode()` 获取杭州智慧业务编码
- **应用场景**: 业务统计和队列管理

### 统计数据同步机制
- **定时任务**: `HallStatisticsTask` 每5分钟同步一次统计数据
- **数据来源**: 杭州智慧接口 `${HZZH.STATISTIC_IP}/smartqueue/*`
- **存储规则**: 统计表中的外键字段全部为String类型，存储杭州智慧的ID，不是内部系统ID
- **统计表映射**:
  - `yy_hall_statistics_dept.department_id` (String) ← 杭州智慧部门ID
  - `yy_hall_statistics_windows.department_id` (String) ← 杭州智慧部门ID  
  - `yy_hall_statistics_windows.window_id` (String) ← 杭州智慧窗口ID
  - `yy_hall_statistics_business.department_id` (String) ← 杭州智慧部门ID
  - `yy_hall_statistics_business.window_id` (String) ← 杭州智慧窗口ID
  - `yy_hall_statistics_business.business_id` (String) ← 杭州智慧业务ID
- **查询转换**: 
  ```java
  // 示例：根据内部部门ID查询统计数据
  LambdaQueryWrapper<SysDeptEntity> eq = new QueryWrapper<SysDeptEntity>()
      .lambda().eq(SysDeptEntity::getId, departmentId);
  SysDeptEntity sysDeptEntity = sysDeptDao.selectOne(eq);
  String hzzhDeptId = sysDeptEntity.getCode(); // 转换为杭州智慧部门ID
  ```

### 开发注意事项
1. **统计数据接口**: 当需要根据当前用户部门筛选数据时，必须先转换ID
2. **前端传参**: 前端传递的是内部系统ID，后端需要转换后查询统计数据
3. **双向同步**: 用户、部门、窗口、业务的增删改都需要同步到杭州智慧系统
4. **错误处理**: 杭州智慧接口调用失败时需要适当的降级处理