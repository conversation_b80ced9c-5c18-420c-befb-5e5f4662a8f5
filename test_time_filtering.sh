#!/bin/bash

# 测试时间筛选功能的curl脚本
BASE_URL="http://localhost:18000/hmit-admin/backend/earlywarning/page"

echo "=== 测试预警信息时间筛选功能 ==="

# 测试1: 基本查询（无时间筛选）
echo "测试1: 基本查询"
curl -s "${BASE_URL}?warningType=4&page=1&limit=5" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Content-Type: application/json' | jq '.data.total'

echo ""

# 测试2: seTime 筛选
echo "测试2: seTime 筛选 (2024-01-01)"
curl -s "${BASE_URL}?warningType=4&page=1&limit=5&seTime=2024-01-01" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Content-Type: application/json' | jq '.data.total'

echo ""

# 测试3: createTime 筛选
echo "测试3: createTime 筛选 (2024-12-25)"
curl -s "${BASE_URL}?warningType=4&page=1&limit=5&createTime=2024-12-25" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Content-Type: application/json' | jq '.data.total'

echo ""

# 测试4: appTime 筛选
echo "测试4: appTime 筛选 (2024-12-25)"
curl -s "${BASE_URL}?warningType=1&page=1&limit=5&appTime=2024-12-25" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Content-Type: application/json' | jq '.data.total'

echo ""

# 测试5: 组合筛选
echo "测试5: 组合筛选 (warningType=2, createTime=2024-12-25)"
curl -s "${BASE_URL}?warningType=2&page=1&limit=5&createTime=2024-12-25" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Content-Type: application/json' | jq '.data.total'

echo ""

# 测试6: 完整响应示例
echo "测试6: 完整响应示例 (warningType=4, limit=2)"
curl -s "${BASE_URL}?warningType=4&page=1&limit=2" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Content-Type: application/json' | jq '.'

echo ""
echo "=== 测试完成 ==="
