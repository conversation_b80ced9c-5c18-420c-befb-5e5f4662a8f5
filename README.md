# 基础框架

### 基础说明
对Admin和API的权限做了整合。

### 浙政钉授权 
[服务端]根据员工 Code 查询详情
/mozi/employee/getEmployeeByCode

[服务端]根据手机号码获取人员编码
/mozi/employee/get_by_mobile

[服务端]批量根据手机号获取employeeCode
/mozi/employee/get_by_mobiles

[客户端]获取登录授权码 (无需)
getAuthCode

### 代码生成器
生成代码前注意替换```hmit-generatir```模块下```generator.properties```中的 moduleName的 
```demo``` 和 tablePrefix 的 ```tb_```值为对应内容。

### 国标商密
加密组件配置在 common 包下，默认集成了 SM2 和 SM4，使用 SMUtil 即可。
- 修改依赖，如果需要在其他系统中集成，后端代码可直接复制包 ```io.hmit.common.utils.sm```，
并在 pom.xml 下添加如下依赖。 前端代码请参考前端框架。
```
<!-- 国密 -->
<dependency>
    <groupId>org.bouncycastle</groupId>
    <artifactId>bcprov-ext-jdk15to18</artifactId>
    <version>1.71</version>
</dependency>
```

- 修改完后对 hmit-admin 模块下的 qcloud 依赖，把包```bcprov-jdk15on```排除（相同包下同类名称导致冲突无法使用）。
```
<dependency>
    <groupId>com.qcloud</groupId>
    <artifactId>cos_api</artifactId>
    <version>${qcloud.cos.version}</version>
    <exclusions>
        <exclusion>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </exclusion>
        <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

- 具体加密部分需要修改的，可以直接使用全局搜索查找 ```/* SM 登录加密 */``` ，即可查询到需要修改的内容。


### 登录锁定
默认登录账号密码错误 5 次对账号锁定 30 分钟。具体时间和次数可以通过设置 UserLockServiceImpl 类来修改，
在 admin 模块的 application.yml 配置文件中的 ```hmit.login.lock``` 设置项可以选择开启或关闭。

**需要注意的是，如果设置锁定时间大于 30 分钟，需要修改 servlet 的 session 超时时间。**

### 错误
若出现 ```NoSuchMessageException: No message found under code'XXXX' for locale ……```
请检查 ```ErrorCode.java```类是否新增了错误码但未在 i18 国际化配置中新增对应错误提示。

_此错误提示非致命性错误。_